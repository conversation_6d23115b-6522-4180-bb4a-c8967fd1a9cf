<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.weibo.api</groupId>
		<artifactId>captain</artifactId>
		<version>0.39-SNAPSHOT</version>
	</parent>

	<groupId>com.weibo.api.captain</groupId>
	<artifactId>captain-assigner</artifactId>
	<name>Archetype - captain-assigner</name>
	<url>http://maven.apache.org</url>
	<dependencies>
		<dependency>
			<groupId>com.weibo.api</groupId>
			<artifactId>weibo-api-core</artifactId>
			<version>5.74</version>
		</dependency>
		<dependency>
			<groupId>com.weibo.api.captain</groupId>
			<artifactId>captain-common</artifactId>
			<version>0.39-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>org.yaml</groupId>
			<artifactId>snakeyaml</artifactId>
			<version>1.17</version>
		</dependency>
		<dependency>
			<groupId>com.esotericsoftware.yamlbeans</groupId>
			<artifactId>yamlbeans</artifactId>
			<version>1.09</version>
		</dependency>
		<dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.2.4</version>
        </dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>3.6.0</version>
		</dependency>
	</dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
