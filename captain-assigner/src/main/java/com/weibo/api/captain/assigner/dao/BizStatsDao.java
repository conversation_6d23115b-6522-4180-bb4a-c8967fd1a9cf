/**  
 * Project Name:captain-assigner  
 * File Name:BizStatsDao.java  
 * Package Name:com.weibo.api.captain.assigner.dao  
 * Date:2016年7月18日下午10:15:00  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.assigner.dao;

import java.util.List;

import com.weibo.api.captain.assigner.stats.BizSlaStats;
import com.weibo.api.captain.assigner.stats.BizStatsCompare;

/**  
 * <pre>  
 * ClassName:BizStatsDao 
 * 
 * description here!
 * <pre/>   
 * Date:     2016年7月18日 下午10:15:00 <br/>  
 * <AUTHOR>  
 * @version    
 * @see        
 */
public interface BizStatsDao {
    boolean addBizStatsCompare(BizStatsCompare bizStatsCompare);
    boolean updateBizStatsCompare(BizStatsCompare bizStatsCompare);
    List<BizStatsCompare> lookup(String group);
    BizStatsCompare lookup(String group,String biz);
}
  
	