/**  
 * Project Name:captain-assigner  
 * File Name:BizStatsDaoImpl.java  
 * Package Name:com.weibo.api.captain.assigner.dao  
 * Date:2016年7月18日下午10:15:17  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.assigner.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.RowMapper;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.weibo.api.captain.assigner.stats.BizStatsCompare;
import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.data.dao.util.JdbcTemplate;

/**  
 * <pre>  
 * ClassName:BizStatsDaoImpl 
 * 
 * description here!
 * <pre/>   
 * Date:     2016年7月18日 下午10:15:17 <br/>  
 * <AUTHOR>  
 * @version     
 * @see        
 */
public class BizStatsDaoImpl implements BizStatsDao{
    private JdbcTemplate jdbcTemplate;
    private String insertSql =
            "insert into ClusterManager.bizStatsAlarm (groupName,biz,avgTime,hitPercent,readTps,writeTps,slowRequest,createTime) values (?,?,?,?,?,?,?,?)";
    private String selectSql = "select * from ClusterManager.bizStatsAlarm where groupName=? and biz=?";
    private String updateSql = "update ClusterManager.bizStatsAlarm set avgTime=?,hitPercent=?,readTps=?,writeTps=?,slowRequest=?,createTime=? where groupName = ? and biz = ?";
    private String selectSqlByGroup = "select * from ClusterManager.bizStatsAlarm where groupName=? ";


    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public boolean addBizStatsCompare(BizStatsCompare bizStatsCompare) {
        try {
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            String avgTime = gson.toJson(bizStatsCompare.getAvgTime());
            String hitPercent = gson.toJson(bizStatsCompare.getHitPercent());
            String readTps = gson.toJson(bizStatsCompare.getReadTps());
            String writeTps = gson.toJson(bizStatsCompare.getWriteTps());
            String slowRequest = gson.toJson(bizStatsCompare.getSlowRequest());
            int result = jdbcTemplate.update(insertSql, new Object[] {bizStatsCompare.getGroup(),bizStatsCompare.getBiz(), avgTime, hitPercent,readTps,writeTps,slowRequest,bizStatsCompare.getCreateTime()});
            return result > 0; 
        } catch (DataAccessException e) {
            ApiLogger.error("bizStatsDaoImpl add biz stats error:group="+bizStatsCompare.getGroup()+",biz="+bizStatsCompare.getBiz());
            throw e;
        }
    }

    @Override
    public boolean updateBizStatsCompare(BizStatsCompare bizStatsCompare) {
        try {
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            String avgTime = gson.toJson(bizStatsCompare.getAvgTime());
            String hitPercent = gson.toJson(bizStatsCompare.getHitPercent());
            String readTps = gson.toJson(bizStatsCompare.getReadTps());
            String writeTps = gson.toJson(bizStatsCompare.getWriteTps());
            String slowRequest = gson.toJson(bizStatsCompare.getSlowRequest());
            int result = jdbcTemplate.update(updateSql, new Object[] {avgTime, hitPercent,readTps,writeTps,slowRequest,bizStatsCompare.getCreateTime(),bizStatsCompare.getGroup(),bizStatsCompare.getBiz()});
            return result > 0; 
        } catch (DataAccessException e) {
            ApiLogger.error("bizStatsDaoImpl update biz stats error:group="+bizStatsCompare.getGroup()+",biz="+bizStatsCompare.getBiz());
            throw e;
        }
    }

    @Override
    public  List<BizStatsCompare> lookup(String group) {
        try {
            List<BizStatsCompare> list = jdbcTemplate.query(selectSqlByGroup, new Object[]{group}, new BizStatsRowMapper());
            return list;
        } catch (DataAccessException e) {
            ApiLogger.error("proxyStatsDapImpl get resource error:group="+group, e);
            return null;
        }
    }

    @Override
    public BizStatsCompare lookup(String group,String biz) {
        try {
            List<BizStatsCompare> list = jdbcTemplate.query(selectSql, new Object[]{group,biz}, new BizStatsRowMapper());
            if (list == null || list.isEmpty()) {
                return null;
            }
            return list.get(0);
        } catch (DataAccessException e) {
            ApiLogger.error("proxyStatsDapImpl get resource error:group="+group, e);
            return null;
        }
    }
    
    private class BizStatsRowMapper implements RowMapper<BizStatsCompare> {

        @Override
        public BizStatsCompare mapRow(ResultSet rs, int rowNum) throws SQLException {
            BizStatsCompare bizSlaStats = new BizStatsCompare();
            bizSlaStats.setGroup(rs.getString("groupName"));
            bizSlaStats.setBiz(rs.getString("biz"));
            String avgTime = rs.getString("avgTime");
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            List<Float> avgTimeSet = gson.fromJson(avgTime, new TypeToken<ArrayList<Float>>() {}.getType());
            List<Float> hitPercent = gson.fromJson(rs.getString("hitPercent"),new TypeToken<ArrayList<Float>>() {}.getType());
            List<Long> readTps = gson.fromJson(rs.getString("readTps"), new TypeToken<ArrayList<Long>>() {}.getType());
            List<Long> writeTps = gson.fromJson(rs.getString("writeTps"), new TypeToken<ArrayList<Long>>() {}.getType());
            List<Integer> slowRequest = gson.fromJson(rs.getString("slowRequest"), new TypeToken<ArrayList<Integer>>() {}.getType());
            bizSlaStats.setAvgTime(avgTimeSet);
            bizSlaStats.setHitPercent(hitPercent);
            bizSlaStats.setReadTps(readTps);
            bizSlaStats.setWriteTps(writeTps);
            bizSlaStats.setSlowRequest(slowRequest);
            bizSlaStats.setCreateTime(rs.getString("createTime"));
            return bizSlaStats;
        }
        
    }
}
  
	