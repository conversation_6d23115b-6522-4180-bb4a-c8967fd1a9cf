/**
 * Project Name:captain-assigner File Name:ConfigRecordingDao1.java Package
 * Name:com.weibo.api.captain.assigner.dao Date:2016年7月13日下午7:10:32 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 *
 */

package com.weibo.api.captain.assigner.dao;

import java.util.Set;

import com.weibo.api.captain.assigner.model.ConfigRecording;

/**
 * <pre> ClassName:ConfigRecordingDao1
 *
 * description here! <pre/> Date: 2016年7月13日 下午7:10:32 <br/>
 *
 * <AUTHOR>
 * @version
 * @see
 */
public interface ConfigRecordingDao {
    boolean addConfigRecording(ConfigRecording configRecording);
    public boolean deleteConfig(String group,String biz);
    Set<String> lookupAllGroups();
}

