/**
 * Project Name:captain-assigner File Name:ConfigRecordingDaoImpl1.java Package
 * Name:com.weibo.api.captain.assigner.dao Date:2016年7月13日下午7:11:21 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 *
 */

package com.weibo.api.captain.assigner.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.RowMapper;

import com.weibo.api.captain.assigner.model.ConfigRecording;
import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.data.dao.util.JdbcTemplate;

/**
 * <pre> ClassName:ConfigRecordingDaoImpl1
 *
 * description here! <pre/> Date: 2016年7月13日 下午7:11:21 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class ConfigRecordingDaoImpl implements ConfigRecordingDao {
    private String selectSql = "select distinct groupName from configRecord";
    private String deleteSql = "delete from captain.configRecord where groupName = ? and biz = ?";
    private JdbcTemplate jdbcTemplate;

    @Override
    public boolean addConfigRecording(ConfigRecording configRecording) {
        int result = jdbcTemplate.update("insert into configRecord(groupName,biz,sign,content, createTime) values(?,?,?,?,?)",
                new Object[] {configRecording.getGroup(),configRecording.getBiz(), configRecording.getSign(), configRecording.getContent(), configRecording.getCreateTime()});
        return result > 0;
    }

    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public Set<String> lookupAllGroups() {
        try {
            List<String> groupList = jdbcTemplate.query(selectSql, new GroupRowMapper());
            Set <String> result = new HashSet<String>(groupList);
            return result;
        } catch (RuntimeException ex) {
            ApiLogger.error("configRecordDao lookupAllGroups error", ex);
            return null;
        }
    }

    private class GroupRowMapper implements RowMapper<String> {

        @Override
        public String mapRow(ResultSet rs, int rowNum) throws SQLException {
            String result = rs.getString("groupName");
            return result;
        }
    }
    public boolean deleteConfig(String group, String biz) {
        try {
            int result = jdbcTemplate.update(deleteSql, new Object[] {group,biz});
            return result > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("configRecordDao delete error,group =" + group +",biz="+biz,e);
            throw e;
        }
    }
}

