/**
 * Project Name:captain-assigner  
 * File Name:GenerateId.java  
 * Package Name:com.weibo.api.captain.assigner.dao  
 * Date:2016年9月7日下午5:04:03  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *
 */

package com.weibo.api.captain.assigner.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;

import com.weibo.api.captain.assigner.model.BaseInfo;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.data.dao.util.JdbcTemplate;

/**
 * <pre>  
 * ClassName:GenerateId 
 *
 * description here!
 * <pre/>   
 * Date:     2016年9月7日 下午5:04:03 <br/>  
 * <AUTHOR>
 * @version
 * @since    JDK 1.8  
 * @see
 */
public class GenerateId implements GenerateIdDao{
    private final static String insertSql = "insert into captain.generateId (type,biz) values (?,?)";
    private final static String countSql = "select * from captain.generateId";
    private final static String selectSql = "select id from captain.generateId where type = ? and biz = ?";
    private final static String selectBaseInfo = "select * from captain.generateId where id = ?";
    private final static String selectBaseInfoall = "select * from captain.generateId where id in(:param)";
    private static String deleteSql = "delete from captain.generateId where id = ?";
    private JdbcTemplate jdbcTemplate;

    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public int generateId(final String type, final String biz) {
        try {
            KeyHolder keyHolder = new GeneratedKeyHolder();
            int id = 0;
            jdbcTemplate.update(new PreparedStatementCreator() {
                public PreparedStatement createPreparedStatement(Connection con){
                    try {
                        PreparedStatement ps = con.prepareStatement(insertSql, PreparedStatement.RETURN_GENERATED_KEYS);
                        ps.setString(1, type);
                        ps.setString(2, biz);
                        return ps;
                    } catch (Exception e) {
                        ApiLogger.error("generateId error",e);
                    }
                    return null;

                }
            }, keyHolder);

            id = keyHolder.getKey().intValue();
            return id;
        } catch (DataAccessException e) {
            ApiLogger.error("generateId resource error",e);
            throw e;
        }
    }

    @Override
    public int queryTotalCount() {
        try {
            List<BaseInfo> list = jdbcTemplate.query(countSql, new Object[]{}, new BaseInfoRowMapper());
            if (list == null || list.isEmpty()) {
                return 0;
            }
            return list.size();
        } catch (DataAccessException e) {
            ApiLogger.error("GenerateId getBaseInfo error",e );
            throw e;
        }
    }

    @Override
    public List<BaseInfo> queryAll() {
        try {
            List<BaseInfo> list = jdbcTemplate.query(countSql, new Object[]{}, new BaseInfoRowMapper());
            if (list == null || list.isEmpty()) {
                return Collections.emptyList();
            }
            return list;
        } catch (DataAccessException e) {
            ApiLogger.error("GenerateId getBaseInfo error",e );
            throw e;
        }
    }


    @Override
    public boolean isExist(String type, String biz) {
        try {
            List<Integer> list = jdbcTemplate.query(selectSql, new Object[] {type,biz}, new GenerateIdRowMapper());
            if (list == null || list.isEmpty()) {
                return false;
            }
            return list.get(0) > 0;
        } catch (DataAccessException e) {
            ApiLogger.warn("GenerateId isExist error:type=" + type + ", biz=" + biz, e);
            throw e;
        }
    }

    private class GenerateIdRowMapper implements RowMapper<Integer> {
        @Override
        public Integer mapRow(ResultSet rs, int rowNum) throws SQLException {
            try {
                return rs.getInt("id");
            } catch (Exception ex) {
                ApiLogger.error("GenerateId RowMapper error", ex);
                return null;
            }
        }
    }

    @Override
    public BaseInfo getBaseInfo(int id) {
        try {
            List<BaseInfo> list = jdbcTemplate.query(selectBaseInfo, new Object[]{id}, new BaseInfoRowMapper());
            if (list == null || list.isEmpty()) {
                return null;
            }
            return list.get(0);
        } catch (DataAccessException e) {
            ApiLogger.error("GenerateId getBaseInfo error",e );
            throw e;
        }
    }


    public List<BaseInfo> getBaseInfolist(List ids) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("param", ids);
        NamedParameterJdbcTemplate jdbc = new NamedParameterJdbcTemplate(jdbcTemplate.getDataSource());
        try {
            List<BaseInfo> list = jdbc.query(selectBaseInfoall, paramMap , new BaseInfoRowMapper());
            return list;
        } catch (DataAccessException e) {
            ApiLogger.error("GenerateId getBaseInfo error",e );
            throw e;
        }
    }

    public boolean deleteId(int id) {
        try {
            int result = jdbcTemplate.update(deleteSql, new Object[] {id});
            return result > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("generateId delete error,id =" + id,e);
            throw e;
        }
    }

    private class BaseInfoRowMapper implements RowMapper<BaseInfo> {

        @Override
        public BaseInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
            try {
                BaseInfo baseInfo = new BaseInfo();
                baseInfo.setBiz(rs.getString("biz"));
                baseInfo.setType(rs.getString("type"));
                baseInfo.setId(rs.getInt("id"));
                return baseInfo;
            } catch (DataAccessException e) {
                ApiLogger.error("baseInfoRowMapper error",e);
                return null;
            }
        }

    }


}
  
	