/**
 * Project Name:captain-assigner  
 * File Name:GenerateId.java  
 * Package Name:com.weibo.api.captain.assigner.dao  
 * Date:2016年9月7日下午5:01:39  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *
 */

package com.weibo.api.captain.assigner.dao;

import com.weibo.api.captain.assigner.model.BaseInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>  
 * ClassName:GenerateId 
 *
 * description here!
 * <pre/>   
 * Date:     2016年9月7日 下午5:01:39 <br/>  
 * <AUTHOR>
 * @version
 * @since    JDK 1.8  
 * @see
 */
public interface GenerateIdDao {
    int generateId(String type,String biz);
    boolean isExist(String type,String biz);
    BaseInfo getBaseInfo(int id);
    public List<BaseInfo> getBaseInfolist(List ids);
    boolean deleteId(int id);
    int queryTotalCount();
    List<BaseInfo> queryAll();
}
  
