/**
 * Project Name:captain-assigner  
 * File Name:IdcInfoDao.java  
 * Package Name:com.weibo.api.captain.assigner.dao  
 * Date:2016年8月30日下午5:03:03  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *
 */

package com.weibo.api.captain.assigner.dao;

import java.util.List;

import com.weibo.api.captain.assigner.model.IdcInfo;

/**
 * <pre>  
 * ClassName:IdcInfoDao 
 *
 * 机房基本信息处理
 * <pre/>   
 * Date:     2016年8月30日 下午5:03:03 <br/>  
 * <AUTHOR>
 * @version
 * @since    JDK 1.8  
 * @see
 */
public interface IdcInfoDao {
    public boolean addIdcInfo(IdcInfo idcInfo);

    public boolean updateIdcInfo(IdcInfo idcInfo);

    /*
     * 根据提案id获取对应机房信息
     */
    public List<IdcInfo> getIdcInfos (int backupProId);
    public List<IdcInfo> getIdcInfoslist(List ids);
    public boolean deleteIdcInfo(IdcInfo idcInfo);
    public boolean deleteIdcs(List<Integer> backupProIds);
}
  
	