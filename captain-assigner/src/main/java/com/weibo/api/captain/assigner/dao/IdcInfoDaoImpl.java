/**
 * Project Name:captain-assigner File Name:IdcInfoDaoImpl.java Package
 * Name:com.weibo.api.captain.assigner.dao Date:2016年8月30日下午5:05:49 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 *
 */

package com.weibo.api.captain.assigner.dao;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;

import com.weibo.api.captain.assigner.model.IdcInfo;
import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.data.dao.util.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

/**
 * <pre> ClassName:IdcInfoDaoImpl
 *
 * description here! <pre/> Date: 2016年8月30日 下午5:05:49 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class IdcInfoDaoImpl implements IdcInfoDao {
    private JdbcTemplate jdbcTemplate;
    private static String insertSql =
            "insert into captain.idcInfo (backupProId,idc,groupName,masterCapacity,masterL1Capacity,masterL1Block,masterBandWidth,masterL1BandWidth, slave, slaveL1, masterL1, masterIps,slaveIps,masterL1Ips,slaveL1Ips,reuseIdc,state) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    private static String updateSql =
            "update captain.idcInfo set groupName = ?,masterCapacity = ?,masterL1Capacity = ?,masterL1Block = ?,masterBandWidth = ?, masterL1BandWidth = ?, slave = ?, slaveL1 = ? ,masterL1 = ? ,masterIps = ?, slaveIps = ?, masterL1Ips = ?, slaveL1Ips = ?, reuseIdc = ?, state = ? where backupProId = ? and idc = ?";
    private static String selectSql = "select * from captain.idcInfo where backupProId = ?";
    private static String selectSqlall = "select * from captain.idcInfo where backupProId in (:param)";
    private static String deleteSql = "delete from captain.idcInfo where backupProId = ? and idc = ?";
    private static String deleteAllIdcForId = "delete from captain.idcInfo where backupProId = ?";
    
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public boolean addIdcInfo(IdcInfo idcInfo) {
        try {
            int result = jdbcTemplate.update(insertSql,
                    new Object[] {idcInfo.getBackupProId(), idcInfo.getIdc(), idcInfo.getGroup(), idcInfo.getMasterCapacity(),
                            idcInfo.getMasterL1Capacity(), idcInfo.getMasterL1Block(), idcInfo.getMasterBandWidth(),
                            idcInfo.getMasterL1BandWidth(), idcInfo.getSlave(), idcInfo.getSlaveL1Idc(), idcInfo.getMasterL1Idc(), idcInfo.getMasterIps(),
                            idcInfo.getSlaveIps(), idcInfo.getMasterL1Ips(), idcInfo.getSlaveL1Ips(), idcInfo.getReuseIdc(), idcInfo.getState()});
            return result > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("idcInfoDaoImpl add error,backupProId =" + idcInfo.getBackupProId() + "," + idcInfo.getIdc(),e);
            throw e;
        }
    }

    @Override
    public boolean updateIdcInfo(IdcInfo idcInfo) {
        try {
            int result = jdbcTemplate.update(updateSql,
                    new Object[] {idcInfo.getGroup(), idcInfo.getMasterCapacity(), idcInfo.getMasterL1Capacity(),
                            idcInfo.getMasterL1Block(), idcInfo.getMasterBandWidth(), idcInfo.getMasterL1BandWidth(), idcInfo.getSlave(),
                            idcInfo.getSlaveL1Idc(),idcInfo.getMasterL1Idc(), idcInfo.getMasterIps(), idcInfo.getSlaveIps(), idcInfo.getMasterL1Ips(),
                            idcInfo.getSlaveL1Ips(), idcInfo.getReuseIdc(), idcInfo.getState(), idcInfo.getBackupProId(),
                            idcInfo.getIdc()});
            return result > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("idcInfoDaoImpl update error,backupProId =" + idcInfo.getBackupProId() + "," + idcInfo.getIdc(),e);
            throw e;
        }
    }

    @Override
    public List<IdcInfo> getIdcInfos(int backupProId) {
        try {
            List<IdcInfo> list = jdbcTemplate.query(selectSql, new Object[] {backupProId}, new IdcInfoRowMapper());
            return list;
        } catch (DataAccessException e) {
            ApiLogger.error("idcInfoDaoImpl get resource error:backupProId=" + backupProId, e);
            throw e;
        }
    }

    @Override
    public List<IdcInfo> getIdcInfoslist(List ids) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("param", ids);
        NamedParameterJdbcTemplate jdbc = new NamedParameterJdbcTemplate(jdbcTemplate.getDataSource());
        try {
            List<IdcInfo> list = jdbc.query(selectSqlall, paramMap, new IdcInfoRowMapper());
            return list;
        } catch (DataAccessException e) {
            ApiLogger.error("idcInfoDaoImpl get resource error:backupProId=" +ids , e);
            throw e;
        }
    }






    private class IdcInfoRowMapper implements RowMapper<IdcInfo> {
        @Override
        public IdcInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
            IdcInfo idcInfo = null;
            try {
                idcInfo = new IdcInfo();
                idcInfo.setBackupProId(rs.getInt("backupProId"));
                idcInfo.setIdc(rs.getString("idc"));
                idcInfo.setGroup(rs.getString("groupName"));
                idcInfo.setMasterCapacity(rs.getInt("masterCapacity"));
                idcInfo.setMasterL1Capacity(rs.getInt("masterL1Capacity"));
                idcInfo.setMasterL1Block(rs.getInt("masterL1Block"));
                idcInfo.setMasterBandWidth(rs.getInt("masterBandWidth"));
                idcInfo.setMasterL1BandWidth(rs.getInt("masterL1BandWidth"));
                idcInfo.setSlave(rs.getString("slave"));
                idcInfo.setSlaveL1Idc(rs.getString("slaveL1"));
                idcInfo.setMasterL1Idc(rs.getString("masterL1"));
                idcInfo.setMasterIps(rs.getString("masterIps"));
                idcInfo.setSlaveIps(rs.getString("slaveIps"));
                idcInfo.setMasterL1Ips(rs.getString("masterL1Ips"));
                idcInfo.setSlaveL1Ips(rs.getString("slaveL1Ips"));
                idcInfo.setReuseIdc(rs.getString("reuseIdc"));
                idcInfo.setState(rs.getInt("state"));
                return idcInfo;
            } catch (Exception ex) {
                ApiLogger.error("resourceDaoImpl RecordInfoRowMapper error", ex);
                return null;
            }
        }
    }

    @Override
    public boolean deleteIdcInfo(IdcInfo idcInfo) {
        try {
            int result = jdbcTemplate.update(deleteSql,
                    new Object[] {idcInfo.getBackupProId(),idcInfo.getIdc()});
            return result > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("idcInfoDaoImpl delete error,backupProId =" + idcInfo.getBackupProId() + "," + idcInfo.getIdc(),e);
            throw e;
        }
    }
    
    @Override
    public boolean deleteIdcs(final List<Integer>backupProIds) {
        try {
            int []res = jdbcTemplate.batchUpdate(deleteAllIdcForId, new BatchPreparedStatementSetter() {
                
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setInt(1, backupProIds.get(i));
                }
                
                @Override
                public int getBatchSize() {
                    return backupProIds.size();
                }
            }) ;
            
            return res.length > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("idcInfoDaoImpl delete error,backupProIds =" + backupProIds,e);
            throw e;
        }
    }
}

