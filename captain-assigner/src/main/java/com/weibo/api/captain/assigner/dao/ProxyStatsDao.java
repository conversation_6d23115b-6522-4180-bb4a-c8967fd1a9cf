/**
 * Project Name:captain-assigner File Name:ProxyStatsDao.java Package
 * Name:com.weibo.api.captain.assigner.dao Date:2016年7月18日下午8:49:50 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.dao;

import java.util.List;

import com.weibo.api.captain.assigner.stats.ProxyStats;

/**
 * <pre> ClassName:ProxyStatsDao
 * 
 * 数据库操作proxystats<pre/> Date: 2016年7月18日 下午8:49:50 <br/>
 * 
 * <AUTHOR>
 * @version
 * @see
 */
public interface ProxyStatsDao {
    boolean addProxyStats(ProxyStats proxyStats);
    
    boolean updateProxyStats(ProxyStats proxyStats);

    List<ProxyStats> lookUpProxyStats();
    
    ProxyStats lookUpProxyStats(String group);
}

