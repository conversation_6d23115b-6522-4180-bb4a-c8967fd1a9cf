/**
 * Project Name:captain-assigner File Name:ProxyStatsDaoImpl.java Package
 * Name:com.weibo.api.captain.assigner.dao Date:2016年7月18日下午8:52:26 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.RowMapper;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.weibo.api.captain.assigner.stats.ProxyStats;
import com.weibo.vintage.model.EndpointAddress;
import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.data.dao.util.JdbcTemplate;

/**
 * <pre> ClassName:ProxyStatsDaoImpl
 * 
 * 
 * <pre/> Date: 2016年7月18日 下午8:52:26 <br/>
 * 
 * <AUTHOR>
 * @version
 * @see
 */
public class ProxyStatsDaoImpl implements ProxyStatsDao {
    private JdbcTemplate jdbcTemplate;
    private String insertSql =
            "insert into ClusterManager.proxyStatsAlarm (groupName,unreachableNodes,nodesConsistence,abnormalNodes,signConsistence,createTime) values (?,?,?,?,?,?)";
    private String selectSql = "select * from ClusterManager.proxyStatsAlarm";
    private String selectSqlByGroup = "select * from ClusterManager.proxyStatsAlarm where groupName = ?";
    private String updateSql = "update ClusterManager.proxyStatsAlarm set unreachableNodes=?,nodesConsistence=?,abnormalNodes=?,signConsistence=?,createTime=? , groupName = ? where id=?";

    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public boolean addProxyStats(ProxyStats proxyStats) {
        try {
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            String unreachableNodes = gson.toJson(proxyStats.getUnreachableNodeAddress());
            String abnormalNodes = gson.toJson(proxyStats.getAbnormalNodeAddress());
            String signConsistence = gson.toJson(proxyStats.getSignConsistenceAddress());
            int result = jdbcTemplate.update(insertSql, new Object[] {proxyStats.getGroup(),unreachableNodes, proxyStats.isNodesConsistence(), abnormalNodes,signConsistence,proxyStats.getCreateTime()});
            return result > 0;
        } catch (RuntimeException e) {
            ApiLogger.error("proxyStatsDapImpl add proxy stats error:group="+proxyStats.getGroup()+",createTime="+proxyStats.getCreateTime(),e);
            throw e;
        }
    }

    @Override
    public List<ProxyStats> lookUpProxyStats() {
        List<ProxyStats> list = null;
        try {
            list = jdbcTemplate.query(selectSql, new ProxyStatsRowMapper());
            return list;
        } catch (DataAccessException e) {
            ApiLogger.error("proxyStatsDapImpl get resource error", e);
            return null;
        }
    }
    
    private class ProxyStatsRowMapper implements RowMapper<ProxyStats> {

        @Override
        public ProxyStats mapRow(ResultSet rs, int rowNum) throws SQLException {
              ProxyStats proxyStats = new ProxyStats();
              GsonBuilder gb = new GsonBuilder();
              Gson gson = gb.create();
              Set<EndpointAddress> unreachableNodes = gson.fromJson(rs.getString("unreachableNodes"), new TypeToken<Set<EndpointAddress>>() {}.getType());
              Set<EndpointAddress> abnormalNodes = gson.fromJson(rs.getString("abnormalNodes"), new TypeToken<Set<EndpointAddress>>() {}.getType());
              Set<EndpointAddress> signConsistence = gson.fromJson(rs.getString("signConsistence"), new TypeToken<Set<EndpointAddress>>() {}.getType());
              proxyStats.setGroup(rs.getString("groupName"));
              proxyStats.setUnreachableNodeAddress(unreachableNodes);
              proxyStats.setNodesConsistence(rs.getBoolean("nodesConsistence"));
              proxyStats.setAbnormalNodeAddress(abnormalNodes);
              proxyStats.setSignConsistenceAddress(signConsistence);
              proxyStats.setCreateTime(rs.getString("createTime"));
              proxyStats.setId(rs.getInt("id"));
              return proxyStats;
        }
        
    }

    @Override
    public ProxyStats lookUpProxyStats(String group) {
        try {
            List<ProxyStats> list = jdbcTemplate.query(selectSqlByGroup, new Object[]{group}, new ProxyStatsRowMapper());
            if (list == null || list.isEmpty()) {
                return null;
            }
            return list.get(0);
        } catch (DataAccessException e) {
            ApiLogger.error("proxyStatsDapImpl get resource error:group="+group, e);
            return null;
        }
    }

    @Override
    public boolean updateProxyStats(ProxyStats proxyStats) {
        try {
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            String unreachableNodes = gson.toJson(proxyStats.getUnreachableNodeAddress());
            String abnormalNodes = gson.toJson(proxyStats.getAbnormalNodeAddress());
            String signConsistence = gson.toJson(proxyStats.getSignConsistenceAddress());
            int result = jdbcTemplate.update(updateSql, new Object[] {unreachableNodes, proxyStats.isNodesConsistence(), abnormalNodes,signConsistence,proxyStats.getCreateTime(),proxyStats.getGroup(),proxyStats.getId()});
            return result > 0;
        } catch (RuntimeException e) {
            ApiLogger.error("proxyStatsDapImpl update proxy stats error:group="+proxyStats.getGroup()+",createTime="+proxyStats.getCreateTime(),e);
            throw e;
        }
    }

}

