/**
 * Project Name:captain-assigner  
 * File Name:ResourceAccessSettingDao.java  
 * Package Name:com.weibo.api.captain.assigner.dao  
 * Date:2016年8月30日下午4:43:40  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *
 */

package com.weibo.api.captain.assigner.dao;

import com.weibo.api.captain.assigner.model.ResourceAccessSetting;

import java.util.List;

/**
 * <pre>  
 * ClassName:ResourceAccessSettingDao 
 *
 * 资源访问设置信息
 * <pre/>   
 * Date:     2016年8月30日 下午4:43:40 <br/>  
 * <AUTHOR>
 * @version
 * @since    JDK 1.8  
 * @see
 */
public interface ResourceAccessSettingDao {
    public boolean addResourceAccessSetting(ResourceAccessSetting resourceAccessSetting);

    public boolean updateResourceAccessSetting(ResourceAccessSetting resourceAccessSetting);

    /*
     * 通过提案id获取对应提案的资源访问控制信息
     */
    public ResourceAccessSetting getResourceAccessSetting(int backupProId);
    public List<ResourceAccessSetting>getResourceAccessSettinglist(List ids);
    public boolean deleteRes(List<Integer>ids);
}
  
	