/**
 * Project Name:captain-assigner File Name:ResourceAccessSettingDaoImpl.java Package
 * Name:com.weibo.api.captain.assigner.dao Date:2016年8月30日下午4:47:32 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 *
 */

package com.weibo.api.captain.assigner.dao;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;

import com.weibo.api.captain.assigner.model.ResourceAccessSetting;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.data.dao.util.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

/**
 * <pre> ClassName:ResourceAccessSettingDaoImpl
 *
 * description here! <pre/> Date: 2016年8月30日 下午4:47:32 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class ResourceAccessSettingDaoImpl implements ResourceAccessSettingDao {
    private JdbcTemplate jdbcTemplate;
    private static String insertSql =
            "insert into captain.resourceAccessSetting (backupProId,hash,distribution,hashTag,saveTag,autoEjectHosts,timeout,resourceType,serverRetryTimeout,serverFailureLimit,partialReply,exptime,force_write_all,updateSlaveL1,local_affinity,flag) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    private static String updateSql =
            "update captain.resourceAccessSetting set hash=?,distribution=?,hashTag=?,saveTag=?,autoEjectHosts=?,timeout=?,resourceType=?,serverRetryTimeout=?,serverFailureLimit=?, partialReply=?,exptime=? ,force_write_all=?,updateSlaveL1=?,local_affinity=?,flag=? where backupProId = ?";

    private static String selectSql = "select * from captain.resourceAccessSetting where backupProId = ?";
    private static String selectSqlall = "select * from captain.resourceAccessSetting where backupProId in(:param)";
    private static String deleteSql = "delete from captain.resourceAccessSetting where backupProId = ?";
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public boolean addResourceAccessSetting(ResourceAccessSetting resourceAccessSetting) {
        try {
            int result = jdbcTemplate.update(insertSql,
                    new Object[] {resourceAccessSetting.getBackupProId(), resourceAccessSetting.getHash(),
                            resourceAccessSetting.getDistribution(), resourceAccessSetting.getHashTag(), resourceAccessSetting.getSaveTag(),
                            resourceAccessSetting.getAutoEjectHosts(), resourceAccessSetting.getTimeout(),
                            resourceAccessSetting.getResourceType(), resourceAccessSetting.getServerRetryTimeout(),
                            resourceAccessSetting.getServerFailureLimit(), resourceAccessSetting.getPartialReply(),
                            resourceAccessSetting.getExptime() , resourceAccessSetting.getForceWriteAll(),
                            resourceAccessSetting.getUpdateSlaveL1(),resourceAccessSetting.getlocalAffinity(),
                            resourceAccessSetting.getFlag()});
            return result > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("resourceAccessSettingDaoImpl add error,backupProId=" + resourceAccessSetting.getBackupProId(),e);
            throw e;
        }
    }

    @Override
    public boolean updateResourceAccessSetting(ResourceAccessSetting resourceAccessSetting) {
        try {
            int result = jdbcTemplate.update(updateSql,
                    new Object[] {resourceAccessSetting.getHash(), resourceAccessSetting.getDistribution(),
                            resourceAccessSetting.getHashTag(), resourceAccessSetting.getSaveTag(),
                            resourceAccessSetting.getAutoEjectHosts(), resourceAccessSetting.getTimeout(),
                            resourceAccessSetting.getResourceType(), resourceAccessSetting.getServerRetryTimeout(),
                            resourceAccessSetting.getServerFailureLimit(),resourceAccessSetting.getPartialReply(),
                            resourceAccessSetting.getExptime(),resourceAccessSetting.getForceWriteAll(),
                            resourceAccessSetting.getUpdateSlaveL1(),resourceAccessSetting.getlocalAffinity(),
                            resourceAccessSetting.getFlag(), resourceAccessSetting.getBackupProId()});
            return result >= 0;
        } catch (DataAccessException e) {
            ApiLogger.error("resourceAccessSettingDaoImpl update error,backUpProId =" +resourceAccessSetting.getBackupProId(),e);
            throw e;
        }
    }

    @Override
    public ResourceAccessSetting getResourceAccessSetting(int backupProId) {
        try {
            List<ResourceAccessSetting> list = jdbcTemplate.query(selectSql, new Object[] {backupProId}, new ResourceAccessSettingRowMapper());
            if (list == null || list.isEmpty()) {
                return null;
            }
            return list.get(0);
        } catch (DataAccessException e) {
            ApiLogger.error("resourceAccessSettingDaoImpl get resource error:backupProId=" + backupProId, e);
            throw e;
        }
    }

    public List<ResourceAccessSetting> getResourceAccessSettinglist(List ids) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("param", ids);
        NamedParameterJdbcTemplate jdbc = new NamedParameterJdbcTemplate(jdbcTemplate.getDataSource());
        try {
            List<ResourceAccessSetting> lists = jdbc.query(selectSqlall, paramMap , new ResourceAccessSettingRowMapper());
            return lists;
        } catch (DataAccessException e) {
            ApiLogger.error("resourceAccessSettingDaoImpl get resource error:backupProId=" + ids, e);
            throw e;
        }
    }

    public boolean deleteRes(final List<Integer>backupProIds) {
        try {
            int []res = jdbcTemplate.batchUpdate(deleteSql, new BatchPreparedStatementSetter() {
                
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setInt(1, backupProIds.get(i));
                }
                
                @Override
                public int getBatchSize() {
                   return backupProIds.size();
                }
            });
            return res.length > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("resourceAccessSettingDaoImpl delete error,backupProIds =" + backupProIds,e);
            throw e;
        }
    }

    private class ResourceAccessSettingRowMapper implements RowMapper<ResourceAccessSetting> {
        @Override
        public ResourceAccessSetting mapRow(ResultSet rs, int rowNum) throws SQLException {
            ResourceAccessSetting resourceAccessSetting = null;
            try {
                resourceAccessSetting = new ResourceAccessSetting();
                resourceAccessSetting.setBackupProId(rs.getInt("backupProId"));
                resourceAccessSetting.setAutoEjectHosts(rs.getInt("autoEjectHosts"));
                resourceAccessSetting.setDistribution(rs.getString("distribution"));
                resourceAccessSetting.setExptime(rs.getLong("exptime"));
                resourceAccessSetting.setForceWriteAll(rs.getInt("force_write_all"));
                resourceAccessSetting.setUpdateSlaveL1(rs.getInt("updateSlaveL1"));
                resourceAccessSetting.setlocalAffinity(rs.getInt("local_affinity"));
                resourceAccessSetting.setFlag(rs.getInt("flag"));
                resourceAccessSetting.setHash(rs.getString("hash"));
                resourceAccessSetting.setHashTag(rs.getString("hashTag"));
                resourceAccessSetting.setPartialReply(rs.getInt("partialReply"));
                resourceAccessSetting.setResourceType(rs.getInt("resourceType"));
                resourceAccessSetting.setSaveTag(rs.getInt("saveTag"));
                resourceAccessSetting.setServerFailureLimit(rs.getInt("serverFailureLimit"));
                resourceAccessSetting.setServerRetryTimeout(rs.getLong("serverRetryTimeout"));
                resourceAccessSetting.setTimeout(rs.getLong("timeout"));
                return resourceAccessSetting;
            } catch (Exception ex) {
                ApiLogger.error("resourceAccessSettingDaoImpl rowMapper error",ex);
                return null;
            }
        }

    }


}

