/**
 * Project Name:captain-assigner  
 * File Name:ResourceDao.java  
 * Package Name:com.weibo.api.captain.assigner.dao  
 * Date:2016年6月29日上午10:25:46  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *
 */

package com.weibo.api.captain.assigner.dao;

import java.util.List;

import com.weibo.api.captain.assigner.model.RecordInfo;

/**
 * <pre>  
 * ClassName:ResourceDao 
 *
 * description here!
 * <pre/>   
 * Date:     2016年6月29日 上午10:25:46 <br/>  
 * <AUTHOR>
 * @version
 * @since    JDK 1.8  
 * @see
 */
public interface ResourceDao {
    int addResource(RecordInfo recordInfo);
    boolean updateResource(RecordInfo recordInfo);
    RecordInfo getResource(int id);
    List<RecordInfo> getResource(boolean assigned);
    RecordInfo getLastResource(int id);
    public boolean deleteRecord(int id);
    List<RecordInfo> getResourceById(int id);
}
  
	