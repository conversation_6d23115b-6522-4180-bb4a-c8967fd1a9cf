/**
 * Project Name:captain-assigner File Name:ResourceDaoImpl.java Package
 * Name:com.weibo.api.captain.assigner.dao Date:2016年6月29日上午10:26:14 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 *
 */

package com.weibo.api.captain.assigner.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;

import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.assigner.util.Statistic;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.data.dao.util.JdbcTemplate;

/**
 * <pre> ClassName:ResourceDaoImpl
 *
 * description here! <pre/> Date: 2016年6月29日 上午10:26:14 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class ResourceDaoImpl implements ResourceDao {

    private JdbcTemplate jdbcTemplate;
    private static String insertSql =
            "insert into captain.resourceInfo (proId,hitPercent,readTps,writeTps,createTime,state,applyUser,assignUser,deployUser,returnUser) values (?,?,?,?,?,?,?,?,?,?)";
    private static String updateSql =
            "update captain.resourceInfo set hitPercent = ?, readTps = ?, writeTps = ?,state = ?, applyUser = ?, assignUser = ?, deployUser = ?, returnUser = ? where id = ?";

    private static String selectSql = "select * from captain.resourceInfo where proId = ? and state != 3 and state != 4";
    // 查看对应记录的所有历史状态
    private static String selectLastInfoSql =
            "select * from captain.resourceInfo where proId = ? and state = 3 order by updateTime desc,id desc";
    private static String selectAssigningSql =
            "select * from captain.resourceInfo where state in (:param) order by proId desc";
    private static String selectAssignedSql = "select * from captain.resourceInfo where state = 2 order by proId desc ";
    private static String deleteSql = "delete from captain.resourceInfo where proId = ?";
    private static String selectAllSqlById = "select * from captain.resourceInfo where proId = ?";

    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    // 插入数据直接返回id号
    @Override
    public int addResource(final RecordInfo recordInfo) {
        try {
            KeyHolder keyHolder = new GeneratedKeyHolder();
            int id = 0;
            jdbcTemplate.update(new PreparedStatementCreator() {
                public PreparedStatement createPreparedStatement(Connection con) {
                    try {
                        PreparedStatement ps = con.prepareStatement(insertSql, PreparedStatement.RETURN_GENERATED_KEYS);
                        ps.setInt(1, recordInfo.getProId());
                        ps.setFloat(2, recordInfo.getHitPercent());
                        ps.setInt(3, recordInfo.getReadTps());
                        ps.setInt(4, recordInfo.getWriteTps());
                        ps.setString(5, recordInfo.getCreateTime());
                        ps.setInt(6, recordInfo.getState());
                        ps.setString(7, recordInfo.getApplyUser());
                        ps.setString(8, recordInfo.getAssignUser());
                        ps.setString(9, recordInfo.getDeployUser());
                        ps.setString(10, recordInfo.getReturnUser());
                        return ps;
                    } catch (Exception e) {
                        ApiLogger.error("generateId error", e);
                    }
                    return null;

                }
            }, keyHolder);

            id = keyHolder.getKey().intValue();
            return id;
        } catch (DataAccessException e) {
            ApiLogger.error("resourceDaoImpl add resource error", e);
            throw e;
        }
    }

    @Override
    public boolean updateResource(RecordInfo recordInfo) {
        try {
            int result = jdbcTemplate.update(updateSql,
                    new Object[] {recordInfo.getHitPercent(), recordInfo.getReadTps(),
                            recordInfo.getWriteTps(), recordInfo.getState(), recordInfo.getApplyUser(), recordInfo.getAssignUser(),
                            recordInfo.getDeployUser(),recordInfo.getReturnUser(), recordInfo.getId()});
            return result > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("resourceDaoImpl update resource error", e);
            throw e;
        }
    }

    @Override
    public RecordInfo getResource(int id) {//只要能查询到一个，就说明存在（因为回滚，会有多条记录）。
        try {
            List<RecordInfo> list = jdbcTemplate.query(selectSql, new Object[] {id}, new RecordInfoRowMapper());
            if (list == null || list.isEmpty()) {//防止在查数据库时出现异常返回list[null]
                return null;
            }
            return list.get(0);
        } catch (DataAccessException e) {
            ApiLogger.warn("resourceDaoImpl get resource error:id=" + id, e);
            throw e;
        }
    }

    @Override
    public List<RecordInfo> getResource(boolean assigned) {
        List<RecordInfo> list = null;
        List<String> ids= new ArrayList<String>();
        ids.add("1");
        ids.add("0");
        ids.add("-1");
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("param", ids);
        NamedParameterJdbcTemplate jdbc = new NamedParameterJdbcTemplate(jdbcTemplate.getDataSource());
        try {
            if (assigned) {
                list = jdbcTemplate.query(selectAssignedSql, new RecordInfoRowMapper());
            } else {
                list = jdbc.query(selectAssigningSql,paramMap, new RecordInfoRowMapper());
            }
            return list;
        } catch (DataAccessException e) {
            ApiLogger.warn("resourceDaoImpl get resource error:assigned=" + assigned, e);
            return null;
        }
    }

    private class RecordInfoRowMapper implements RowMapper<RecordInfo> {
        @Override
        public RecordInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
            RecordInfo recordInfo = null;
            try {
                recordInfo = new RecordInfo();
                recordInfo.setId(rs.getInt("id"));
                recordInfo.setState(rs.getInt("state"));
                recordInfo.setHitPercent(rs.getFloat("hitPercent"));
                recordInfo.setReadTps(rs.getInt("readTps"));
                recordInfo.setWriteTps(rs.getInt("writeTps"));
                recordInfo.setApplyUser(rs.getString("applyUser"));
                recordInfo.setAssignUser(rs.getString("assignUser"));
                recordInfo.setDeployUser(rs.getString("deployUser"));
                recordInfo.setReturnUser(rs.getString("returnUser"));
                recordInfo.setCreateTime(rs.getString("createTime"));
                recordInfo.setUpdateTime(rs.getString("updateTime"));
                recordInfo.setProId(rs.getInt("proId"));
                return recordInfo;
            } catch (Exception ex) {
                ApiLogger.error("resourceDaoImpl RecordInfoRowMapper error", ex);
                return null;
            }
        }
    }

    @Override
    public RecordInfo getLastResource(int id) {
        try {
            List<RecordInfo> list = jdbcTemplate.query(selectLastInfoSql, new Object[] {id}, new RecordInfoRowMapper());
            if (list == null || list.isEmpty()) {
                return null;
            }

            return list.get(0);
        } catch (DataAccessException e) {
            ApiLogger.warn("resourceDaoImpl get resource error:id=" + id, e);
            throw e;
        }
    }
    
    @Override
    public List<RecordInfo> getResourceById(int id) {
        try {
            List<RecordInfo> list = jdbcTemplate.query(selectAllSqlById, new Object[] {id}, new RecordInfoRowMapper());
            if (list == null || list.isEmpty()) {
                return null;
            }
            return list;
        } catch (DataAccessException e) {
            ApiLogger.warn("resourceDaoImpl get resource error:id=" + id, e);
            throw e;
        }
    }
    
    @Override
    public boolean deleteRecord(int id) {
        try {
           int result = jdbcTemplate.update(deleteSql,
                    new Object[] {id});
           return result > 0;

        } catch (DataAccessException e) {
            ApiLogger.error("resourceDaoImpl delete error,proId =" + id,e);
            throw e;
        }
    }
}

