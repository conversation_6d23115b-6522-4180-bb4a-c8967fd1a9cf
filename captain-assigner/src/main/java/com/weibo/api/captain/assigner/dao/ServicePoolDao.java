/**  
 * Project Name:captain-assigner  
 * File Name:ServicePoolDao.java  
 * Package Name:com.weibo.api.captain.assigner.dao  
 * Date:2016年10月14日上午10:28:44  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.assigner.dao;

import java.util.List;
import com.weibo.api.captain.assigner.model.ServicePoolInfo;

/**  
 * <pre>  
 * ClassName:ServicePoolDao 
 * 
 * 服务池对应dao层，跟configserver数据一致性？
 * <pre/>   
 * Date:     2016年10月14日 上午10:28:44 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public interface ServicePoolDao {
    //增加服务池相关基本信息.
    boolean addServicePoolInfo(String type,int proxyState,String configAddr,String idcList);
    
    //获取所有服务类型
    List<ServicePoolInfo> getServiceType();
    
   // ServicePoolInfo getServicePoolInfo(String type);
    public List<ServicePoolInfo> getServicePoolInfolist(List groups);
    boolean updateServicePoolInfo(String type,int proxyState,String configAddr,String idcList);

    boolean delServicePoolInfo(String type);
}
  
	