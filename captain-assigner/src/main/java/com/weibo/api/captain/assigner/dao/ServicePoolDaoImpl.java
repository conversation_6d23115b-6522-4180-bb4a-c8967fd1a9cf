/**  
 * Project Name:captain-assigner  
 * File Name:ServicePoolDaoImpl.java  
 * Package Name:com.weibo.api.captain.assigner.dao  
 * Date:2016年10月14日上午10:35:33  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.assigner.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.RowMapper;
import com.weibo.api.captain.assigner.model.ServicePoolInfo;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.data.dao.util.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

/**  
 * <pre>  
 * ClassName:ServicePoolDaoImpl 
 * 
 * description here!
 * <pre/>   
 * Date:     2016年10月14日 上午10:35:33 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class ServicePoolDaoImpl implements ServicePoolDao{
    private JdbcTemplate jdbcTemplate; 
    private String selectSql = "select * from captain.servicePoolInfo";
    private String insertSql = "insert into captain.servicePoolInfo (type, proxyState, configAddr, idcList) values (?,?,?,?)";
    private String selectServicePoolInfoall = "select * from captain.servicePoolInfo where type in (:param)";
    private String updateSql = "update captain.servicePoolInfo set proxyState = ?,configAddr = ?,idcList = ? where type = ?";
    private String deleteSql = "delete from captain.servicePoolInfo where type = ?";
    
    
    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    @Override
    public boolean addServicePoolInfo(String type, int proxyState, String configAddr,String idcList) {
        try {
            int result = jdbcTemplate.update(insertSql, new Object[] {type,proxyState,configAddr,idcList});
            return result > 0; 
        } catch (DataAccessException e) {
            ApiLogger.error("servicePoolDaoImpl add service pool info error:type=" + type + "proxyState=" + proxyState
                    + "configAddr=" + configAddr+"idcList="+idcList,e);
            throw e;
        }
    }

    @Override
    public List<ServicePoolInfo> getServiceType() {
         try {
            List<ServicePoolInfo> result = jdbcTemplate.query(selectSql, new ServicePoolInfoRowMapper());
            return result;
         } catch (DataAccessException e) {
             ApiLogger.error("servicePoolDaoImpl get service type error",e);
             throw e;
         }
    }
    
    private class ServicePoolInfoRowMapper implements RowMapper<ServicePoolInfo> {

        @Override
        public ServicePoolInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
            ServicePoolInfo servicePoolInfo = new ServicePoolInfo();
            servicePoolInfo.setType(rs.getString("type"));
            servicePoolInfo.setProxyState(rs.getInt("proxyState"));
            servicePoolInfo.setConfigAddr(rs.getString("configAddr"));
            servicePoolInfo.setIdcList(rs.getString("idcList"));
            return servicePoolInfo;
        }
        
    }

    @Override
    public List<ServicePoolInfo> getServicePoolInfolist(List groups) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("param", groups);
        NamedParameterJdbcTemplate jdbc = new NamedParameterJdbcTemplate(jdbcTemplate.getDataSource());
        try {
            List<ServicePoolInfo> list = jdbc.query(selectServicePoolInfoall, paramMap, new ServicePoolInfoRowMapper());
            return list;

        } catch (DataAccessException e) {
            ApiLogger.error("servicePoolDaoImpl get service pool info error,type="+groups,e);
            throw e;
        }
    }


    @Override
    public boolean updateServicePoolInfo(String type, int proxyState, String configAddr, String idcList) {
        try {
            int result = jdbcTemplate.update(updateSql,
                    new Object[] {proxyState,configAddr,idcList,type});
            return result > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("servicePoolDaoImpl update resource error", e);
            throw e;
        }
    }

    @Override
    public boolean delServicePoolInfo(String type) {
        try {
            int result = jdbcTemplate.update(deleteSql, new Object[] {type});
            return result > 0;
        } catch (DataAccessException e) {
            ApiLogger.error("servicePoolDaoImpl delete error,type =" + type,e);
            throw e;
        }
    }

}
  
	