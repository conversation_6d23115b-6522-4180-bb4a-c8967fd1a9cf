/**
 * Project Name:captain-assigner File Name:ConfigRecording1.java Package
 * Name:com.weibo.api.captain.assigner.model Date:2016年7月13日下午7:05:53 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.model;

/**
 * <pre> ClassName:ConfigRecording
 * 
 * 配置信息 <pre/> Date: 2016年7月13日 下午7:05:53 <br/>
 * 
 * <AUTHOR>
 * @version
 * @see
 */
public class ConfigRecording {
    private String group;
    private String sign;
    private String content;
    private String createTime;
    private String biz;
    private String updateTime;

    public String getGroup() {
        return group;
    }
     
    public String getBiz() {
		return biz;
	}

	public void setBiz(String biz) {
		this.biz = biz;
	}

	public void setGroup(String group) {
        this.group = group;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}

