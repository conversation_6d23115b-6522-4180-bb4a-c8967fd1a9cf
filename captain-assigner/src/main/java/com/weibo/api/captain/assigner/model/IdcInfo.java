/**
 * Project Name:captain-assigner File Name:IdcInfo.java Package
 * Name:com.weibo.api.captain.assigner.model Date:2016年6月28日下午10:47:51 Copyright (c) 2016, @weibo
 * All Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.model;

/**
 * <pre> ClassName:IdcInfo
 * 
 * 机房部署信息 <pre/> Date: 2016年6月28日 下午10:47:51 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class IdcInfo {
    /*
     * 对应提案id，当加了回退功能后，需要记录历史提案(提案id不唯一，但是数据库会有自增id)，这里的对应提案id命名为backupProId(数据库自增id).
     */
    private int backupProId;
    private String idc;
    private String group;
    //分层模式:0表示单机房主库模式，1表示单机房独立主备,2表示多机房互为主备
//    private int layerMode;
    private int masterCapacity;
    private int masterL1Capacity;
    // l1对应的分组数
    private int masterL1Block;
    private int masterBandWidth;
    private int masterL1BandWidth;
    private String slave;
    private String masterIps;
    private String slaveIps;
    private String masterL1Ips;
    private String slaveL1Ips;
    /*
     * 状态信息，主要有两种状态：0，表示待分配状态，1，表示已分配资源状态
     */
    private int state;
    private String updateTime;
    
    //复用机房信息
    private String reuseIdc;

    //masterL1复用
    private String masterL1Idc;
    
    //同步机房信息，多个以","分隔
    private String slaveL1Idc;

    public int getBackupProId() {
        return backupProId;
    }

    public void setBackupProId(int backupProId) {
        this.backupProId = backupProId;
    }

    public String getIdc() {
        return idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }
    
    public String getGroup() {
        return group;
    }
    
    public void setGroup(String group) {
        this.group = group;
    }

//    public int getLayerMode() {
//        return layerMode;
//    }
//
//    public void setLayerMode(int layerMode) {
//        this.layerMode = layerMode;
//    }

    public int getMasterCapacity() {
        return masterCapacity;
    }

    public void setMasterCapacity(int masterCapacity) {
        this.masterCapacity = masterCapacity;
    }

    public int getMasterL1Capacity() {
        return masterL1Capacity;
    }

    public void setMasterL1Capacity(int masterL1Capacity) {
        this.masterL1Capacity = masterL1Capacity;
    }

    public int getMasterL1Block() {
        return masterL1Block;
    }

    public void setMasterL1Block(int masterL1Block) {
        this.masterL1Block = masterL1Block;
    }

    public int getMasterBandWidth() {
        return masterBandWidth;
    }

    public void setMasterBandWidth(int masterBandWidth) {
        this.masterBandWidth = masterBandWidth;
    }

    public int getMasterL1BandWidth() {
        return masterL1BandWidth;
    }

    public void setMasterL1BandWidth(int masterL1BandWidth) {
        this.masterL1BandWidth = masterL1BandWidth;
    }

    public String getSlave() {
        return slave;
    }

    public void setSlave(String slave) {
        this.slave = slave;
    }

    public String getMasterIps() {
        return masterIps;
    }

    public void setMasterIps(String masterIps) {
        this.masterIps = masterIps;
    }

    public String getSlaveIps() {
        return slaveIps;
    }

    public void setSlaveIps(String slaveIps) {
        this.slaveIps = slaveIps;
    }

    public String getMasterL1Ips() {
        return masterL1Ips;
    }

    public void setMasterL1Ips(String masterL1Ips) {
        this.masterL1Ips = masterL1Ips;
    }

    public String getSlaveL1Ips() {
        return slaveL1Ips;
    }

    public void setSlaveL1Ips(String slaveL1Ips) {
        this.slaveL1Ips = slaveL1Ips;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getReuseIdc() {
        return reuseIdc;
    }

    public void setReuseIdc(String reuseIdc) {
        this.reuseIdc = reuseIdc;
    }

    public String getSlaveL1Idc() {
        return slaveL1Idc;
    }

    public void setSlaveL1Idc(String slaveL1Idc) {
        this.slaveL1Idc = slaveL1Idc;
    }
    
    /**
     * 重写 equals方法
     * @param object
     * @return
     */
    @Override
    public boolean equals(Object object) {
        if (object instanceof IdcInfo) {
            IdcInfo idcInfo = (IdcInfo) object;
            if (this.idc.equals(idcInfo.getIdc())){
                return true;
            }
        }
        return false;
    }

    public String getMasterL1Idc() {
        return masterL1Idc;
    }

    public void setMasterL1Idc(String masterL1Idc) {
        this.masterL1Idc = masterL1Idc;
    }
}

