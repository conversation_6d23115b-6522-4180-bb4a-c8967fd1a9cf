package com.weibo.api.captain.assigner.model;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-05-24 20:14
 */
public class IdcInfoConsistencyDiff {

    /**
     * 提案id
     */
    private Integer proId;

    /**
     * 具体的集群间ip差异
     */
    private List<IdcInfoDiff> idcInfoDiffs;

    /**
     * generateId当中的biz
     */
    private String namespace;


    public IdcInfoConsistencyDiff(){
        this.idcInfoDiffs = new ArrayList();
    }

    public Integer getProId() {
        return proId;
    }

    public void setProId(Integer proId) {
        this.proId = proId;
    }

    public List<IdcInfoDiff> getIdcDiffs() {
        return idcInfoDiffs;
    }

    public void setIdcDiffs(List<IdcInfoDiff> idcDiffs) {
        this.idcInfoDiffs = idcDiffs;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }
}
