package com.weibo.api.captain.assigner.model;

import java.util.Set;

/**
 * <AUTHOR>
 * @Description diffIps内容是 sourceGroupName的ip和targetGroupName的差集
 * （在sourceGroup当中，但是不在targetGroup当中的ip集合）
 * （reverse相关的集合，表示在targetGroup当中，但是不在sourceGroup当中的ip集合）
 * @create 2021-05-24 20:14
 */
public class IdcInfoDiff {
    /**
     * 原集群名称
     */
    private String sourceGroupName;

    /**
     * 对比的集群名称
     */
    private String targetGroupName;
    /**
     * master ip差异
     */
    private Set<String> masterDiffIps;

    /**
     * slave ip差异
     */
    private Set<String> slaveDiffIps;

    /**
     * masterL1 ip差异
     */
    private Set<String> masterL1DiffIps;

    /**
     * slaveL1 ip差异
     */
    private Set<String> slaveL1DiffIps;

    /**
     * reverse开头的，表示在targetGroup当中，但是不在sourceGroup当中的ip集合
     */
    private Set<String> reverseMasterDiffIps;

    private Set<String> reverseSlaveDiffIps;

    private Set<String> reverseMasterL1DiffIps;

    private Set<String> reverseSlaveL1DiffIps;

    public String getSourceGroupName() {
        return sourceGroupName;
    }

    public void setSourceGroupName(String sourceGroupName) {
        this.sourceGroupName = sourceGroupName;
    }

    public String getTargetGroupName() {
        return targetGroupName;
    }

    public void setTargetGroupName(String targetGroupName) {
        this.targetGroupName = targetGroupName;
    }

    public Set<String> getMasterDiffIps() {
        return masterDiffIps;
    }

    public void setMasterDiffIps(Set<String> masterDiffIps) {
        this.masterDiffIps = masterDiffIps;
    }

    public Set<String> getSlaveDiffIps() {
        return slaveDiffIps;
    }

    public void setSlaveDiffIps(Set<String> slaveDiffIps) {
        this.slaveDiffIps = slaveDiffIps;
    }

    public Set<String> getMasterL1DiffIps() {
        return masterL1DiffIps;
    }

    public void setMasterL1DiffIps(Set<String> masterL1DiffIps) {
        this.masterL1DiffIps = masterL1DiffIps;
    }

    public Set<String> getSlaveL1DiffIps() {
        return slaveL1DiffIps;
    }

    public void setSlaveL1DiffIps(Set<String> slaveL1DiffIps) {
        this.slaveL1DiffIps = slaveL1DiffIps;
    }



    public Set<String> getReverseMasterDiffIps() {
        return reverseMasterDiffIps;
    }

    public void setReverseMasterDiffIps(Set<String> reverseMasterDiffIps) {
        this.reverseMasterDiffIps = reverseMasterDiffIps;
    }

    public Set<String> getReverseSlaveDiffIps() {
        return reverseSlaveDiffIps;
    }

    public void setReverseSlaveDiffIps(Set<String> reverseSlaveDiffIps) {
        this.reverseSlaveDiffIps = reverseSlaveDiffIps;
    }

    public Set<String> getReverseMasterL1DiffIps() {
        return reverseMasterL1DiffIps;
    }



    public void setReverseMasterL1DiffIps(Set<String> reverseMasterL1DiffIps) {
        this.reverseMasterL1DiffIps = reverseMasterL1DiffIps;
    }

    public Set<String> getReverseSlaveL1DiffIps() {
        return reverseSlaveL1DiffIps;
    }

    public void setReverseSlaveL1DiffIps(Set<String> reverseSlaveL1DiffIps) {
        this.reverseSlaveL1DiffIps = reverseSlaveL1DiffIps;
    }
}
