/**
 * Project Name:captain-assigner File Name:IdcLayerMode.java Package
 * Name:com.weibo.api.captain.assigner.model Date:2016年9月13日下午5:43:26 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.model;

/**
 * <pre> ClassName:IdcLayerMode
 * 
 * description here! <pre/> Date: 2016年9月13日 下午5:43:26 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public enum IdcLayerMode {
    /**
     * 单机房主库模式
     */
    SINGLE_IDC_MASTER(0),

    /**
     * 单机房独立主备
     */
    SINGLE_IDC_MASTER_SLAVE(1),

    /**
     * 多机房互为主备
     */
    IDC_MASTER_SLAVE(2);

    private int value;

    private IdcLayerMode(int value) {
        this.value = value;
    }

    public String toString() {
        return String.valueOf(this.value);
    }

}

