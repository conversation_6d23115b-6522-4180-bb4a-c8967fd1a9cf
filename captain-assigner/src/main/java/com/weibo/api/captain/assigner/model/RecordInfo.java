/**
 * Project Name:captain-assigner File Name:RecordInfo.java Package
 * Name:com.weibo.api.captain.assigner.model Date:2016年6月28日下午10:44:20 Copyright (c) 2016, @weibo
 * All Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.model;

/**
 * <pre> ClassName:RecordInfo
 * 
 * 提案对应基本存储信息 <pre/> Date: 2016年6月28日 下午10:44:20 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class RecordInfo {
    private int id;
    private int proId;
    /*
     * 业务类型
     */
    private String type;
    /*
     * namespace
     */
    private String biz;
    private float hitPercent;
    private int readTps;
    private int writeTps;
    private String createTime;
    private String updateTime;
    /*
     * 提案对应状态，目前总共有6种状态，－1：保存，0:申请，1:分配，2:部署，3:说明部署成功，进行变更后的数据，可以理解为备份，便于回滚，4:回退状态,如果配置失败，
     * 会进行回退到上一个版本
     */
    private int state;
    private String applyUser;
    private String assignUser;
    private String deployUser;
    private String returnUser;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getProId() {
        return proId;
    }

    public void setProId(int proId) {
        this.proId = proId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    // public int getLayerMode() {
    // return layerMode;
    // }
    //
    // public void setLayerMode(int layerMode) {
    // this.layerMode = layerMode;
    // }

    public float getHitPercent() {
        return hitPercent;
    }

    public void setHitPercent(float hitPercent) {
        this.hitPercent = hitPercent;
    }

    public int getReadTps() {
        return readTps;
    }

    public void setReadTps(int readTps) {
        this.readTps = readTps;
    }

    public int getWriteTps() {
        return writeTps;
    }

    public void setWriteTps(int writeTps) {
        this.writeTps = writeTps;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }

    public String getAssignUser() {
        return assignUser;
    }

    public void setAssignUser(String assignUser) {
        this.assignUser = assignUser;
    }

    public String getDeployUser() {
        return deployUser;
    }

    public void setDeployUser(String deployUser) {
        this.deployUser = deployUser;
    }

    public String getReturnUser() {
        return returnUser;
    }

    public void setReturnUser(String returnUser) {
        this.returnUser = returnUser;
    }
}

