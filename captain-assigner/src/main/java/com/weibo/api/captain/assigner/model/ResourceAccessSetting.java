/**
 * Project Name:captain-assigner File Name:ResourceAssessSetting.java Package
 * Name:com.weibo.api.captain.assigner.model Date:2016年5月19日下午2:14:37 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.model;

/**
 * <pre> ClassName:ResourceAssessSetting
 * 
 * 资源访问设置 <pre/> Date: 2016年5月19日 下午2:14:37 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class ResourceAccessSetting {
//    private int bid;
//    private int clusterId;
    private int id;
    /*
     * 对应提案id，当加了回退功能后，需要记录历史提案(提案id不唯一，但是数据库会有自增id)，这里的对应提案id命名为backupProId(数据库自增id).
     */
    private int backupProId;
    private String hash;
    private String distribution;
    private String hashTag;
    private int saveTag;
    private int autoEjectHosts;
    private long timeout;
//    private long masterEffectiveTime;
//    private long lruTimeout;
    private int resourceType;
    private long serverRetryTimeout;
    private int serverFailureLimit;
    private int partialReply;
    private long exptime;
    private int forceWriteAll;
    private int updateSlaveL1;
    private int localAffinity;
    private int flag;
    private String updateTime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getBackupProId() {
        return backupProId;
    }

    public void setBackupProId(int backupProId) {
        this.backupProId = backupProId;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getDistribution() {
        return distribution;
    }

    public void setDistribution(String distribution) {
        this.distribution = distribution;
    }

    public String getHashTag() {
        return hashTag;
    }

    public void setHashTag(String hashTag) {
        this.hashTag = hashTag;
    }

    public int getSaveTag() {
        return saveTag;
    }

    public void setSaveTag(int saveTag) {
        this.saveTag = saveTag;
    }

    public int getAutoEjectHosts() {
        return autoEjectHosts;
    }

    public void setAutoEjectHosts(int autoEjectHosts) {
        this.autoEjectHosts = autoEjectHosts;
    }

    public long getTimeout() {
        return timeout;
    }

    public void setTimeout(long timeout) {
        this.timeout = timeout;
    }

//    public long getMasterEffectiveTime() {
//        return masterEffectiveTime;
//    }
//
//    public void setMasterEffectiveTime(long masterEffectiveTime) {
//        this.masterEffectiveTime = masterEffectiveTime;
//    }
//
//    public long getLruTimeout() {
//        return lruTimeout;
//    }
//
//    public void setLruTimeout(long lruTimeout) {
//        this.lruTimeout = lruTimeout;
//    }

    public int getResourceType() {
        return resourceType;
    }

    public void setResourceType(int resourceType) {
        this.resourceType = resourceType;
    }

    public long getServerRetryTimeout() {
        return serverRetryTimeout;
    }

    public void setServerRetryTimeout(long serverRetryTimeout) {
        this.serverRetryTimeout = serverRetryTimeout;
    }

    public int getServerFailureLimit() {
        return serverFailureLimit;
    }

    public void setServerFailureLimit(int serverFailureLimit) {
        this.serverFailureLimit = serverFailureLimit;
    }

    public int getPartialReply() {
        return partialReply;
    }

    public void setPartialReply(int partialReply) {
        this.partialReply = partialReply;
    }

    public long getExptime() {
        return exptime;
    }

    public void setExptime(long exptime) {
        this.exptime = exptime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public int getForceWriteAll() {
        return forceWriteAll;
    }

    public void setForceWriteAll(int forceWriteAll) {
        this.forceWriteAll = forceWriteAll;
    }

    public int getUpdateSlaveL1() {
        return updateSlaveL1;
    }

    public void setUpdateSlaveL1(int updateSlaveL1) {
        this.updateSlaveL1 = updateSlaveL1;
    }

    public void setlocalAffinity(int localAffinity) {
        this.localAffinity = localAffinity;
    }

    public int getlocalAffinity() {
        return localAffinity;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }
}

