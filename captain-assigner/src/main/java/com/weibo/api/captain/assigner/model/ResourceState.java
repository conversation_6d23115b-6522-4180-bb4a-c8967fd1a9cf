/**
 * Project Name:captain-assigner File Name:ResourceState.java Package
 * Name:com.weibo.api.captain.assigner.model Date:2016年9月13日下午3:56:21 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.model;

/**
 * <pre> ClassName:ResourceState
 * 
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public enum ResourceState {
    /**
     * 保存
     */
    SAVE_STATE(-1), 
    
    /**
     * 申请
     */
    APPLY_STATE(0), 
    
    /**
     * 分配
     */
    ASSIGN_STATE(1), 
    
    /**
     * 部署
     */
    DEPLOY_STATE(2), 
    
    /**
     * 历史状态，在资源变更时，保留资源信息，便于回滚
     */
    HISTORY_STATE(3), 
    
    /**
     * 回滚状态
     */
    REVERT_STATE(4), 
    
    /**
     * 驳回状态
     */
    REJECT_STATE(5);

    private int value;

    private ResourceState(int value) {
        this.value = value;
    }

    public String toString() {
        return String.valueOf(this.value);
    }
}

