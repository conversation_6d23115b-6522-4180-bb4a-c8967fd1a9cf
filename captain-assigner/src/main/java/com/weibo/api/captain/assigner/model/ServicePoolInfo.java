/**
 * Project Name:captain-assigner File Name:ServicePoolInfo.java Package
 * Name:com.weibo.api.captain.assigner.model Date:2016年10月14日下午6:00:12 Copyright (c) 2016, @weibo
 * All Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.model;

/**
 * <pre> ClassName:ServicePoolInfo
 * 
 * description here! <pre/> Date: 2016年10月14日 下午6:00:12 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class ServicePoolInfo {
    String type;
    int proxyState;
    String configAddr;
    String idcList;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getProxyState() {
        return proxyState;
    }

    public void setProxyState(int proxyState) {
        this.proxyState = proxyState;
    }

    public String getConfigAddr() {
        return configAddr;
    }

    public void setConfigAddr(String configAddr) {
        this.configAddr = configAddr;
    }

    public String getIdcList() {
        return idcList;
    }

    public void setIdcList(String idcList) {
        this.idcList = idcList;
    }
}

