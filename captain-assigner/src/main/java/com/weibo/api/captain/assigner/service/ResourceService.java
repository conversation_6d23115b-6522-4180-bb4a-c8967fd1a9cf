/**
 * Project Name:captain-assigner  
 * File Name:ResourceService1.java  
 * Package Name:com.weibo.api.captain.assigner.service  
 * Date:2016年6月29日上午10:19:50  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *
 */

package com.weibo.api.captain.assigner.service;

import java.util.List;

import cn.sina.api.commons.util.JsonBuilder;
import com.weibo.api.captain.assigner.model.IdcInfo;
import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.assigner.model.ResourceAccessSetting;

/**
 * <pre>  
 * ClassName:ResourceService1 
 *
 * 基础服务类
 * <pre/>   
 * Date:     2016年6月29日 上午10:19:50 <br/>  
 * <AUTHOR>
 * @version
 * @see
 */
public interface ResourceService {
    /**
     *
     * applyResource:申请资源. <br/>    
     * 包含两种情况：1，用户先进行了保存，再申请资源，此时在申请资源时，已经生成一个提案id，这时需要更改信息.<br/>
     * 2，用户直接申请资源，此时需要注册信息。申请资源对应的状态为state＝0；  
     *
     * <AUTHOR>
     * @param recordInfo
     * @return
     */
    String applyResource(RecordInfo recordInfo, ResourceAccessSetting resourceAccessSetting, List<IdcInfo> idcInfos);

    /**
     *
     * generatorYaml:根据资源信息生成yaml文件. <br/>   
     * 调用该方法应在分配资源前.<br/>  
     *
     * <AUTHOR>
     * @param
     * @return
     */
    String generatorYaml(int id, ResourceAccessSetting resourceAccessSetting,List<IdcInfo> idcInfos);

    /**
     *
     * assignResource:分配资源. <br/>   
     * 将信息写到数据库及configserver中.<br/>  
     *
     * <AUTHOR>
     * @param id 提案id
     * @param group 对应group
     * @param configStr yaml配置信息
     * @return
     */
    String assignResource(int id, String group, String configStr,String assignUser,String idcInfo);

    String getResource(int id);

    JsonBuilder getResourceJsonBuilder(int id);

    String getResourceByPort(Integer port,boolean assigned);

    String getResource(boolean assigned);

    String deployResource(String deployUser, int id);

    String getAllBizStats(String group);

    String getProxyStats();

    String returnResource(int id);

    String addService(String type,int proxyState, String configAddr, String idcArray[]);

    String getService();

    String rejectService(int id,String rejectUser);

    boolean isExist(String type, String biz);

    boolean isExistServicePoolInfo(String type, String idc);
    
    public String delResource(int id);
}
  
	