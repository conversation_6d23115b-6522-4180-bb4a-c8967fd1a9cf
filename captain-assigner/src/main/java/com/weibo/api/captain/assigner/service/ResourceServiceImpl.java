/**
 * Project Name:captain-assigner  
 * File Name:ResourceService1Impl.java  
 * Package Name:com.weibo.api.captain.assigner.service  
 * Date:2016年6月29日上午10:20:11  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *
 */

package com.weibo.api.captain.assigner.service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;
import com.weibo.api.captain.common.service.StaticConfigBiz;
import com.weibo.api.captain.common.util.CaptainUtil;
import com.weibo.api.captain.common.util.ThreadLocalUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.weibo.api.captain.assigner.dao.BizStatsDao;
import com.weibo.api.captain.assigner.dao.ConfigRecordingDao;
import com.weibo.api.captain.assigner.dao.GenerateIdDao;
import com.weibo.api.captain.assigner.dao.IdcInfoDao;
import com.weibo.api.captain.assigner.dao.ProxyStatsDao;
import com.weibo.api.captain.assigner.dao.ResourceAccessSettingDao;
import com.weibo.api.captain.assigner.dao.ResourceDao;
import com.weibo.api.captain.assigner.dao.ServicePoolDao;
import com.weibo.api.captain.assigner.model.BaseInfo;
import com.weibo.api.captain.assigner.model.ConfigRecording;
import com.weibo.api.captain.assigner.model.IdcInfo;
import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.assigner.model.ResourceAccessSetting;
import com.weibo.api.captain.assigner.model.ResourceState;
import com.weibo.api.captain.assigner.model.ServicePoolInfo;
import com.weibo.api.captain.assigner.stats.BizStatsCompare;
import com.weibo.api.captain.assigner.stats.ProxyStats;
import com.weibo.api.captain.common.CaptainConstants;
import com.weibo.api.captain.common.exception.CaptainExcepFactor;
import com.weibo.api.captain.common.exception.CaptainException;
import com.weibo.api.captain.common.model.CacheAgentConfig;
import com.weibo.api.captain.common.model.CacheAgentGlobalConfig;
import com.weibo.api.captain.common.model.CacheAgentGroupConfig;
import com.weibo.api.captain.common.service.StaticConfigBizImpl;
import com.weibo.api.captain.common.util.CaptainJsonUtil;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.JsonBuilder;

/**
 * <pre>  
 * ClassName:ResourceService1Impl 
 *
 *
 * <pre/>   
 * Date:     2016年6月29日 上午10:20:11 <br/>  
 * <AUTHOR>
 * @version
 * @since    JDK 1.8  
 * @see
 */
public class ResourceServiceImpl implements ResourceService {

    private final static int RESOURCE_ACCESS_DEFAULT_SETTING = 1;
    private ResourceDao resourceDao;
    private ConfigRecordingDao configRecordingDao;
    private BizStatsDao bizStatsDao;
    private ProxyStatsDao proxyStatsDao;
    private ResourceAccessSettingDao resourceAccessSettingDao;
    private IdcInfoDao idcInfoDao;
    private GenerateIdDao generateIdDao;
    private final static int IDC_STATE = 1;
    /*
     * 对于提案来说，只有在插入数据库后才会自动生成id，默认情况下id为0，也可以理解成无效的id.
     */
    private final static int DEFAULT_ID = 0;
    private ServicePoolDao servicePoolDao;
    private ApplicationContext ctx;

    public void init() {
        ctx = new ClassPathXmlApplicationContext("captain-common.xml");
    }

    public void setResourceDao(ResourceDao resourceDao) {
        this.resourceDao = resourceDao;
    }

    public void setConfigRecordingDao(ConfigRecordingDao configRecordingDao) {
        this.configRecordingDao = configRecordingDao;
    }

    public void setBizStatsDao(BizStatsDao bizStatsDao) {
        this.bizStatsDao = bizStatsDao;
    }

    public void setProxyStatsDao(ProxyStatsDao proxyStatsDao) {
        this.proxyStatsDao = proxyStatsDao;
    }

    public void setResourceAccessSettingDao(ResourceAccessSettingDao resourceAccessSettingDao) {
        this.resourceAccessSettingDao = resourceAccessSettingDao;
    }

    public void setIdcInfoDao(IdcInfoDao idcInfoDao) {
        this.idcInfoDao = idcInfoDao;
    }

    public void setGenerateIdDao(GenerateIdDao generateIdDao) {
        this.generateIdDao = generateIdDao;
    }

    public void setServicePoolDao(ServicePoolDao servicePoolDao) {
        this.servicePoolDao = servicePoolDao;
    }

    @Override
    @Transactional(propagation= Propagation.SUPPORTS)
    public String applyResource(RecordInfo recordInfo, ResourceAccessSetting resourceAccessSetting, List<IdcInfo> idcInfos) {
        try {
            if (recordInfo.getProId() == DEFAULT_ID) {//保存或直接申请
                recordInfo.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                int proId = generateIdDao.generateId(recordInfo.getType(),recordInfo.getBiz());//使用数据库自增id
                recordInfo.setProId(proId);
                int backupProId = DEFAULT_ID;
                if ((backupProId = resourceDao.addResource(recordInfo)) == DEFAULT_ID) {
                    ApiLogger.warn("resourceServiceImpl add resource error, type="+recordInfo.getType()+",biz="+recordInfo.getBiz());
                    return makeToJson(String.valueOf(DEFAULT_ID));
                }
                resourceAccessSetting.setBackupProId(backupProId);
                if (!resourceAccessSettingDao.addResourceAccessSetting(resourceAccessSetting)) {
                    ApiLogger.warn("resourceServiceImpl add resourceAccessSetting error,type="+recordInfo.getType()+",biz="+recordInfo.getBiz());
                    return makeToJson(String.valueOf(DEFAULT_ID));
                }
                if (idcInfos != null) {
                    for (IdcInfo idcInfo:idcInfos) {
                        idcInfo.setBackupProId(backupProId);
                        if (!idcInfoDao.addIdcInfo(idcInfo)) {
                            ApiLogger.warn("resourceServiceImpl add idcInfo error,type="+recordInfo.getType()+",biz="+recordInfo.getBiz()+",group="+idcInfo.getGroup());
                            return makeToJson(String.valueOf(DEFAULT_ID));
                        }
                    }
                }
            } else {
                RecordInfo applyResourceInfo = resourceDao.getResource(recordInfo.getProId());
                if (applyResourceInfo.getState() != Integer.parseInt(ResourceState.SAVE_STATE.toString()) && applyResourceInfo.getState() != Integer.parseInt(ResourceState.APPLY_STATE.toString())) {
                    return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "The resource of id is not to apply, state error"));
                }
                int backupProId = applyResourceInfo.getId();
                recordInfo.setId(backupProId);
                resourceAccessSetting.setBackupProId(backupProId);
                if (!resourceDao.updateResource(recordInfo) || !resourceAccessSettingDao.updateResourceAccessSetting(resourceAccessSetting)) {
                    ApiLogger.warn("resourceServiceImpl update recordInfo error or resourceAccessSetting error,type="+recordInfo.getType()+",biz="+recordInfo.getBiz());
                    return makeToJson(String.valueOf(DEFAULT_ID));
                }
                List<IdcInfo> getIdcInfosFromSql = idcInfoDao.getIdcInfos(backupProId);
                List<IdcInfo> commonIdcInfoList = new ArrayList<IdcInfo>(30);//存储参数及数据库含有的共同的idcinfo
                for (IdcInfo idcInfoParameter : idcInfos) {
                    idcInfoParameter.setBackupProId(backupProId);
                    if (getIdcInfosFromSql.contains(idcInfoParameter)) {
                        commonIdcInfoList.add(idcInfoParameter);
                        if (!idcInfoDao.updateIdcInfo(idcInfoParameter)) {
                            return makeToJson(String.valueOf(DEFAULT_ID));
                        }
                    } else {
                        if (!idcInfoDao.addIdcInfo(idcInfoParameter)) {
                            return makeToJson(String.valueOf(DEFAULT_ID));
                        }
                    }
                }
                for (IdcInfo idcInfoSql : getIdcInfosFromSql) {
                    if (!commonIdcInfoList.contains(idcInfoSql)) {
                        if (!idcInfoDao.deleteIdcInfo(idcInfoSql)) {
                            return makeToJson(String.valueOf(DEFAULT_ID));
                        }
                    }
                }
            }
        } catch (Exception e) {
            ApiLogger.warn("resourceServiceImpl apply resource error,type="+recordInfo.getType()+",biz="+recordInfo.getBiz());
        }
        return makeToJson(String.valueOf(recordInfo.getProId()));
    }

    private <T> String makeToJson (T result) {
        JsonBuilder jsonBuilder = new JsonBuilder();
        jsonBuilder.append("result", (result+""));
        return jsonBuilder.flip().toString();
    }

    public String generatorYaml(int id, ResourceAccessSetting resourceAccessSetting,List<IdcInfo> idcInfoList) {
        RecordInfo recordInfo = resourceDao.getResource(id);
        if (recordInfo == null) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (id)'s resource doesn't exist"));
        }
        try {
            List<ConfigRecording> configRecordings = new ArrayList<ConfigRecording>();
            for (IdcInfo idcInfo : idcInfoList) {
                String group = idcInfo.getGroup();
                String type = generatorType(group);
                ServicePoolInfo servicePoolInfo = getServicePoolInfo(type);
                String configStr = generatorYamlConfig(idcInfo, resourceAccessSetting,servicePoolInfo.getConfigAddr());
                //只需要当前业务方对应的配置
                ConfigRecording configRecording = new ConfigRecording();
                configRecording.setGroup(group);
                configRecording.setContent(configStr);
                configRecordings.add(configRecording);
            }
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            return gson.toJson(configRecordings);
        } catch (Exception e) {
            ApiLogger.warn("resourceServiceImpl generator yaml error:id=" + resourceAccessSetting.getBackupProId(), e);
        }
        return null;
    }

    @Override
    public String assignResource(int id, String group, String configStr, String assignUser, String idcInfo) {
        RecordInfo recordInfo = resourceDao.getResource(id);
        if (recordInfo == null) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_RESOURCE_NOT_EXISTS, "The id related information does not exist"));
        }
        if (recordInfo.getState() != Integer.parseInt(ResourceState.APPLY_STATE.toString()) && recordInfo.getState() != Integer.parseInt(ResourceState.DEPLOY_STATE.toString())) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "The state of the resource can not be assigned "));
        }
        boolean result = false;
        CacheAgentConfig cacheAgentConfig = CacheAgentConfig.parseStr(configStr);
        if (cacheAgentConfig == null) {
            ApiLogger.warn("assign resource parse configStr error,group="+group+",configStr="+configStr);
            return makeToJson(result);
        }
        ApiLogger.info("assign resource parse configStr success,group="+group+",size="+cacheAgentConfig.getGroupConfs().size());
        CacheAgentGroupConfig groupConfig = cacheAgentConfig.getGroupConfs().get(0);//从上层传过来的configStr对应的是一个namespace的
        BaseInfo baseInfo = generateIdDao.getBaseInfo(recordInfo.getProId());
        if (!baseInfo.getBiz().equals(groupConfig.getName())) {
            ApiLogger.warn("assign resource biz inconsistencies,baseInfoBiz="+baseInfo.getBiz()+",configStrBiz="+groupConfig.getName());
            return makeToJson(result);
        }
        String type = generatorType(group);
        if (!baseInfo.getType().equals(type)) {
            ApiLogger.warn("assign resource type inconsistencies,baseInfoType="+baseInfo.getType()+",configStrType="+type);
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "The type of the id does not match the argument type"));
        }

        ServicePoolInfo servicePoolInfo = getServicePoolInfo(type);
        StaticConfigBiz staticConfigBiz = (StaticConfigBizImpl) ctx.getBean("staticConfigBiz");

        //读主,print configserver
        String configServer=ThreadLocalUtil.getServer(servicePoolInfo.getConfigAddr());
        staticConfigBiz.setBaseUrl(configServer);
        ApiLogger.info("assignResource assign resource get config server success,configServer="+configServer+",old configserver:"+servicePoolInfo.getConfigAddr());

        String registerConfigStr = configStr;
        try {
            Thread.sleep(1);
        }catch (InterruptedException e) {
            ApiLogger.warn("assign resource sleep error", e);
        }
        CacheAgentConfig cacheAgentConfigFromConfigServer = staticConfigBiz.lookup(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
        if (cacheAgentConfigFromConfigServer == null) {//防止configserver不稳定
            cacheAgentConfigFromConfigServer = staticConfigBiz.lookup(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
        }
        if(cacheAgentConfigFromConfigServer == null){//TODO  临时紧急解决，更新时两次请求vintage仍然为空的情况，直接返回失败(此处可能会影响非更新操作本来vintage就为空的情况)
            Map map = new HashMap();
            map.put("code",400);
            map.put("desc","get config from vintage null or timeout");
            ApiLogger.warn("get config from vintage null or timeout two times，group = "+group);
            return JSON.toJSONString(map);
        }
        List<IdcInfo> getFromIdcList = idcInfoDao.getIdcInfos(recordInfo.getId());
        if (cacheAgentConfigFromConfigServer != null) {
            List<CacheAgentGroupConfig> groupConfs = cacheAgentConfigFromConfigServer.getGroupConfs();
            boolean isExist = false;
            ApiLogger.info("assign resource groupConfs size="+groupConfs.size());
            for (int i = 0; i < groupConfs.size(); i++) {
                if (groupConfs.get(i).getName().equals(baseInfo.getBiz())) {
                    ApiLogger.info("assign resource groupConfs name="+groupConfs.get(i).getName());
                    ApiLogger.info("assign resource groupConfs："+groupConfs.get(i).toString());
                    groupConfs.set(i, groupConfig);
                    isExist = true;
                }
            }
            if (!isExist) {
                ApiLogger.warn("assign resource group not exist,group="+group);
                groupConfs.add(groupConfig);
            }
            for (int i = 0; i < groupConfs.size(); i++) {
                if (groupConfs.get(i).getHashTag().equals("chaohua_go")) {
                    groupConfs.remove(i);
                    ApiLogger.info("assign resource remove chaohua_go");
                    break;
                }
            }
            registerConfigStr = cacheAgentConfigFromConfigServer.toYamlStr();
        }
        result = register2Vintage(group,registerConfigStr,servicePoolInfo.getConfigAddr(),staticConfigBiz);
        if (result) {
            result = updateInfo(recordInfo, group, groupConfig,assignUser,idcInfo,getFromIdcList);
            if (result) {
                ApiLogger.info("assign resource success,group="+group+",baseInfo="+baseInfo.getBiz());
                String sign = staticConfigBiz.getSign(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
                if (sign == null) {// 重试一次
                    sign = staticConfigBiz.getSign(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
                }
                ConfigRecording configRecording = new ConfigRecording();
                configRecording.setGroup(group);
                configRecording.setBiz(baseInfo.getBiz());
                configRecording.setContent(configStr);
                configRecording.setSign(sign);
                configRecording.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                result = configRecordingDao.addConfigRecording(configRecording);
            }
            else{
                ApiLogger.warn("assign resource updateInfo error,group="+group+",baseInfo="+baseInfo.getBiz());
            }
        }
        return makeToJson(result);
    }

    private String generatorType(String group) {
        String type = "";
        Pattern p=Pattern.compile("cache."+"(service\\.|service2\\.0\\.)"+"(\\w+.*)"+".pool");
        Matcher m=p.matcher(group);
        while(m.find()){
            type = m.group(2);
        }
        return type;
    }

    private boolean updateInfo(RecordInfo recordInfo, String group, CacheAgentGroupConfig groupConfig,String assignUser,String idcInfo,List<IdcInfo> getIdcInfoFromSql) {
        ResourceAccessSetting resourceAccessSetting = buildResourceAccessetting(groupConfig);
        resourceAccessSetting.setBackupProId(recordInfo.getId());
        if (recordInfo.getState() == Integer.parseInt(ResourceState.DEPLOY_STATE.toString())) {//资源管理，需要记录历史信息。
            int backupProId = resourceDao.addResource(recordInfo);
            if (backupProId == DEFAULT_ID) {
                ApiLogger.warn("assign resource addResource error,group="+group+",baseInfo="+recordInfo.getBiz());
                return false;
            }
            recordInfo.setState(Integer.parseInt(ResourceState.HISTORY_STATE.toString()));
            resourceAccessSetting.setBackupProId(backupProId);
            if (!resourceDao.updateResource(recordInfo) || !resourceAccessSettingDao.addResourceAccessSetting(resourceAccessSetting)) {
                ApiLogger.warn("assign resource updateResource error,group="+group+",baseInfo="+recordInfo.getBiz());
                return false;
            }
            IdcInfo idcInfoValue = null;
            if (!StringUtils.isBlank(idcInfo)) {
                GsonBuilder gb = new GsonBuilder();
                Gson gson = gb.create();
                List<IdcInfo> result = gson.fromJson(idcInfo, new TypeToken<List<IdcInfo>>() {}.getType());
                idcInfoValue = result.get(0);
            }
            boolean extraIdc = true;
            for (IdcInfo singleIdcInfo : getIdcInfoFromSql) {
                if (singleIdcInfo.getGroup().equals(group)) {
                    String masterIps = dealWithIps(groupConfig.getMaster());
                    String slaveIps = dealWithIps(groupConfig.getSlave());
                    String masterL1Ips = dealWithBlockIps(groupConfig.getMasterL1());
                    String slaveL1Ips = dealWithBlockIps(groupConfig.getSlaveL1());
                    extraIdc = false;
                    if (!StringUtils.isBlank(idcInfo)) {
                        if (!StringUtils.isBlank(idcInfoValue.getIdc())) {
                            singleIdcInfo.setIdc(idcInfoValue.getIdc());
                        }
                        singleIdcInfo.setMasterBandWidth(idcInfoValue.getMasterBandWidth());
                        singleIdcInfo.setMasterCapacity(idcInfoValue.getMasterCapacity());
                        singleIdcInfo.setMasterL1BandWidth(idcInfoValue.getMasterL1BandWidth());
                        singleIdcInfo.setMasterL1Block(idcInfoValue.getMasterL1Block());
                        singleIdcInfo.setMasterL1Capacity(idcInfoValue.getMasterL1Capacity());
                        singleIdcInfo.setReuseIdc(idcInfoValue.getReuseIdc());
                        if (!StringUtils.isBlank(idcInfoValue.getReuseIdc())) {//强制slave及slavel1为"".
                            singleIdcInfo.setSlave("");
                            singleIdcInfo.setSlaveL1Idc("");
                        } else {
                            singleIdcInfo.setSlave(idcInfoValue.getSlave());
                            singleIdcInfo.setSlaveL1Idc(idcInfoValue.getSlaveL1Idc());
                        }
                        //TODO:masterl1
                        singleIdcInfo.setMasterL1Idc(idcInfoValue.getMasterL1Idc());
                        singleIdcInfo.setState(IDC_STATE);
                    }
                    singleIdcInfo.setMasterIps(masterIps);
                    singleIdcInfo.setSlaveIps(slaveIps);
                    singleIdcInfo.setMasterL1Ips(masterL1Ips);
                    singleIdcInfo.setSlaveL1Ips(slaveL1Ips);
                }
                singleIdcInfo.setBackupProId(backupProId);
                if (!idcInfoDao.addIdcInfo(singleIdcInfo)) {
                    ApiLogger.warn("assign resource addIdcInfo error,group="+group+",baseInfo="+recordInfo.getBiz());
                    return false;
                }
            }
            if (extraIdc) {
                idcInfoValue.setState(IDC_STATE);
                idcInfoValue.setBackupProId(backupProId);
                if (!idcInfoDao.addIdcInfo(idcInfoValue)) {
                    ApiLogger.warn("assign resource addIdcInfo error,group="+group+",baseInfo="+recordInfo.getBiz());
                    return false;
                }
            }
        }else {//仅仅进行资源分配，更新值
            if (!resourceAccessSettingDao.updateResourceAccessSetting(resourceAccessSetting)) {
                ApiLogger.warn("assign resource updateResourceAccessSetting error,group="+group+",baseInfo="+recordInfo.getBiz());
                return false;
            }
            int state = 1;
            for (IdcInfo singleIdcInfo: getIdcInfoFromSql) {
                if (group.equals(singleIdcInfo.getGroup())) {
                    String masterIps = dealWithIps(groupConfig.getMaster());
                    String slaveIps = dealWithIps(groupConfig.getSlave());
                    String masterL1Ips = dealWithBlockIps(groupConfig.getMasterL1());
                    String slaveL1Ips = dealWithBlockIps(groupConfig.getSlaveL1());
                    singleIdcInfo.setMasterIps(masterIps);
                    singleIdcInfo.setSlaveIps(slaveIps);
                    singleIdcInfo.setMasterL1Ips(masterL1Ips);
                    singleIdcInfo.setSlaveL1Ips(slaveL1Ips);
                    singleIdcInfo.setState(IDC_STATE);
                    if (!idcInfoDao.updateIdcInfo(singleIdcInfo)) {
                        ApiLogger.warn("assign resource updateIdcInfo error,group="+group+",baseInfo="+recordInfo.getBiz());
                        return false;
                    }
                }
                state &= singleIdcInfo.getState();
            }
            if (state == IDC_STATE) {//若所有机房都分配完毕，整个提案才算分配完成。
                recordInfo.setState(Integer.parseInt(ResourceState.ASSIGN_STATE.toString()));
                if(!resourceDao.updateResource(recordInfo)) {
                    ApiLogger.warn("assign resource updateResource error,group="+group+",baseInfo="+recordInfo.getBiz());
                    return false;
                }
            }
        }
        return true;
    }

    private ResourceAccessSetting buildResourceAccessetting(CacheAgentGroupConfig groupConfig) {
        ResourceAccessSetting resourceAccessSetting = new ResourceAccessSetting();
        resourceAccessSetting.setAutoEjectHosts(groupConfig.getAutoEjecThosts() == true ? 1 : 0);
        resourceAccessSetting.setDistribution(groupConfig.getDistribution());
        resourceAccessSetting.setExptime(groupConfig.getExptime());
        resourceAccessSetting.setForceWriteAll(groupConfig.getForceWriteAll() == true ? 1 : 0);
        resourceAccessSetting.setUpdateSlaveL1(groupConfig.getUpdateSlaveL1() == true ? 1 : 0);
        resourceAccessSetting.setlocalAffinity(groupConfig.getlocalAffinity() == true ? 1 : 0);
        resourceAccessSetting.setFlag(groupConfig.getFlag());
        resourceAccessSetting.setHash(groupConfig.getHash());
        resourceAccessSetting.setHashTag(groupConfig.getHashTag());
        resourceAccessSetting.setPartialReply(groupConfig.getPartialReply() == true ? 1 : 0);
        resourceAccessSetting.setResourceType(groupConfig.getRedis() == true ? 1 : 0);
        resourceAccessSetting.setSaveTag(groupConfig.getSaveTag() == true ? 1 : 0);
        resourceAccessSetting.setServerFailureLimit(groupConfig.getServerFailureLimit());
        resourceAccessSetting.setServerRetryTimeout(groupConfig.getServerRetryTimeout());
        resourceAccessSetting.setTimeout(groupConfig.getTimeout());
        return resourceAccessSetting;
    }

    private String dealWithIps(List<String> ipList) {
        String ips = "";
        if (ipList != null) {
            for (String ip : ipList) {
                ips += (ip + ",");
            }
            if (!ips.equals("")) {
                ips = ips.substring(0, ips.length() - 1);
            }
        }
        return ips;
    }

    private String dealWithBlockIps (List<List<String>> ips) {
        String result = "" ;
        if (ips != null) {
            for (List<String> singleBlockIps : ips) {
                result += dealWithIps(singleBlockIps) + "#";
            }
            if (!result.equals("")) {
                result = result.substring(0,result.length()-1);
            }
        }
        return result;
    }

    @Override
    public String getResource(int id) {
        RecordInfo recordInfo = resourceDao.getResource(id);
        if (recordInfo != null) {
            ResourceAccessSetting resourceAccessSetting = resourceAccessSettingDao.getResourceAccessSetting(recordInfo.getId());
            List<IdcInfo> idcInfos = idcInfoDao.getIdcInfos(recordInfo.getId());
            String groupName = "";
            if (!CollectionUtils.isEmpty(idcInfos)) {
                groupName = idcInfos.get(0).getGroup();
            }
            ServicePoolInfo servicePoolInfo = getServicePoolInfo(getType(groupName));
            BaseInfo baseInfo = generateIdDao.getBaseInfo(recordInfo.getProId());
            return makeRecordInfoToJson(recordInfo, resourceAccessSetting, idcInfos,servicePoolInfo,baseInfo).toString();
        }
        return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_CLUSTER_ID_NOT_EXISTS,"the resource dosen't exist"));
    }

    @Override
    public JsonBuilder getResourceJsonBuilder(int id) {
        RecordInfo recordInfo = resourceDao.getResource(id);
        if (recordInfo != null) {
            ResourceAccessSetting resourceAccessSetting = resourceAccessSettingDao.getResourceAccessSetting(recordInfo.getId());
            List<IdcInfo> idcInfos = idcInfoDao.getIdcInfos(recordInfo.getId());
            String groupName = "";
            if (!CollectionUtils.isEmpty(idcInfos)) {
                groupName = idcInfos.get(0).getGroup();
            }
            ServicePoolInfo servicePoolInfo = getServicePoolInfo(getType(groupName));
            BaseInfo baseInfo = generateIdDao.getBaseInfo(recordInfo.getProId());
            return makeRecordInfoToJson(recordInfo, resourceAccessSetting, idcInfos,servicePoolInfo,baseInfo);
        }
        return CaptainJsonUtil.buildErrorBuilder(new CaptainException(CaptainExcepFactor.E_CLUSTER_ID_NOT_EXISTS,"the resource dosen't exist"));
    }

    @Override
    public String getResourceByPort(Integer port,boolean assigned) {
        JsonBuilder result = new JsonBuilder();
        List<JsonBuilder> list = new ArrayList<JsonBuilder>();
        List<RecordInfo> recordInfos = resourceDao.getResource(assigned);

        List<Integer> ids = new ArrayList<Integer>(recordInfos.size());
        List<Integer> proIds = new ArrayList<Integer>(recordInfos.size());
        if (recordInfos != null && CollectionUtils.isNotEmpty(recordInfos)) {
            for (RecordInfo recordInfo : recordInfos) {
                ids.add(recordInfo.getId());
                proIds.add(recordInfo.getProId());
            }

            List<ResourceAccessSetting> resourceAccessSettings = resourceAccessSettingDao.getResourceAccessSettinglist(ids);
            Map<Integer, ResourceAccessSetting> resourceAccessSettingMap = new HashMap<Integer, ResourceAccessSetting>();
            for (ResourceAccessSetting resourceAccessSetting : resourceAccessSettings) {
                resourceAccessSettingMap.put(resourceAccessSetting.getBackupProId(), resourceAccessSetting);
            }

            List<IdcInfo> idcInfoList = idcInfoDao.getIdcInfoslist(ids);
            Map<Integer, List<IdcInfo>> idcInfoMap = new HashMap<Integer, List<IdcInfo>>();
            for (int i = 0; i < idcInfoList.size(); i++) {
                if (!idcInfoMap.containsKey(idcInfoList.get(i).getBackupProId())) {
                    List<IdcInfo> listTemp = new ArrayList<IdcInfo>();
                    for (int j = 0; j < idcInfoList.size(); j++) {
                        if (idcInfoList.get(j).getBackupProId() == idcInfoList.get(i).getBackupProId()) {
                            listTemp.add(idcInfoList.get(j));
                        }
                    }
                    idcInfoMap.put(idcInfoList.get(i).getBackupProId(), listTemp);
                }
            }

            Set<String> types = new HashSet<String>();
            for (Map.Entry<Integer, List<IdcInfo>> entry : idcInfoMap.entrySet()) {
                List<IdcInfo> idcInfos = entry.getValue();
                if (!CollectionUtils.isEmpty(idcInfos)) {
                    types.add(getType(idcInfos.get(0).getGroup()));
                }
            }

            List<ServicePoolInfo> servicePoolInfos = servicePoolDao.getServicePoolInfolist(new ArrayList<String>(types));
            List<BaseInfo> baseInfos = generateIdDao.getBaseInfolist(proIds);
            Map<Integer, BaseInfo> baseInfoMap = new HashMap<Integer, BaseInfo>();
            for (BaseInfo baseInfo:baseInfos) {
                baseInfoMap.put(baseInfo.getId(), baseInfo);
            }

            for (RecordInfo recordInfo : recordInfos) {
                int id = recordInfo.getId();
                ResourceAccessSetting resourceAccessSetting = resourceAccessSettingMap.get(id);
                List<IdcInfo> idcInfos = idcInfoMap.get(id);
                String groupName = "";
                if (!CollectionUtils.isEmpty(idcInfos)) {
                    groupName = idcInfos.get(0).getGroup();
                }
                if (groupName.equals("")) {//仅仅为了排查问题，完后删除
                    ApiLogger.info("current error id:" + id);
                    continue;
                }
                String type = getType(groupName);
                ServicePoolInfo servicePoolInfo = null;
                for (ServicePoolInfo servicePoolInfoTemp : servicePoolInfos) {
                    if (servicePoolInfoTemp.getType().equals(type)) {
                        servicePoolInfo = servicePoolInfoTemp;
                    }
                }
                int proId = recordInfo.getProId();
                BaseInfo baseInfo = baseInfoMap.get(proId);
                JsonBuilder jb = makeRecordInfoToJson(recordInfo, resourceAccessSetting, idcInfos, servicePoolInfo, baseInfo);
                list.add(jb);
            }
        }
        result.appendJsonArr("result", list);
        return result.flip().toString();
    }

    @Override
    public String getResource(boolean assigned) {
        JsonBuilder result = new JsonBuilder();
        List<JsonBuilder> list = new ArrayList<JsonBuilder>();
        List<RecordInfo> recordInfos = resourceDao.getResource(assigned);

        List<Integer> ids = new ArrayList<Integer>(recordInfos.size());
        List<Integer> proIds = new ArrayList<Integer>(recordInfos.size());
        if (recordInfos != null && CollectionUtils.isNotEmpty(recordInfos)) {
            for (RecordInfo recordInfo : recordInfos) {
                ids.add(recordInfo.getId());
                proIds.add(recordInfo.getProId());
            }

            List<ResourceAccessSetting> resourceAccessSettings = resourceAccessSettingDao.getResourceAccessSettinglist(ids);
            Map<Integer, ResourceAccessSetting> resourceAccessSettingMap = new HashMap<Integer, ResourceAccessSetting>();
            for (ResourceAccessSetting resourceAccessSetting : resourceAccessSettings) {
                resourceAccessSettingMap.put(resourceAccessSetting.getBackupProId(), resourceAccessSetting);
            }

            List<IdcInfo> idcInfoList = idcInfoDao.getIdcInfoslist(ids);
            Map<Integer, List<IdcInfo>> idcInfoMap = new HashMap<Integer, List<IdcInfo>>();
            for (int i = 0; i < idcInfoList.size(); i++) {
                if (!idcInfoMap.containsKey(idcInfoList.get(i).getBackupProId())) {
                    List<IdcInfo> listTemp = new ArrayList<IdcInfo>();
                    for (int j = 0; j < idcInfoList.size(); j++) {
                        if (idcInfoList.get(j).getBackupProId() == idcInfoList.get(i).getBackupProId()) {
                            listTemp.add(idcInfoList.get(j));
                        }
                    }
                    idcInfoMap.put(idcInfoList.get(i).getBackupProId(), listTemp);
                }
            }

            Set<String> types = new HashSet<String>();
            for (Map.Entry<Integer, List<IdcInfo>> entry : idcInfoMap.entrySet()) {
                List<IdcInfo> idcInfos = entry.getValue();
                if (!CollectionUtils.isEmpty(idcInfos)) {
                    types.add(getType(idcInfos.get(0).getGroup()));
                }
            }

            List<ServicePoolInfo> servicePoolInfos = servicePoolDao.getServicePoolInfolist(new ArrayList<String>(types));
            List<BaseInfo> baseInfos = generateIdDao.getBaseInfolist(proIds);
            Map<Integer, BaseInfo> baseInfoMap = new HashMap<Integer, BaseInfo>();
            for (BaseInfo baseInfo:baseInfos) {
                baseInfoMap.put(baseInfo.getId(), baseInfo);
            }

            for (RecordInfo recordInfo : recordInfos) {
                int id = recordInfo.getId();
                ResourceAccessSetting resourceAccessSetting = resourceAccessSettingMap.get(id);
                List<IdcInfo> idcInfos = idcInfoMap.get(id);
                String groupName = "";
                if (!CollectionUtils.isEmpty(idcInfos)) {
                    groupName = idcInfos.get(0).getGroup();
                }
                if (groupName.equals("")) {//仅仅为了排查问题，完后删除
                    ApiLogger.info("current error id:" + id);
                    continue;
                }
                String type = getType(groupName);
                ServicePoolInfo servicePoolInfo = null;
                for (ServicePoolInfo servicePoolInfoTemp : servicePoolInfos) {
                    if (servicePoolInfoTemp.getType().equals(type)) {
                        servicePoolInfo = servicePoolInfoTemp;
                    }
                }
                int proId = recordInfo.getProId();
                BaseInfo baseInfo = baseInfoMap.get(proId);
                JsonBuilder jb = makeRecordInfoToJson(recordInfo, resourceAccessSetting, idcInfos, servicePoolInfo, baseInfo);
                list.add(jb);
            }
        }
        result.appendJsonArr("result", list);
        return result.flip().toString();
    }

    @Transactional(propagation= Propagation.SUPPORTS)
    public String delResource(int id){
        BaseInfo baseInfo = generateIdDao.getBaseInfo(id);
        if (baseInfo == null) {
            ApiLogger.warn("get resource error,not exist.id="+id);
            return makeToJson(false);
        }
        String type = baseInfo.getType();
        String biz = baseInfo.getBiz();
        ServicePoolInfo servicePoolInfo = getServicePoolInfo(type);
        if (servicePoolInfo == null) {
            ApiLogger.warn("get servicePoolInfo error, not exist.type="+type);
            return makeToJson(false);
        }
        String configAddr = servicePoolInfo.getConfigAddr();        
        String idcs = servicePoolInfo.getIdcList();
        StaticConfigBiz staticConfigBiz = (StaticConfigBizImpl) ctx.getBean("staticConfigBiz");
        staticConfigBiz.setBaseUrl(configAddr);
        String []idcArray = idcs.split(",");
        List<String> groups = new ArrayList<String>();
        for (int i = 0; i < idcArray.length; i++) {
            String group = generatorGroup(type, idcArray[i]);
            ApiLogger.info("current idc:" + idcArray[i] + "," + "group:" + group);
            CacheAgentConfig cacheAgentConfig = staticConfigBiz.lookup(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
            if (cacheAgentConfig == null) {
                cacheAgentConfig = staticConfigBiz.lookup(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
            }
            if (cacheAgentConfig != null) {
                List<CacheAgentGroupConfig> groupConfs = cacheAgentConfig.getGroupConfs();
                for (int j = 0; j < groupConfs.size(); j++) {
                    if (groupConfs.get(j).getName().equals(biz)) {
                        groups.add(group);
                        groupConfs.remove(groupConfs.get(j));
                        if (groupConfs.size() == 0) {
                            boolean result = unregister2Vintage(group, configAddr,staticConfigBiz);
                            if (!result) {
                                ApiLogger.warn("unregister2Vintage result=" + result + "group=" + group);
                                return makeToJson(result);
                            }
                        } else {
                            cacheAgentConfig.setGroupConfs(groupConfs);
                            String configStr = cacheAgentConfig.toYamlStr();
                            ApiLogger.info("current register param,group:=" + group + ",configStr:" + configStr);
                            boolean result = register2Vintage(group, configStr, configAddr,staticConfigBiz);
                            if (!result) {
                                ApiLogger.warn("register2Vintage result=" + result + "group=" + group);
                                return makeToJson(result);
                            }
                        }
                        ApiLogger.debug("vintage success");
                        break;
                    }
                }
            }
        }
         
        List<RecordInfo> recordInfos = resourceDao.getResourceById(id);
        List<Integer> ids = new ArrayList<Integer>();
        for (int i = 0; i < recordInfos.size(); i++) {
            RecordInfo recordInfo = recordInfos.get(i);
            ids.add(recordInfo.getId());
        }
       
        if (!ids.isEmpty()) {
            if (!resourceAccessSettingDao.deleteRes(ids)) {
                ApiLogger.warn("del resourceAccessSetting error");
                return makeToJson(false);
            }
            if (!idcInfoDao.deleteIdcs(ids)) {
                ApiLogger.warn("del idcInfo error");
                return makeToJson(false);
            }
        }
        
        if (!resourceDao.deleteRecord(id)) {
            ApiLogger.warn("del record error,id ="+id);
            return makeToJson(false);
        }

        for (int i=0; i<groups.size();i++) {
            if (!configRecordingDao.deleteConfig(groups.get(i),biz)) {
                ApiLogger.warn("del configRecord error,group="+groups.get(i)+",biz="+biz);
                return makeToJson(false);
            }
        }


        if (!generateIdDao.deleteId(id)) {
            ApiLogger.warn("del baseInfo error, id="+id);
            return makeToJson(false);
        }
        
        return makeToJson(true);
    }

    private  JsonBuilder makeRecordInfoToJson(RecordInfo recordInfo,ResourceAccessSetting resourceAccessSetting,List<IdcInfo> idcInfos,ServicePoolInfo servicePoolInfo,BaseInfo baseInfo) {
        JsonBuilder jb = new JsonBuilder();
        jb.append("id", recordInfo.getProId());//提案id
        jb.append("type", baseInfo.getType());
        jb.append("biz", baseInfo.getBiz());
        List<JsonBuilder> list = new ArrayList<JsonBuilder>();
        for (IdcInfo idc:idcInfos) {
            JsonBuilder jBuilder = new JsonBuilder();
            jBuilder.append("idc", idc.getIdc());
            jBuilder.append("group", idc.getGroup());
            jBuilder.append("masterCapacity", idc.getMasterCapacity());
            jBuilder.append("masterL1Capacity", idc.getMasterL1Capacity());
            jBuilder.append("masterL1Block", idc.getMasterL1Block());
            jBuilder.append("masterBandWidth", idc.getMasterBandWidth());
            jBuilder.append("masterL1BandWidth", idc.getMasterL1BandWidth());
            jBuilder.append("slave", idc.getSlave());
            jBuilder.append("slaveL1Idc", idc.getSlaveL1Idc());
            jBuilder.append("masterL1Idc", idc.getMasterL1Idc());
            jBuilder.append("masterIps", idc.getMasterIps());
            jBuilder.append("masterL1Ips", idc.getMasterL1Ips());
            jBuilder.append("slaveIps", idc.getSlaveIps());
            jBuilder.append("slaveL1Ips", idc.getSlaveL1Ips());
            jBuilder.append("reuseIdc", idc.getReuseIdc());
            list.add(jBuilder.flip());
        }
        jb.appendJsonArr("idcInfos", list) ;
        jb.append("hitPercent", recordInfo.getHitPercent());
        jb.append("readTps", recordInfo.getReadTps());
        jb.append("writeTps", recordInfo.getWriteTps());
        jb.append("hash", resourceAccessSetting.getHash());
        jb.append("distribution", resourceAccessSetting.getDistribution());
        jb.append("hashTag", resourceAccessSetting.getHashTag());
        jb.append("saveTag", resourceAccessSetting.getSaveTag());
        jb.append("autoEjectHosts", resourceAccessSetting.getAutoEjectHosts());
        jb.append("timeout", resourceAccessSetting.getTimeout());
        jb.append("resourceType", resourceAccessSetting.getResourceType());
        jb.append("serverRetryTimeout", resourceAccessSetting.getServerRetryTimeout());
        jb.append("serverFailureLimit", resourceAccessSetting.getServerFailureLimit());
        jb.append("partialReply", resourceAccessSetting.getPartialReply());
        jb.append("exptime", resourceAccessSetting.getExptime());
        jb.append("forceWriteAll", resourceAccessSetting.getForceWriteAll());
        jb.append("updateSlaveL1", resourceAccessSetting.getUpdateSlaveL1());
        jb.append("localAffinity", resourceAccessSetting.getlocalAffinity());
        jb.append("flag", resourceAccessSetting.getFlag());
        jb.append("createTime", recordInfo.getCreateTime().toString());
        jb.append("updateTime", recordInfo.getUpdateTime().toString());
        jb.append("state", recordInfo.getState());
        jb.append("applyUser", recordInfo.getApplyUser());
        jb.append("assignUser", recordInfo.getAssignUser());
        jb.append("deployUser", recordInfo.getDeployUser());
        jb.append("rejectUser", recordInfo.getReturnUser());
        if (servicePoolInfo != null) {
            jb.append("proxyState", servicePoolInfo.getProxyState());
            jb.append("configAddr", servicePoolInfo.getConfigAddr());
        }
        return jb.flip();
    }

    private CacheAgentGroupConfig generatorGroupConfig(IdcInfo idcInfo,ResourceAccessSetting resourceAccessSetting) {
        CacheAgentGroupConfig gconf = new CacheAgentGroupConfig();
        String bizName = resourceAccessSetting.getHashTag();
        if(bizName == null){
            ApiLogger.warn(String.format("malformed config for missing hash_tag, config:%s"));
            return null;
        }
        gconf.setName(bizName);
        gconf.setHash(resourceAccessSetting.getHash());
        gconf.setDistribution(resourceAccessSetting.getDistribution());
        gconf.setHashTag(bizName);
        boolean isSaveTag = resourceAccessSetting.getSaveTag() == RESOURCE_ACCESS_DEFAULT_SETTING ? true : false;
        gconf.setSaveTag(isSaveTag);
        boolean isAutoEjectHost = resourceAccessSetting.getAutoEjectHosts() == RESOURCE_ACCESS_DEFAULT_SETTING ? true : false;
        gconf.setAutoEjecThosts(isAutoEjectHost);
        gconf.setTimeout(resourceAccessSetting.getTimeout());
//        gconf.setMasterEffectiveTime(resourceAccessSetting.getMasterEffectiveTime());
//        gconf.setLruTimeout(resourceAccessSetting.getLruTimeout());
        boolean isRedis = resourceAccessSetting.getResourceType() == RESOURCE_ACCESS_DEFAULT_SETTING ? true : false;
        gconf.setRedis(isRedis);
        gconf.setServerRetryTimeout(resourceAccessSetting.getServerRetryTimeout());
        gconf.setServerFailureLimit(resourceAccessSetting.getServerFailureLimit());
        boolean isPartialReply = resourceAccessSetting.getPartialReply() == RESOURCE_ACCESS_DEFAULT_SETTING ? true : false;
        gconf.setPartialReply(isPartialReply);
        gconf.setExptime(resourceAccessSetting.getExptime());
        if(!CaptainUtil.ignoreNewField()){
            boolean forceFlag = resourceAccessSetting.getForceWriteAll() == 1 ? true : false;
            boolean slaveFlag = resourceAccessSetting.getUpdateSlaveL1() == 1 ? true : false;
            boolean localAffinity = resourceAccessSetting.getlocalAffinity() == 1 ? true : false;
            int flag=resourceAccessSetting.getFlag();
            if(!CaptainUtil.ignoreFalseField()){
                gconf.setForceWriteAll(forceFlag);
            }else{
                if(forceFlag){
                    gconf.setForceWriteAll(forceFlag);
                }
            }
            if(!CaptainUtil.ignoreTrueField()){
                gconf.setUpdateSlaveL1(slaveFlag);
            }else{
                if(!slaveFlag){
                    gconf.setUpdateSlaveL1(slaveFlag);
                }
            }
            if(!CaptainUtil.ignoreLocationFalseField()){
                gconf.setlocalAffinity(localAffinity);
            }else{
                if(localAffinity){
                    gconf.setlocalAffinity(localAffinity);
                }
            }
            if (!CaptainUtil.ignoreFlagZeroField()) {
                gconf.setFlag(flag);
            } else {
                if (flag > 0) {
                    gconf.setFlag(flag);
                }
            }
        }
        List<String> masters = createNodes(idcInfo.getMasterIps());
        gconf.setMaster(masters);
        List<String> slaves = createNodes(idcInfo.getSlaveIps());
        gconf.setSlave(slaves);
        if (!StringUtils.isBlank(idcInfo.getMasterL1Ips())) {
            List<List<String>> master_L1s = new ArrayList<List<String>>();
            //不同的l1分组以特殊字符#分隔。
            for (String master_L1_ip: idcInfo.getMasterL1Ips().split("#")) {
                List<String> singleMasterL1 = createNodes(master_L1_ip.trim());
                master_L1s.add(singleMasterL1);
            }
            gconf.setMasterL1(master_L1s);
        }
        if (!StringUtils.isBlank(idcInfo.getSlaveL1Ips())) {
            List<List<String>> slave_L1s = new ArrayList<List<String>>();
            //不同的l1分组以特殊字符#分隔。
            for (String slaveL1: idcInfo.getSlaveL1Ips().split("#")) {
                List<String> singleSlaveL1 = createNodes(slaveL1.trim());
                slave_L1s.add(singleSlaveL1);
            }
            gconf.setSlaveL1(slave_L1s);
        }
        return gconf;
    }
    private List<String> createNodes(String ips) {
        List<String> nodes = new ArrayList<String>();
        if (!StringUtils.isBlank(ips)) {
            for (String nodeIp : ips.split(",")) {
                nodes.add(nodeIp.trim());
            }
        }
        return nodes;
    }

    private String generatorYamlConfig(IdcInfo idcInfo,ResourceAccessSetting resourceAccessSetting,String configAddr) {
        StaticConfigBiz staticConfigBiz = (StaticConfigBizImpl) ctx.getBean("staticConfigBiz");

        String configServer=ThreadLocalUtil.getServer(configAddr);
        staticConfigBiz.setBaseUrl(configServer);
        ApiLogger.info("generatorYamlConfig assign resource get config server success,configServer="+configServer+",old configserver:"+configAddr);

        CacheAgentConfig cacheAgentConfig = staticConfigBiz.lookup(idcInfo.getGroup(), CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
        CacheAgentGroupConfig cacheAgentGroupConfig = generatorGroupConfig(idcInfo,resourceAccessSetting);
        CacheAgentGlobalConfig cacheAgentGlobalConfig = null;
        if (cacheAgentConfig != null) {
            cacheAgentGlobalConfig = cacheAgentConfig.getGlobalConfig();
        } else {
            cacheAgentGlobalConfig = new CacheAgentGlobalConfig();
            cacheAgentGlobalConfig.setGetSignTime(10000);
            cacheAgentGlobalConfig.setHeartBeatTime(2000);
        }
        CacheAgentConfig result = new CacheAgentConfig();
        result.setGlobalConfig(cacheAgentGlobalConfig);
        List<CacheAgentGroupConfig> groupConfs = new ArrayList<CacheAgentGroupConfig>();
        groupConfs.add(cacheAgentGroupConfig);
        result.setGroupConfs(groupConfs);
        return result.toYamlStr();
    }

    private boolean register2Vintage(String group,String configStr, String configAddr, StaticConfigBiz staticConfigBiz) {
        boolean result = false;
        try {
            result = staticConfigBiz.register(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY,configStr);
            if (!result) {
                result = staticConfigBiz.register(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY,configStr);
            }
        } catch (Exception e) {
            ApiLogger.error("resourceServiceImpl reegister2Vintage occur error,group = "+ group,e);
            e.printStackTrace();
        }
        return result;
    }

    private boolean unregister2Vintage(String group,String configAddr, StaticConfigBiz staticConfigBiz) {
        boolean result = false;
        try {
            result = staticConfigBiz.unregister(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
            if (!result) {
                result = staticConfigBiz.unregister(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
            }
        } catch (Exception e) {
            ApiLogger.error("resourceServiceImpl unregister2Vintage error,group = "+ group);
        }
        return result;
    }

    @Override
    public String deployResource(String deployUser,int id) {
        RecordInfo recordInfo = resourceDao.getResource(id);
        if (recordInfo != null) {
            if (recordInfo.getState() == Integer.parseInt(ResourceState.ASSIGN_STATE.toString())) {
                recordInfo.setDeployUser(deployUser);
                recordInfo.setState(Integer.parseInt(ResourceState.DEPLOY_STATE.toString()));
                if (!resourceDao.updateResource(recordInfo)) {
                    return makeToJson(false);
                } else {
                    return makeToJson(true);
                }
            } else {
                return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "The resource is already deployed or not to deploy state"));
            }
        } else {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_RESOURCE_NOT_EXISTS, "The id related information does not exist"));
        }
    }

    @Override
    public String getAllBizStats(String group) {
        String result = null;
        try {
            List<BizStatsCompare> bizSlaStatsList = bizStatsDao.lookup(group);
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            result = gson.toJson(bizSlaStatsList);
        } catch (Exception e) {
            ApiLogger.warn("resourceServiceImpl get all biz stats error:group = "+ group);
        }
        return result;
    }

    @Override
    public String getProxyStats() {
        String result = null;
        try {
            List<ProxyStats> proxyStatsList = proxyStatsDao.lookUpProxyStats();
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            result = gson.toJson(proxyStatsList);
        } catch (Exception e) {
            ApiLogger.warn("resourceServiceImpl get proxy stats error");
        }
        return result;
    }

    @Override
    public String returnResource(int id) {
        boolean result = true;
        RecordInfo recordInfo = resourceDao.getResource(id);
        if (recordInfo == null) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_RESOURCE_NOT_EXISTS, "The id related information does not exist"));
        }
        if (recordInfo.getState() != Integer.parseInt(ResourceState.DEPLOY_STATE.toString())) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "The state of the resource can not be returned "));
        }
        RecordInfo lastRecordInfo = resourceDao.getLastResource(id);
        if (lastRecordInfo == null) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_RESOURCE_NOT_EXISTS, "return error,the id has no related returned information"));
        }
        List<IdcInfo> lastIdcInfos = idcInfoDao.getIdcInfos(lastRecordInfo.getId());
        List<IdcInfo> currentIdcInfos = idcInfoDao.getIdcInfos(recordInfo.getId());
        String group = currentIdcInfos.get(0).getGroup();//取一个group就可以，因为对于同一个type来说不同的group对应的configserver地址是一样的。
        String type = generatorType(group);
        ServicePoolInfo servicePoolInfo = getServicePoolInfo(type);
        StaticConfigBiz staticConfigBiz = (StaticConfigBizImpl) ctx.getBean("staticConfigBiz");

        String configMasterAddr= ThreadLocalUtil.getServer(servicePoolInfo.getConfigAddr());
        staticConfigBiz.setBaseUrl(configMasterAddr);
        ApiLogger.info("return resource,new configMasterAddr:"+configMasterAddr+" old address:"+servicePoolInfo.getConfigAddr());


        List<IdcInfo> commonIdcInfos = new ArrayList<IdcInfo>();
        ResourceAccessSetting resourceAccessSetting = resourceAccessSettingDao.getResourceAccessSetting(lastRecordInfo.getId());
        BaseInfo baseInfo = generateIdDao.getBaseInfo(recordInfo.getProId());
        for (IdcInfo lastIdcInfo : lastIdcInfos) {
            if (currentIdcInfos.contains(lastIdcInfo)) {
                commonIdcInfos.add(lastIdcInfo);
            }
            String registerConfigStr = generatorRegisterConfigStr(staticConfigBiz, recordInfo.getProId(), servicePoolInfo.getConfigAddr(), resourceAccessSetting, lastIdcInfo, baseInfo.getBiz());
            result = register2Vintage(lastIdcInfo.getGroup(), registerConfigStr, servicePoolInfo.getConfigAddr(),staticConfigBiz);
            if (!result) {
                ApiLogger.warn("return resource register2vintage error, group:" + lastIdcInfo.getGroup() + "id:" + id);
                return makeToJson(false+","+lastRecordInfo.getUpdateTime());
            }
        }
        for (IdcInfo currentIdcInfo : currentIdcInfos) {
            if (!commonIdcInfos.contains(currentIdcInfo)) {
                CacheAgentConfig cacheAgentConfigFromConfigServer =
                        staticConfigBiz.lookup(currentIdcInfo.getGroup(), CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
                if (cacheAgentConfigFromConfigServer == null) {
                    cacheAgentConfigFromConfigServer =
                            staticConfigBiz.lookup(currentIdcInfo.getGroup(), CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
                }
                if (cacheAgentConfigFromConfigServer != null) {
                    List<CacheAgentGroupConfig> groupConfs = cacheAgentConfigFromConfigServer.getGroupConfs();
                    for (int i = 0; i < groupConfs.size(); i++) {
                        if (groupConfs.get(i).getName().equals(baseInfo.getBiz())) {
                            groupConfs.remove(i);
                            break;
                        }
                    }
                    if (groupConfs.size() == 0) {
                        result = unregister2Vintage(currentIdcInfo.getGroup(), servicePoolInfo.getConfigAddr(),staticConfigBiz);
                    } else {
                        String registerConfigStr = cacheAgentConfigFromConfigServer.toYamlStr();
                        result = register2Vintage(currentIdcInfo.getGroup(), registerConfigStr, servicePoolInfo.getConfigAddr(),staticConfigBiz);
                    }
                    if (!result) {
                        ApiLogger.warn("return error, group:" + currentIdcInfo.getGroup() + "id:" + id);
                        return makeToJson(false+","+lastRecordInfo.getUpdateTime());
                    }
                }
            }
        }
        recordInfo.setState(Integer.parseInt(ResourceState.REVERT_STATE.toString()));
        result = resourceDao.updateResource(recordInfo);
        if (result) {
            lastRecordInfo.setState(Integer.parseInt(ResourceState.DEPLOY_STATE.toString()));
            result = resourceDao.updateResource(lastRecordInfo);

        }
        return makeToJson(result+","+lastRecordInfo.getUpdateTime());
    }

    private String generatorRegisterConfigStr(StaticConfigBiz staticConfigBiz, int proId, String configAddr,
                                              ResourceAccessSetting resourceAccessSetting, IdcInfo idcInfo, String biz) {
        String configStr = generatorYamlConfig(idcInfo, resourceAccessSetting, configAddr);
        CacheAgentConfig cacheAgentConfig = CacheAgentConfig.parseStr(configStr);
        if (cacheAgentConfig == null) {
            ApiLogger.warn("return resource parse configStr error,group=" + idcInfo.getGroup() + ",configStr=" + configStr);
            return null;
        }
        CacheAgentGroupConfig groupConfig = cacheAgentConfig.getGroupConfs().get(0);
        if (!biz.equals(groupConfig.getName())) {
            ApiLogger.warn("return resource biz inconsistencies,baseInfoBiz=" + biz + ",configStrBiz="
                    + groupConfig.getName());
            return null;
        }
        CacheAgentConfig cacheAgentConfigFromConfigServer =
                staticConfigBiz.lookup(idcInfo.getGroup(), CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
        String registerConfigStr = configStr;
        if (cacheAgentConfigFromConfigServer != null) {
            List<CacheAgentGroupConfig> groupConfs = cacheAgentConfigFromConfigServer.getGroupConfs();
            for (int i = 0; i < groupConfs.size(); i++) {
                if (groupConfs.get(i).getName().equals(biz)) {
                    groupConfs.set(i, groupConfig);
                }
            }
            registerConfigStr = cacheAgentConfigFromConfigServer.toYamlStr();
        }
        return registerConfigStr;
    }

    @Override
    public String addService(String type,int proxyState,String configAddr, String idcArray[]) {
        ServicePoolInfo servicePoolInfo = getServicePoolInfo(type);
        String theTargetIdcList = "";
        List<String> groups = new ArrayList<String>();
        if (servicePoolInfo != null) {
            if (!servicePoolInfo.getConfigAddr().equals(configAddr)) {
                return  CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, type + "'s configAddr inconsistent with the previous,previous is " +servicePoolInfo.getConfigAddr()));
            }
            if (servicePoolInfo.getProxyState() != proxyState) {
                return  CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, type + "'s proxyState inconsistent with the previous,previous is "+ servicePoolInfo.getProxyState()));
            }
            String currentIdcList = servicePoolInfo.getIdcList();
            theTargetIdcList = currentIdcList;
            String currentIdcArray[] = currentIdcList.split(",");
            for (int i=0;i<idcArray.length;i++) {
                for (int j=0;j<currentIdcArray.length;j++) {
                    if (idcArray[i].equals(currentIdcArray[j])) {
                        return  CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, type + "'s idcList "+ idcArray[i]+" already exist"));
                    }
                }
                theTargetIdcList += (","+idcArray[i]);
                String group = generatorGroup(type,idcArray[i]);
                groups.add(group);
            }
        } else {
            for (int i=0;i<idcArray.length;i++) {
                theTargetIdcList += (idcArray[i] + ",");
                String group = generatorGroup(type,idcArray[i]);
                groups.add(group);
            }
            if (!theTargetIdcList.equals("")) {
                theTargetIdcList = theTargetIdcList.substring(0,theTargetIdcList.length()-1);
            }
        }
        StaticConfigBiz staticConfigBiz = (StaticConfigBizImpl) ctx.getBean("staticConfigBiz");

        String configMasterAddr=ThreadLocalUtil.getServer(configAddr);
        staticConfigBiz.setBaseUrl(configMasterAddr);
        ApiLogger.info("addService set config master addr to "+configMasterAddr);


        boolean result = true;
        for (String group: groups) {
            result &= staticConfigBiz.registerCluster(group);
            if (!result) {
                ApiLogger.warn("register cluster error,group="+group);
                return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR,"group:"+group+" register cluster error"));
            }
        }
        if (servicePoolInfo == null) {
            result &= servicePoolDao.addServicePoolInfo(type, proxyState, configAddr,theTargetIdcList);
        } else {
            result &= servicePoolDao.updateServicePoolInfo(type, proxyState, configAddr,theTargetIdcList);
        }

        return makeToJson(result);
    }

    private ServicePoolInfo getServicePoolInfo(String type) {
        ArrayList<String> typeList = new ArrayList<String>();
        typeList.add(type);
        List<ServicePoolInfo> servicePoolInfos = servicePoolDao.getServicePoolInfolist(typeList);
        if (CollectionUtils.isNotEmpty(servicePoolInfos)) {
            return servicePoolInfos.get(0);
        }
        return null;
    }

    @Override
    public String getService() {
        List<ServicePoolInfo> servicePoolInfos = servicePoolDao.getServiceType();
        JsonBuilder jsonBuilder = new JsonBuilder();
        List<JsonBuilder> list = new ArrayList<JsonBuilder>();
        if (!CollectionUtils.isEmpty(servicePoolInfos)) {
            for (ServicePoolInfo servicePoolInfo : servicePoolInfos) {
                JsonBuilder jBuilder = new JsonBuilder();
                jBuilder.append("type", servicePoolInfo.getType());
                jBuilder.append("proxyState", servicePoolInfo.getProxyState());
                jBuilder.append("configAddr", servicePoolInfo.getConfigAddr());
                jBuilder.append("idcList", servicePoolInfo.getIdcList());
                list.add(jBuilder.flip());
            }
        }
        return jsonBuilder.appendJsonArr("result",list).flip().toString();
    }

    @Override
    public String rejectService(int id, String rejectUser) {
        boolean result = false;
        RecordInfo recordInfo = resourceDao.getResource(id);
        if (recordInfo == null) {
            return CaptainJsonUtil.buildErrorJson(
                    new CaptainException(CaptainExcepFactor.E_RESOURCE_NOT_EXISTS, "The id related information does not exist!"));
        }
        recordInfo.setState(Integer.parseInt(ResourceState.REJECT_STATE.toString()));
        recordInfo.setReturnUser(rejectUser);
        result = resourceDao.updateResource(recordInfo);
        return makeToJson(result);
    }

    @Override
    public boolean isExist(String type, String biz) {
        return generateIdDao.isExist(type, biz);
    }

    @Override
    public boolean isExistServicePoolInfo(String type,String idc) {
        ServicePoolInfo servicePoolInfo = getServicePoolInfo(type);
        if (servicePoolInfo != null) {
            String idcList = servicePoolInfo.getIdcList();
            String [] idcArray = idcList.split(",");
            boolean isInIdcArray = false;
            for (int i=0;i<idcArray.length;i++) {
                if (idcArray[i].equals(idc)) {
                    isInIdcArray = true;
                    break;
                }
            }
            if (!isInIdcArray) {
                return false;
            }

        } else {
            return false;
        }
        return true;
    }

    private String generatorGroup(String type, String idc) {
        String group = "cache.service." + type + ".pool." + idc;
        return group;
    }

    public static void main(String[] args) {
    }

    private String getType (String group) {
        return group.split("cache.service.")[1].split(".pool")[0];
    }
}

