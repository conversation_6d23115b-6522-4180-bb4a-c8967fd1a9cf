/**
 * Project Name:captain-assigner File Name:Alarm.java Package
 * Name:com.weibo.api.captain.assigner.stats Date:2016年8月15日下午5:39:19 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.stats;

import java.util.HashMap;
import java.util.Map;

import cn.sina.api.commons.util.ApacheHttpClient;

/**
 * <pre> ClassName:Alarm
 * 
 * 发送报警邮件 Method:POST 和 GET 
 * 通过Team的Name发邮件
 * subject，邮件标题，发邮件的时候用 
 * sv:产品线 
 * service: 产品线下的服务 
 * objectStr:服务下的某个部分 
 * subject: 报警的标题，短信的内容 
 * content: 邮件报警的邮件内容 
 * html: 邮件内容是否是HTML格式的 
 * team: team和mailto有一个必填项 要发的组，多个组之间逗号分隔 
 * mailto: team和mailto有一个必填项 邮件收件人，可在
 * http://connect.monitor.sina.com.cn/v1/common/get_contacts 查看以有联系人 
 * msgto: 短信收件人 Example:
 * /alarm/sendByTeamName?team={team名称}&sv=微博平台&service=data_system&object=process&subject=test&
 * content=test&mailto={邮箱前缀}&msgto={邮箱前缀}"
 * Date: 2016年8月15日 下午5:39:19 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class AlarmUtil {
    private static String url = "http://suic.intra.sina.com.cn:9090/alarm/sendByTeamName";
    private static ApacheHttpClient httpClient = new ApacheHttpClient();

    public static void sendAlarmInfo(String subject, String content, String mailList) {
        Map<String, String> nameValues = new HashMap<String, String>();
        nameValues.put("sv", "微博平台");
        nameValues.put("service", "data_system");
        nameValues.put("object", "process");
        nameValues.put("subject", subject);
        nameValues.put("content", content);
        nameValues.put("mailto", mailList);
        nameValues.put("msgto", mailList);
        httpClient.post(url, nameValues);
    }
}

