/**  
 * Project Name:captain-assigner  
 * File Name:BizSlaStats.java  
 * Package Name:com.weibo.api.captain.assigner.stats  
 * Date:2016年7月16日上午10:33:22  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.assigner.stats;  

/**  
 * <pre>  
 * ClassName:BizSlaStats 
 * 
 * 业务方的sla指标
 * <pre/>   
 * Date:     2016年7月16日 上午10:33:22 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class BizSlaStats {
    String group;
    
    String biz;
    
    float avgTime;

    float  hitPercent;

    long readTps;

    long writeTps;

    int slowRequest;
    
    String createTime;

    public float getAvgTime() {
        return avgTime;
    }

    public void setAvgTime(float avgTime) {
        this.avgTime = avgTime;
    }

    public float getHitPercent() {
        return hitPercent;
    }

    public void setHitPercent(int hitPercent) {
        this.hitPercent = hitPercent;
    }

    public long getReadTps() {
        return readTps;
    }

    public void setReadTps(long readTps) {
        this.readTps = readTps;
    }

    public long getWriteTps() {
        return writeTps;
    }

    public void setWriteTps(long writeTps) {
        this.writeTps = writeTps;
    }

    public int getSlowRequest() {
        return slowRequest;
    }

    public void setSlowRequest(int slowRequest) {
        this.slowRequest = slowRequest;
    }

    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }
}
  
	