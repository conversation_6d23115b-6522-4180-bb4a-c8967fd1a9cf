/**
 * Project Name:captain-assigner File Name:BizStatsCompare.java Package
 * Name:com.weibo.api.captain.assigner.stats Date:2016年7月19日上午11:31:49 Copyright (c) 2016, @weibo
 * All Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.stats;

import java.util.List;

/**
 * <pre> ClassName:BizStatsCompare
 * 
 * 记录biz预期及实际指标 <pre/> Date: 2016年7月19日 上午11:31:49 <br/>
 * 
 * <AUTHOR>
 * @version
 * @see
 */
public class BizStatsCompare {
    String group;
    String biz;
    List<Float> hitPercent;
    List<Long> readTps;
    List<Long> writeTps;
    List<Float> avgTime;
    List<Integer> slowRequest;
    String createTime;

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    public List<Float> getHitPercent() {
        return hitPercent;
    }

    public void setHitPercent(List<Float> hitPercent) {
        this.hitPercent = hitPercent;
    }

    public List<Long> getReadTps() {
        return readTps;
    }

    public void setReadTps(List<Long> readTps) {
        this.readTps = readTps;
    }

    public List<Long> getWriteTps() {
        return writeTps;
    }

    public void setWriteTps(List<Long> writeTps) {
        this.writeTps = writeTps;
    }

    public List<Float> getAvgTime() {
        return avgTime;
    }

    public void setAvgTime(List<Float> avgTime) {
        this.avgTime = avgTime;
    }

    public List<Integer> getSlowRequest() {
        return slowRequest;
    }

    public void setSlowRequest(List<Integer> slowRequest) {
        this.slowRequest = slowRequest;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    
}

