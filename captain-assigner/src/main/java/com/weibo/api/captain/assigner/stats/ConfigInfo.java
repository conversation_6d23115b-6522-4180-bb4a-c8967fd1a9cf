/**  
 * Project Name:captain-assigner  
 * File Name:ConfigInfo.java  
 * Package Name:com.weibo.api.captain.assigner.stats  
 * Date:2016年7月16日上午10:26:27  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.assigner.stats;

import java.util.Set;

import com.weibo.vintage.model.EndpointAddress;

/**  
 * <pre>  
 * ClassName:ConfigInfo 
 * 
 * 从configserver中过滤出来的信息，包含sign，工作节点及不可达节点。
 * <pre/>   
 * Date:     2016年7月16日 上午10:26:27 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class ConfigInfo {
    Set<EndpointAddress> unreachableNodeAddress;

    Set<EndpointAddress> workingNodeAddress;

    String sign;

    public Set<EndpointAddress> getUnreachableNodeAddress() {
        return unreachableNodeAddress;
    }

    public void setUnreachableNodeAddress(Set<EndpointAddress> unreachableNodeAddress) {
        this.unreachableNodeAddress = unreachableNodeAddress;
    }

    public Set<EndpointAddress> getWorkingNodeAddress() {
        return workingNodeAddress;
    }

    public void setWorkingNodeAddress(Set<EndpointAddress> workingNodeAddress) {
        this.workingNodeAddress = workingNodeAddress;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
  
	