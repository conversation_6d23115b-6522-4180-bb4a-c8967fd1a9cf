package com.weibo.api.captain.assigner.stats;

import cn.sina.api.commons.util.ApiLogger;
import com.alibaba.fastjson.JSON;
import com.weibo.api.captain.assigner.dao.GenerateIdDao;
import com.weibo.api.captain.assigner.dao.IdcInfoDao;
import com.weibo.api.captain.assigner.dao.ResourceDao;
import com.weibo.api.captain.assigner.dao.ServicePoolDao;
import com.weibo.api.captain.assigner.model.*;
import com.weibo.api.captain.assigner.util.SinaWatchClient;
import com.weibo.api.captain.assigner.util.Statistic2;
import com.weibo.api.captain.common.util.ThreadLocalUtil;
import com.weibo.api.captain.common.CaptainConstants;
import com.weibo.api.captain.common.model.CacheAgentConfig;
import com.weibo.api.captain.common.model.CacheAgentGroupConfig;
import com.weibo.api.captain.common.service.StaticConfigBiz;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-05-24 20:08
 */
public class ConsistencyCheckTask {
    private final String DETAIL_LINK = "http://cacheservice.intra.weibo.com/1/cluster/formatCheckConsistency";
    private final SinaWatchClient SINA_WATCH_CLIENT = new SinaWatchClient("2018010411", "HMT7UoBqn9XBobb6RTASZb2w6PPnZT");

    private ResourceDao resourceDao;
    private IdcInfoDao idcInfoDao;
    private GenerateIdDao generateIdDao;
    private Statistic2 statistic2;
    private StaticConfigBiz staticConfigBiz;
    private ServicePoolDao servicePoolDao;


    public void notifyConsistencyResult() {
        ApiLogger.info("execute check cacheservice consistency");
        StringBuilder content = new StringBuilder();
        List<IdcInfoConsistencyDiff> dbDiffs = checkIdcInfoConsistency();
        List<IdcInfoConsistencyDiff> dbVintageDiffs = queryAllVintageConfigDiffs();
        content.append("数据库一致性检查结果为：").append(generateTitle(dbDiffs))
                .append("详情地址为：").append(DETAIL_LINK).append("?type=1")
                .append("数据库和vintage一致性检查结果为：").append(generateTitle(dbVintageDiffs))
                .append("详情地址为: ").append(DETAIL_LINK).append("?type=2");

        SinaWatchClient.AlertMessage alertMessage = SinaWatchClient.AlertMessage.of()
                .sv("微博平台")
                .service("captain-cacheservice")
                .subject("captain-一致性检查结果")
                .content(content.toString())
                .receiver("yunpeng7,dba")
                .receiverIsGroup(false)
                .mailto(true)
                .weiboto(false)
                .wechatto(false);
        SINA_WATCH_CLIENT.alert(alertMessage);
    }

    public String formatResult(int type) {
        if (Objects.equals(type, 1)) {
            return convertToString(checkIdcInfoConsistency());
        }
        if (Objects.equals(type, 2)) {
            return convertToVintageDbString(queryAllVintageConfigDiffs());
        }
        return "invalid type";
    }

    public List<IdcInfoConsistencyDiff> checkIdcInfoConsistency() {
        List<IdcInfoConsistencyDiff> consistencyDiffs = new ArrayList();

        List<RecordInfo> resources = resourceDao.getResource(true);
        if (CollectionUtils.isEmpty(resources)) {
            ApiLogger.info("consistency check empty resources!");
            return consistencyDiffs;
        }
        ApiLogger.info("begin to check consistency. total resources size:{}", resources.size());
        for (RecordInfo resource : resources) {
            try {
                List<IdcInfo> idcInfos = idcInfoDao.getIdcInfos(resource.getId());
                if (CollectionUtils.isEmpty(idcInfos)) {
                    continue;
                }
                Map<String, IdcInfo> mapByIdc = new HashMap();
                List<IdcInfo> waitForCheckIdcs = new ArrayList();
                //复用集群配置的，单独放到list当中；非复用的配置，加入map
                for (IdcInfo idcInfo : idcInfos) {
                    if (StringUtils.isBlank(idcInfo.getReuseIdc())) {
                        mapByIdc.put(idcInfo.getIdc(), idcInfo);
                    } else {
                        waitForCheckIdcs.add(idcInfo);
                    }
                }

                if (CollectionUtils.isEmpty(waitForCheckIdcs)) {
                    continue;
                }
                IdcInfoConsistencyDiff resourceDiff = null;
                ApiLogger.info("check consistency, backupProId:{}", resource.getProId());
                for (IdcInfo idcInfo : waitForCheckIdcs) {
                    //遍历复用配置的集群，判断是否存在不一致
                    IdcInfo source = mapByIdc.get(idcInfo.getReuseIdc());
                    if (consistencyCheck(source, idcInfo)) {
                        continue;
                    } else {
                        if (Objects.isNull(resourceDiff)) {
                            resourceDiff = new IdcInfoConsistencyDiff();
                            resourceDiff.setProId(resource.getProId());
                        }
                        //计算不一致的差异信息
                        resourceDiff.getIdcDiffs().add(diffIdcInfo(idcInfo, source));
                    }
                }
                if (resourceDiff != null) {
                    consistencyDiffs.add(resourceDiff);
                }
            } catch (Exception e) {
                ApiLogger.error("consistency check exception.proId=" + resource.getProId(), e);
            }
        }

        if (CollectionUtils.isNotEmpty(consistencyDiffs)) {
            //填充namespace属性
            List<Integer> ids = new ArrayList();
            for (IdcInfoConsistencyDiff diff : consistencyDiffs) {
                ids.add(diff.getProId());
            }
            List<BaseInfo> generateIds = generateIdDao.getBaseInfolist(ids);
            if (CollectionUtils.isEmpty(generateIds) || generateIds.size() != consistencyDiffs.size()) {
                throw new RuntimeException("generateIds查询失败！");
            }
            Map<Integer, String> bizMap = new HashMap();
            for (BaseInfo bi : generateIds) {
                bizMap.put(bi.getId(), bi.getBiz());
            }
            for (IdcInfoConsistencyDiff diff : consistencyDiffs) {
                diff.setNamespace(bizMap.get(diff.getProId()));
            }
            ApiLogger.info("consistencyCheck result:{}", JSON.toJSONString(consistencyDiffs));
        }
        return consistencyDiffs;
    }

    /**
     * 检查原始集群信息，和复用原始集群的信息，是否一致
     *
     * @param source
     * @param reuseIdc
     * @return
     */
    public boolean consistencyCheck(IdcInfo source, IdcInfo reuseIdc) {
        return (Objects.equals(source.getMasterIps(), reuseIdc.getMasterIps()) &&
                Objects.equals(source.getMasterL1Ips(), reuseIdc.getMasterL1Ips()) &&
                Objects.equals(source.getSlaveIps(), reuseIdc.getSlaveIps()) &&
                Objects.equals(source.getSlaveL1Ips(), reuseIdc.getSlaveL1Ips()));
    }


    public List<IdcInfoConsistencyDiff> queryAllVintageConfigDiffs() {
        //1. 查询db当中的集群配置，进行分组
        List<BaseInfo> allBaseInfo = generateIdDao.queryAll();
        List<RecordInfo> allAssignedResources = resourceDao.getResource(true);
        List<ServicePoolInfo> servicePoolInfos = servicePoolDao.getServiceType();

        Map<Integer, BaseInfo> baseInfoMap = new HashMap(allBaseInfo.size());
        for (BaseInfo baseInfo : allBaseInfo) {
            baseInfoMap.put(baseInfo.getId(), baseInfo);
        }
        List<Integer> backupProIds = new ArrayList(allAssignedResources.size());
        Map<Integer, RecordInfo> recordInfoMap = new HashMap(allAssignedResources.size());
        for (RecordInfo recordInfo : allAssignedResources) {
            BaseInfo baseInfo = baseInfoMap.get(recordInfo.getProId());
            backupProIds.add(recordInfo.getId());
            recordInfo.setType(baseInfo.getType());
            recordInfo.setBiz(baseInfo.getBiz());
            recordInfoMap.put(recordInfo.getId(), recordInfo);
        }

        //db当中查询集群信息
        List<IdcInfo> allIdcInfos = idcInfoDao.getIdcInfoslist(backupProIds);
        //key是idcInfo当中的backupProId属性。对应于RecordInfo当中的id属性
        Map<Integer, List<IdcInfo>> groupByBackupProId = new HashMap<Integer, List<IdcInfo>>(backupProIds.size());
        for (IdcInfo idcInfo : allIdcInfos) {
            if (groupByBackupProId.containsKey(idcInfo.getBackupProId())) {
                groupByBackupProId.get(idcInfo.getBackupProId()).add(idcInfo);
            } else {
                List<IdcInfo> idcs = new ArrayList();
                idcs.add(idcInfo);
                groupByBackupProId.put(idcInfo.getBackupProId(), idcs);
            }
        }
        //外层key是RecordInfo，内层key是IdcInfo当中的idc属性（机房信息，例如：tc、yf、aliyun等）
        Map<RecordInfo, Map<String, IdcInfo>> resourceMapIdcs = new HashMap(backupProIds.size());
        for (RecordInfo recordInfo : allAssignedResources) {
            List<IdcInfo> idcs = groupByBackupProId.get(recordInfo.getId());
            Map<String, IdcInfo> mps = new HashMap(idcs.size());
            for (IdcInfo idc : idcs) {
                mps.put(idc.getIdc(), idc);
            }
            resourceMapIdcs.put(recordInfo, mps);
        }

        //2. 查询vintage当中的配置信息，进行分组
        Map<String, Map<String, Map<String, CacheAgentGroupConfig>>> groupVintageConfigs = groupVintageConfigs(servicePoolInfos);

        return diffVintageAndDB(groupVintageConfigs, resourceMapIdcs);
    }

    /**
     * 根据db配置，获取对应vintage当中的配置值
     *
     * @param servicePoolInfos 当前db当中的所有配置信息
     * @return 最外层key：type值（对应generateId表的type属性）
     * 中间层key：namespace值（对应generateId表的biz属性）
     * 最内层的key：idc信息（对应idcInfo的idc值，例如：yf、tc、aliyun等）
     * value: 对应的配置信息
     */
    public Map<String, Map<String, Map<String, CacheAgentGroupConfig>>> groupVintageConfigs(List<ServicePoolInfo> servicePoolInfos) {
        Map<String, Map<String, Map<String, CacheAgentGroupConfig>>> groupVintageConfigs = new HashMap();

        for (ServicePoolInfo servicePoolInfo : servicePoolInfos) {
            Map<String, Map<String, CacheAgentGroupConfig>> groupByBizAndIdc = new HashMap();
            for (String idc : servicePoolInfo.getIdcList().split(",")) {
                String groupId = statistic2.generatorGroup(servicePoolInfo.getType(), idc);
                CacheAgentConfig cacheAgentConfig = null;
                try {

                    String configMasterAddr=ThreadLocalUtil.getServer(servicePoolInfo.getConfigAddr());
                    staticConfigBiz.setBaseUrl(servicePoolInfo.getConfigAddr());
                    ApiLogger.info("groupVintageConfigs,new configMasterAddr:"+configMasterAddr+" old address:"+servicePoolInfo.getConfigAddr());

                    cacheAgentConfig = staticConfigBiz.lookup(groupId, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
                } catch (Exception e) {
                    ApiLogger.warn("query vintage exception! groupId=" + groupId, e);
                    continue;
                }
                if (cacheAgentConfig != null) {
                    //   vintageConfigMap.put(groupId, cacheAgentConfig);

                    for (CacheAgentGroupConfig config : cacheAgentConfig.getGroupConfs()) {
                        //CacheAgentGroupConfig当中的name属性，也是namespace的含义。保存的值和generateId当中的biz一致
                        if (groupByBizAndIdc.containsKey(config.getName())) {
                            groupByBizAndIdc.get(config.getName()).put(idc, config);
                        } else {
                            Map<String, CacheAgentGroupConfig> groupByIdc = new HashMap();
                            groupByIdc.put(idc, config);
                            groupByBizAndIdc.put(config.getName(), groupByIdc);
                        }
                    }
                }
            }
            groupVintageConfigs.put(servicePoolInfo.getType(), groupByBizAndIdc);
        }
        return groupVintageConfigs;
    }

    /**
     * 计算db当中配置，和vintage配置的差异信息
     * @param groupVintageConfigs
     * @param resourceMapIdcs
     * @return
     */
    public List<IdcInfoConsistencyDiff> diffVintageAndDB(Map<String, Map<String, Map<String, CacheAgentGroupConfig>>> groupVintageConfigs, Map<RecordInfo, Map<String, IdcInfo>> resourceMapIdcs) {
        List<IdcInfoConsistencyDiff> diffs = new ArrayList();
        for (RecordInfo resource : resourceMapIdcs.keySet()) {
            String type = resource.getType();
            String namespace = resource.getBiz();
            Map<String, IdcInfo> idcMap = resourceMapIdcs.get(resource);
            if (Objects.isNull(idcMap) || idcMap.isEmpty()) {
                //原始db没有内容
                continue;
            }

            if (Objects.isNull(groupVintageConfigs.get(type)) ||
                    Objects.isNull(groupVintageConfigs.get(type).get(namespace)) ||
                    groupVintageConfigs.get(type).get(namespace).isEmpty()) {
                //vintage不存在对应配置
                ApiLogger.info("vinage当中没有对应配置项！type:{}, namespace:{}", type, namespace);
                continue;
            }
            List<IdcInfoDiff> idcInfoDiffs = new ArrayList();
            for (String idc : idcMap.keySet()) {
                //遍历db当中的配置，和vintage配置对比，计算diff
                CacheAgentGroupConfig vintageConfig = groupVintageConfigs.get(type).get(namespace).get(idc);
                if (Objects.nonNull(vintageConfig)) {
                    //计算差异
                    IdcInfoDiff diff = diffIdcInfo(idcMap.get(idc), vintageConfig);
                    if (isEmptyDiff(diff)) {
                        idcInfoDiffs.add(diff);
                    }
                } else {
                    //vintage当中不存在idc配置, 插入一个targetGroupName为null的结构，拼接字符串的时候单独识别
                    ApiLogger.info("vinage 当中不存在对应配置集群配置！type:{}, namespace:{}, idc:{}", type, namespace, idc);
                    IdcInfoDiff dbVintageDiff = new IdcInfoDiff();
                    dbVintageDiff.setSourceGroupName(idcMap.get(idc).getGroup());
                    dbVintageDiff.setTargetGroupName(null);
                    idcInfoDiffs.add(dbVintageDiff);
                }
            }
            if (CollectionUtils.isNotEmpty(idcInfoDiffs)) {
                IdcInfoConsistencyDiff consistencyDiff = new IdcInfoConsistencyDiff();
                consistencyDiff.setNamespace(namespace);
                consistencyDiff.setIdcDiffs(idcInfoDiffs);
                diffs.add(consistencyDiff);
            }
        }
        return diffs;
    }

    /**
     * 计算数据库配置，和vintage差异
     *
     * @param dbConfig      数据库配置
     * @param vintageConfig vintage配置
     * @return null：无差异；非null，有差异
     */
    public IdcInfoDiff diffIdcInfo(IdcInfo dbConfig, CacheAgentGroupConfig vintageConfig) {
        IdcInfoDiff diff = new IdcInfoDiff();
        diff.setSourceGroupName(dbConfig.getGroup());
        diff.setTargetGroupName(dbConfig.getGroup());
        if (StringUtils.isNotBlank(dbConfig.getMasterIps())) {
            Set<String> masterIps = new HashSet(Arrays.asList(dbConfig.getMasterIps().split(",")));
            Set<String> vintageMasterIps = new HashSet();
            if (Objects.nonNull(vintageConfig.getMaster()) && CollectionUtils.isNotEmpty(vintageConfig.getMaster())) {
                vintageMasterIps.addAll(vintageConfig.getMaster());
            }
            Subtraction sub = diffIps(masterIps, vintageMasterIps);
            if (CollectionUtils.isNotEmpty(sub.sourceDiffTarget)) {
                diff.setMasterDiffIps(sub.sourceDiffTarget);
            }
            if (CollectionUtils.isNotEmpty(sub.targetDiffSource)) {
                diff.setReverseMasterDiffIps(sub.targetDiffSource);
            }
        }

        if (StringUtils.isNotBlank(dbConfig.getSlaveIps())) {
            Set<String> slaveIps = new HashSet(Arrays.asList(dbConfig.getSlaveIps().split(",")));
            Set<String> vintageSlaveIps = new HashSet();
            if (Objects.nonNull(vintageConfig.getSlave()) && CollectionUtils.isNotEmpty(vintageConfig.getSlave())) {
                vintageSlaveIps.addAll(vintageConfig.getSlave());
            }
            Subtraction sub = diffIps(slaveIps, vintageSlaveIps);
            if (CollectionUtils.isNotEmpty(sub.sourceDiffTarget)) {
                diff.setSlaveDiffIps(sub.sourceDiffTarget);
            }
            if (CollectionUtils.isNotEmpty(sub.targetDiffSource)) {
                diff.setReverseSlaveDiffIps(sub.targetDiffSource);
            }
        }

        if (StringUtils.isNotBlank(dbConfig.getMasterL1Ips())) {
            Set<String> masterL1Ips = splitL1Ips(dbConfig.getMasterL1Ips());
            Set<String> vintageMasterL1Ips = new HashSet();
            if (Objects.nonNull(vintageConfig.getMasterL1()) && CollectionUtils.isNotEmpty(vintageConfig.getMasterL1())) {
                vintageMasterL1Ips = mergeToSet(vintageConfig.getMasterL1());
            }
            Subtraction sub = diffIps(masterL1Ips, vintageMasterL1Ips);
            if (CollectionUtils.isNotEmpty(sub.sourceDiffTarget)) {
                diff.setMasterL1DiffIps(sub.sourceDiffTarget);
            }
            if (CollectionUtils.isNotEmpty(sub.targetDiffSource)) {
                diff.setReverseMasterL1DiffIps(sub.targetDiffSource);
            }
        }


        if (StringUtils.isNotBlank(dbConfig.getSlaveL1Ips())) {
            Set<String> slaveL1Ips = splitL1Ips(dbConfig.getSlaveL1Ips());
            Set<String> vintageSlaveL1Ips = new HashSet();
            if (Objects.nonNull(vintageConfig.getSlaveL1()) && CollectionUtils.isNotEmpty(vintageConfig.getSlaveL1())) {
                vintageSlaveL1Ips = mergeToSet(vintageConfig.getSlaveL1());
            }
            Subtraction sub = diffIps(slaveL1Ips, vintageSlaveL1Ips);
            if (CollectionUtils.isNotEmpty(sub.sourceDiffTarget)) {
                diff.setSlaveL1DiffIps(sub.sourceDiffTarget);
            }
            if (CollectionUtils.isNotEmpty(sub.targetDiffSource)) {
                diff.setReverseSlaveL1DiffIps(sub.targetDiffSource);
            }
        }

        return diff;
    }

    /**
     * 计算两个集群的差异信息
     *
     * @param source
     * @param target
     * @return
     */
    public IdcInfoDiff diffIdcInfo(IdcInfo source, IdcInfo target) {
        IdcInfoDiff diff = new IdcInfoDiff();
        diff.setSourceGroupName(source.getGroup());
        diff.setTargetGroupName(target.getGroup());
        if (!Objects.equals(source.getMasterIps(), target.getMasterIps())) {
            Subtraction sub = diffIps(source.getMasterIps(), target.getMasterIps());
            if (CollectionUtils.isNotEmpty(sub.sourceDiffTarget)) {
                diff.setMasterDiffIps(sub.sourceDiffTarget);
            }
            if (CollectionUtils.isNotEmpty(sub.targetDiffSource)) {
                diff.setReverseMasterDiffIps(sub.targetDiffSource);
            }
        }
        if (!Objects.equals(source.getSlaveIps(), target.getSlaveIps())) {
            Subtraction sub = diffIps(source.getSlaveIps(), target.getSlaveIps());
            if (CollectionUtils.isNotEmpty(sub.sourceDiffTarget)) {
                diff.setSlaveDiffIps(sub.sourceDiffTarget);
            }
            if (CollectionUtils.isNotEmpty(sub.targetDiffSource)) {
                diff.setReverseSlaveDiffIps(sub.targetDiffSource);
            }
        }
        if (!Objects.equals(source.getMasterL1Ips(), target.getMasterL1Ips())) {
            Subtraction sub = diffIps(source.getMasterL1Ips(), target.getMasterL1Ips());
            if (CollectionUtils.isNotEmpty(sub.sourceDiffTarget)) {
                diff.setMasterL1DiffIps(sub.sourceDiffTarget);
            }
            if (CollectionUtils.isNotEmpty(sub.targetDiffSource)) {
                diff.setReverseMasterL1DiffIps(sub.targetDiffSource);
            }
        }
        if (!Objects.equals(source.getSlaveL1Ips(), target.getSlaveL1Ips())) {
            Subtraction sub = diffIps(source.getSlaveL1Ips(), target.getSlaveL1Ips());
            if (CollectionUtils.isNotEmpty(sub.sourceDiffTarget)) {
                diff.setSlaveDiffIps(sub.sourceDiffTarget);
            }
            if (CollectionUtils.isNotEmpty(sub.targetDiffSource)) {
                diff.setReverseSlaveL1DiffIps(sub.targetDiffSource);
            }
        }
        return diff;
    }

    /**
     * target和source的差集
     *
     * @param source
     * @param target
     * @return
     */
    public Subtraction diffIps(String source, String target) {
        Set<String> sourceIps = new HashSet(Arrays.asList(source.split(",")));
        Set<String> targetIps = new HashSet(Arrays.asList(target.split(",")));
        return diffIps(sourceIps, targetIps);
    }

    /**
     * arget和source的差集s
     *
     * @param sourceIps
     * @param targetIps
     * @return
     */
    private Subtraction diffIps(Set<String> sourceIps, Set<String> targetIps) {
        Set<String> intersection = new HashSet(sourceIps);
        intersection.retainAll(targetIps);

        Set<String> sourceDiffTarget = new HashSet(sourceIps);
        sourceDiffTarget.removeAll(intersection);

        Set<String> targetDiffSource = new HashSet(targetIps);
        targetDiffSource.removeAll(intersection);
        return new Subtraction(sourceDiffTarget, targetDiffSource);
    }

    private String generateTitle(List<IdcInfoConsistencyDiff> diffs) {
        StringBuilder sb = new StringBuilder();

        int namespaceTotalCount = generateIdDao.queryTotalCount();

        BigDecimal diffNums = new BigDecimal(String.valueOf(diffs.size()));
        BigDecimal totalCount = new BigDecimal(String.valueOf(namespaceTotalCount));
        BigDecimal scale = new BigDecimal("100");
        BigDecimal percent = diffNums.divide(totalCount, 6, RoundingMode.HALF_UP)
                .multiply(scale)
                .setScale(2, RoundingMode.HALF_UP);

        sb.append("当前总共有").append(namespaceTotalCount).append("个namespace, 其中存在不一致的namespace一共是")
                .append(diffs.size()).append("个, 占比为：").append(percent.toString()).append("%. ");
        return sb.toString();
    }


    private String convertToVintageDbString(List<IdcInfoConsistencyDiff> diffs) {
        StringBuilder sb = new StringBuilder();
        sb.append(generateTitle(diffs));

        if (CollectionUtils.isEmpty(diffs)) {
            return sb.toString();
        }
        for (IdcInfoConsistencyDiff idcInfoConsistencyDiff : diffs) {
            ApiLogger.info("Consistency diff: {}", JSON.toJSONString(idcInfoConsistencyDiff));
            if (CollectionUtils.isNotEmpty(idcInfoConsistencyDiff.getIdcDiffs())) {
                sb.append("\n namespace: ").append(idcInfoConsistencyDiff.getNamespace())
                        .append(" vintage和数据库集群配置存在不一致！");
                for (IdcInfoDiff diff : idcInfoConsistencyDiff.getIdcDiffs()) {
                    ApiLogger.info("Diff idcInfo:{}", JSON.toJSONString(diff));
                    if (StringUtils.isBlank(diff.getTargetGroupName())){
                        sb.append("\n\t 数据库集群: [").append(diff.getSourceGroupName()).append("], 不存在对应的vintage配置");
                        continue;
                    }
                    sb.append("\n\t 数据库集群：[").append(diff.getSourceGroupName()).append("], vintage集群:[")
                            .append(diff.getTargetGroupName()).append("]配置, 差异信息：")
                            .append("\n\t\t 仅存在于数据库集群:[").append(diff.getSourceGroupName()).append("]当中的ip：");
                    if (CollectionUtils.isNotEmpty(diff.getMasterDiffIps())) {
                        sb.append("\n\t\t\t masterDiffIps:").append(JSON.toJSONString(diff.getMasterDiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getSlaveDiffIps())) {
                        sb.append("\n\t\t\t slaveDiffIps:").append(JSON.toJSONString(diff.getSlaveDiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getMasterL1DiffIps())) {
                        sb.append("\n\t\t\t masterL1DiffIps:").append(JSON.toJSONString(diff.getMasterL1DiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getSlaveL1DiffIps())) {
                        sb.append("\n\t\t\t slaveL1Ips:").append(JSON.toJSONString(diff.getSlaveL1DiffIps()));
                    }

                    sb.append("\n\t\t 仅存在于vintage：[").append(diff.getTargetGroupName()).append("]当中的ip: ");
                    if (CollectionUtils.isNotEmpty(diff.getReverseMasterDiffIps())) {
                        sb.append("\n\t\t\t masterDiffIps:").append(JSON.toJSONString(diff.getReverseMasterDiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getReverseSlaveDiffIps())) {
                        sb.append("\n\t\t\t slaveDiffIps:").append(JSON.toJSONString(diff.getReverseSlaveDiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getReverseMasterL1DiffIps())) {
                        sb.append("\n\t\t\t masterL1DiffIps:").append(JSON.toJSONString(diff.getReverseMasterL1DiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getReverseSlaveL1DiffIps())) {
                        sb.append("\n\t\t\t slaveL1DiffIps:").append(JSON.toJSONString(diff.getReverseSlaveL1DiffIps()));
                    }
                }
            }
        }
        return sb.toString();

    }

    private String convertToString(List<IdcInfoConsistencyDiff> diffs) {
        StringBuilder sb = new StringBuilder();
        sb.append(generateTitle(diffs));

        if (CollectionUtils.isEmpty(diffs)) {
            return sb.toString();
        }
        for (IdcInfoConsistencyDiff idcInfoConsistencyDiff : diffs) {
            ApiLogger.info("Consistency diff: {}", JSON.toJSONString(idcInfoConsistencyDiff));
            if (CollectionUtils.isNotEmpty(idcInfoConsistencyDiff.getIdcDiffs())) {
                sb.append("\n namespace: ").append(idcInfoConsistencyDiff.getNamespace())
                        .append(" 复用集群信息存在不一致！");
                for (IdcInfoDiff diff : idcInfoConsistencyDiff.getIdcDiffs()) {
                    ApiLogger.info("Diff idcInfo:{}", JSON.toJSONString(diff));
                    sb.append("\n\t 集群：[").append(diff.getSourceGroupName()).append("], 复用集群:[")
                            .append(diff.getTargetGroupName()).append("]配置, 差异信息：")
                            .append("\n\t\t 仅存在于集群:[").append(diff.getSourceGroupName()).append("]当中的ip：");
                    if (CollectionUtils.isNotEmpty(diff.getMasterDiffIps())) {
                        sb.append("\n\t\t\t masterDiffIps:").append(JSON.toJSONString(diff.getMasterDiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getSlaveDiffIps())) {
                        sb.append("\n\t\t\t slaveDiffIps:").append(JSON.toJSONString(diff.getSlaveDiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getMasterL1DiffIps())) {
                        sb.append("\n\t\t\t masterL1DiffIps:").append(JSON.toJSONString(diff.getMasterL1DiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getSlaveL1DiffIps())) {
                        sb.append("\n\t\t\t slaveL1Ips:").append(JSON.toJSONString(diff.getSlaveL1DiffIps()));
                    }

                    sb.append("\n\t\t 仅存在于集群：[").append(diff.getTargetGroupName()).append("]当中的ip: ");
                    if (CollectionUtils.isNotEmpty(diff.getReverseMasterDiffIps())) {
                        sb.append("\n\t\t\t masterDiffIps:").append(JSON.toJSONString(diff.getReverseMasterDiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getReverseSlaveDiffIps())) {
                        sb.append("\n\t\t\t slaveDiffIps:").append(JSON.toJSONString(diff.getReverseSlaveDiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getReverseMasterL1DiffIps())) {
                        sb.append("\n\t\t\t masterL1DiffIps:").append(JSON.toJSONString(diff.getReverseMasterL1DiffIps()));
                    }
                    if (CollectionUtils.isNotEmpty(diff.getReverseSlaveL1DiffIps())) {
                        sb.append("\n\t\t\t slaveL1DiffIps:").append(JSON.toJSONString(diff.getReverseSlaveL1DiffIps()));
                    }
                }
            }
        }
        return sb.toString();
    }


    private Set<String> splitL1Ips(String l1Ips) {
        Set<String> ret = new HashSet();
        if (l1Ips.contains("#")) {
            String[] lines = l1Ips.split("#");
            for (String line : lines) {
                ret.addAll(Arrays.asList(line.split(",")));
            }
        } else {
            ret.addAll(Arrays.asList(l1Ips.split(",")));
        }
        return ret;
    }

    private Set<String> mergeToSet(List<List<String>> ips) {
        Set<String> set = new HashSet();
        if (CollectionUtils.isEmpty(ips)) {
            return set;
        }
        for (List<String> line : ips) {
            if (CollectionUtils.isNotEmpty(line)) {
                set.addAll(line);
            }
        }
        return set;
    }


    private boolean isEmptyDiff(IdcInfoDiff diff) {
        if (CollectionUtils.isNotEmpty(diff.getMasterDiffIps())) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(diff.getSlaveDiffIps())) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(diff.getMasterL1DiffIps())) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(diff.getSlaveL1DiffIps())) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(diff.getReverseMasterDiffIps())) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(diff.getReverseSlaveDiffIps())) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(diff.getReverseMasterL1DiffIps())) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(diff.getReverseSlaveL1DiffIps())) {
            return true;
        }
        return false;
    }

    public ResourceDao getResourceDao() {
        return resourceDao;
    }

    public void setResourceDao(ResourceDao resourceDao) {
        this.resourceDao = resourceDao;
    }

    public IdcInfoDao getIdcInfoDao() {
        return idcInfoDao;
    }

    public void setIdcInfoDao(IdcInfoDao idcInfoDao) {
        this.idcInfoDao = idcInfoDao;
    }

    public GenerateIdDao getGenerateIdDao() {
        return generateIdDao;
    }

    public void setGenerateIdDao(GenerateIdDao generateIdDao) {
        this.generateIdDao = generateIdDao;
    }

    static class Subtraction {
        private Set<String> sourceDiffTarget;
        private Set<String> targetDiffSource;

        public Set<String> getSourceDiffTarget() {
            return sourceDiffTarget;
        }

        public Subtraction(Set<String> sourceDiffTarget, Set<String> targetDiffSource) {
            this.sourceDiffTarget = sourceDiffTarget;
            this.targetDiffSource = targetDiffSource;
        }
    }


    public Statistic2 getStatistic2() {
        return statistic2;
    }

    public void setStatistic2(Statistic2 statistic2) {
        this.statistic2 = statistic2;
    }

    public StaticConfigBiz getStaticConfigBiz() {
        return staticConfigBiz;
    }

    public void setStaticConfigBiz(StaticConfigBiz staticConfigBiz) {
        this.staticConfigBiz = staticConfigBiz;
    }


    public ServicePoolDao getServicePoolDao() {
        return servicePoolDao;
    }

    public void setServicePoolDao(ServicePoolDao servicePoolDao) {
        this.servicePoolDao = servicePoolDao;
    }

}
