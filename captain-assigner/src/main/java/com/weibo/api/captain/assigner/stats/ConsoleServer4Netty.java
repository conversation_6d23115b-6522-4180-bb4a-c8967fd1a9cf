package com.weibo.api.captain.assigner.stats;

import java.net.InetSocketAddress;
import java.util.concurrent.Executors;

import org.jboss.netty.bootstrap.ServerBootstrap;
import org.jboss.netty.channel.Channel;
import org.jboss.netty.channel.ChannelFactory;
import org.jboss.netty.channel.ChannelPipeline;
import org.jboss.netty.channel.ChannelPipelineFactory;
import org.jboss.netty.channel.Channels;
import org.jboss.netty.channel.socket.nio.NioServerSocketChannelFactory;
import org.jboss.netty.handler.codec.frame.DelimiterBasedFrameDecoder;
import org.jboss.netty.handler.codec.frame.Delimiters;
import org.jboss.netty.handler.codec.string.StringDecoder;
import org.jboss.netty.handler.codec.string.StringEncoder;
import org.jboss.netty.util.CharsetUtil;

import cn.sina.api.commons.util.ApiLogger;

public class ConsoleServer4Netty {

    private int serverPort;
    private Channel channel;
    
    public void start() throws Exception {
        ChannelFactory factory = new NioServerSocketChannelFactory(
            Executors.newCachedThreadPool(),
            Executors.newCachedThreadPool());
        ServerBootstrap bootstrap = new ServerBootstrap(factory);
        bootstrap.setOption("tcpNoDelay", true);
        bootstrap.setOption("sendBufferSize", 4 * 1024 * 1024);
        bootstrap.setOption("receiveBufferSize", 4 * 1024 * 1024);
        bootstrap.setOption("child.tcpNoDelay", true);
        bootstrap.setOption("child.keepAlive", true);
        bootstrap.setOption("child.sendBufferSize", 4 * 1024 * 1024);
        bootstrap.setOption("child.receiveBufferSize", 4 * 1024 * 1024);
    
        bootstrap.setPipelineFactory(new ChannelPipelineFactory() {
            public ChannelPipeline getPipeline() {
                ChannelPipeline pipeline = Channels.pipeline();
                pipeline.addLast("frameDecoder", new DelimiterBasedFrameDecoder(Integer.MAX_VALUE, Delimiters.lineDelimiter()));
                pipeline.addLast("stringDecoder", new StringDecoder(CharsetUtil.UTF_8));
                pipeline.addLast("stringEncoder", new StringEncoder(CharsetUtil.UTF_8));
                pipeline.addLast("handler", new NettyConsoleHandler());
                return pipeline;
            }
        });
    
        channel = bootstrap.bind(new InetSocketAddress(serverPort));
    
        ApiLogger.info("The processor console server is started on localhost: " + serverPort);
    }
    
    public void shutdown() {
        if (channel != null) {
            channel.close();
        }
        ApiLogger.info("The processor console server is shutdown on localhost: " + serverPort);
    }

    public int getServerPort() {
        return serverPort;
    }

    public void setServerPort(int serverPort) {
        this.serverPort = serverPort;
    }
    
    public static void main(String []args) throws Exception {
        ConsoleServer4Netty test = new ConsoleServer4Netty();
        test.serverPort = 8888;
        test.start();
    }
    
}
