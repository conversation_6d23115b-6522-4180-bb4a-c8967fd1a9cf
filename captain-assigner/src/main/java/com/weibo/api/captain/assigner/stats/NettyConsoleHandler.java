package com.weibo.api.captain.assigner.stats;

import org.jboss.netty.channel.Channel;
import org.jboss.netty.channel.ChannelHandlerContext;
import org.jboss.netty.channel.ExceptionEvent;
import org.jboss.netty.channel.MessageEvent;
import org.jboss.netty.channel.SimpleChannelUpstreamHandler;

import com.weibo.platform.commons.switcher.Switcher;
import com.weibo.platform.commons.switcher.SwitcherManagerFactoryLoader;

import cn.sina.api.commons.util.ApiLogger;

public class NettyConsoleHandler extends SimpleChannelUpstreamHandler {

    @Override
    public void messageReceived(ChannelHandlerContext ctx, MessageEvent e) throws Exception {
      //处理接受到的控制指令
        String command = e.getMessage().toString().trim();
        Channel channel = e.getChannel();
        if(command == null || command.length() < 1){
            return ;
        }
        try {
            String switcherName = command.split(" ")[2];
            Switcher switcher = SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager().getSwitcher(switcherName);
            if (switcher != null) {
                if (command.startsWith("on")) {
                    switcher.setValue(true);
                    channel.write(switcherName +" true \r\n");     
                } else if (command.startsWith("off")) {
                    switcher.setValue(false);
                    channel.write(switcherName + " false \r\n");
                } else if (command.startsWith("show")) {
                    channel.write(switcherName +" " +switcher.getValue()+" \r\n");
                }
            }
            return;
        } catch (IndexOutOfBoundsException ex) {
            channel.write("command style error,example:show/on/off resource switcherName");
            return ;
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, ExceptionEvent e) throws Exception {
        ApiLogger.error("socket console error:", e.getCause());
    }

}
