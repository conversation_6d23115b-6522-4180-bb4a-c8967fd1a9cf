/**
 * Project Name:captain-assigner File Name:ProxyStats.java Package
 * Name:com.weibo.api.captain.assigner.stats Date:2016年7月16日上午10:23:04 Copyright (c) 2016, @weibo
 * All Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.stats;

import java.util.HashSet;
import java.util.Set;

import com.weibo.vintage.model.EndpointAddress;

/**
 * <pre> ClassName:ProxyStats
 * 
 * 记录proxy的状态，包含不可达节点数量，服务池节点跟前端机节点是否一致，不可用，sign是否一致等 <pre/> Date: 2016年7月16日 上午10:23:04 <br/>
 * 
 * <AUTHOR>
 * @version
 * @see
 */
public class ProxyStats {
    int id;

    String group;

    Set<EndpointAddress> unreachableNodeAddress;

    boolean nodesConsistence;

    Set<EndpointAddress> abnormalNodeAddress;

    Set<EndpointAddress> signConsistenceAddress;

    Set<String> biz;

    private String createTime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public Set<EndpointAddress> getUnreachableNodeAddress() {
        return unreachableNodeAddress;
    }

    public void setUnreachableNodeAddress(Set<EndpointAddress> unreachableNodeAddress) {
        this.unreachableNodeAddress = unreachableNodeAddress;
    }

    public boolean isNodesConsistence() {
        return nodesConsistence;
    }

    public void setNodesConsistence(boolean nodesConsistence) {
        this.nodesConsistence = nodesConsistence;
    }

    public Set<EndpointAddress> getAbnormalNodeAddress() {
        return abnormalNodeAddress;
    }

    public void setAbnormalNodeAddress(Set<EndpointAddress> abnormalNodeAddress) {
        this.abnormalNodeAddress = abnormalNodeAddress;
    }

    public Set<EndpointAddress> getSignConsistenceAddress() {
        return signConsistenceAddress;
    }

    public void setSignConsistenceAddress(Set<EndpointAddress> signConsistenceAddress) {
        this.signConsistenceAddress = signConsistenceAddress;
    }

    public Set<String> getBiz() {
        return biz;
    }

    public void setBiz(Set<String> biz) {
        this.biz = biz;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((abnormalNodeAddress == null) ? 0 : abnormalNodeAddress.hashCode());
        result = prime * result + ((group == null) ? 0 : group.hashCode());
        result = prime * result + (nodesConsistence ? 1231 : 1237);
        result = prime * result + ((signConsistenceAddress == null) ? 0 : signConsistenceAddress.hashCode());
        result = prime * result + ((unreachableNodeAddress == null) ? 0 : unreachableNodeAddress.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        ProxyStats other = (ProxyStats) obj;
        if (abnormalNodeAddress == null) {
            if (other.abnormalNodeAddress != null) {
                return false;
            }
        } else if (!compare2Set(abnormalNodeAddress, other.abnormalNodeAddress)) {
            return false;
        }
        if (group == null) {
            if (other.group != null) {
                return false;
            }
        } else if (!group.equals(other.group)) {
            return false;
        }
        if (nodesConsistence != other.nodesConsistence) {
            return false;
        }
        if (signConsistenceAddress == null) {
            if (other.signConsistenceAddress != null) {
                return false;
            }
        } else if (!compare2Set(signConsistenceAddress, other.signConsistenceAddress)) {
            return false;
        }
        if (unreachableNodeAddress == null) {
            if (other.unreachableNodeAddress != null) {
                return false;
            }
        } else if (!compare2Set(unreachableNodeAddress, other.unreachableNodeAddress)) {
            return false;
        }
        return true;
    }

    private boolean compare2Set(Set<EndpointAddress> nodes, Set<EndpointAddress> comparednodes) {
        if (nodes.size() != comparednodes.size()) {
            return false;
        }
        Set<EndpointAddress> result = new HashSet<EndpointAddress>();
        for (EndpointAddress namingServiceNode : nodes) {
            result.add(namingServiceNode);
        }
        for (EndpointAddress namingServiceNode : comparednodes) {
            result.add(namingServiceNode);
        }
        return result.size() == nodes.size() && result.size() == comparednodes.size();

    }

    public String toString() {
        String separator = "--------------------------------------------------------------------------";
        return "\ngroup:"+group + "\nunreachable nodes:" + unreachableNodeAddress.toString() + "\n nodesConsistence:" + nodesConsistence + "\n abnormal nodes:"
                + abnormalNodeAddress.toString() + "\n sign not Consistence:" + signConsistenceAddress.toString() + "\n abnormal namespaces :" + biz.toString()+"\n"+separator;
    }

}

