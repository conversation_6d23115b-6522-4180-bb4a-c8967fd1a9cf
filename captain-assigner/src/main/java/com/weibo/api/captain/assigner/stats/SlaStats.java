/**  
 * Project Name:captain-assigner  
 * File Name:SlaStats.java  
 * Package Name:com.weibo.api.captain.assigner.stats  
 * Date:2016年7月15日上午10:50:11  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.assigner.stats;

import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.weibo.api.captain.assigner.dao.IdcInfoDao;
import com.weibo.api.captain.assigner.model.IdcInfo;
import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.assigner.service.ResourceService;
import com.weibo.api.captain.common.memcache.CaptainMemcacheClientUtil;
import com.weibo.api.captain.common.model.SLA;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * <pre>  
 * ClassName:SlaStats 
 * 
 * description here!
 * <pre/>   
 * Date:     2016年7月15日 上午10:50:11 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class SlaStats {
    private static ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(1);
    private final static int CONFIG_RELOAD_PERIOD = 1; // unit : seconds
    private IdcInfoDao idcInfoDao;
    
    public void init() {
        scheduledExecutorService.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                getAbnormalEvent();
            }
        }, CONFIG_RELOAD_PERIOD, CONFIG_RELOAD_PERIOD, TimeUnit.SECONDS);   
    }
    
    private void getAbnormalEvent() {
        List<RecordInfo> bizInfos = getBizInfo();
        for (RecordInfo recordInfo:bizInfos) {
            String biz = recordInfo.getBiz();
            SLA sla = new SLA();
            sla.setHitPercent(recordInfo.getHitPercent());
            sla.setReadTps(recordInfo.getReadTps());
            sla.setWriteTps(recordInfo.getWriteTps());
            List<IdcInfo> idcInfoList =  idcInfoDao.getIdcInfos(recordInfo.getId());
            for (IdcInfo idcInfo:idcInfoList) {
                String group = idcInfo.getGroup();
                String masterIpString = idcInfo.getMasterIps();
                SLA actualSlaValue = getActualSlaValue(masterIpString);
                boolean result = compare(sla, actualSlaValue);
                if (result) {
                    System.out.println(biz+","+group+",异常");
                }else{
                    System.out.println(biz+","+group+",正常");
                }
                
            }
        }
        //获取实际值
        //比较如果有异常则输出。
        
    }
    
    //预期值与实际值比较
    private boolean compare(SLA preSlaValue,SLA actualSlaValue) {
        if (preSlaValue.getHitPercent() > actualSlaValue.getHitPercent() || preSlaValue.getReadTps() < actualSlaValue.getReadTps() || preSlaValue.getWriteTps() < actualSlaValue.getWriteTps()) {
            return true;
        }
        return false;
    }
    //获取业务方信息
    private List<RecordInfo> getBizInfo() {
        RecordInfo recordInfo = new RecordInfo();
        recordInfo.setBiz("test");
        String idcInfos = "[{\"idc\":\"tc\",\"group\":\"cache.service.unread.pool.tc\",\"masterCapacity\":10,\"masterL1Capacity\":10,\"masterL1Block\":1,\"bandWidth\":20,\"slave\":\"yf\",\"masterIps\":\"127.0.0.1:11211\",\"masterL1Ips\":\"*************:1234\",\"slaveIps\":\"***********:1234\",\"slaveL1Ips\":\"***********:1234\"}]";
//        recordInfo.setIdcInfos(idcInfos);
//        SLA sla = new SLA();
//        sla.setHitPercent(101);
//        sla.setReadTps(0);
//        recordInfo.setSla(sla);
        List<RecordInfo> recordInfoList = new ArrayList<RecordInfo>();
        recordInfoList.add(recordInfo);
        return recordInfoList;
       // return resourceService1.getResource();
    }
    
    private SLA getActualSlaValue(String ipString) {
        EndpointAddress address = new EndpointAddress(ipString.split(":")[0],Integer.parseInt(ipString.split(":")[1]));
        Map<String, String> statsRs = getStat(address);
        int getHits = Integer.parseInt(statsRs.get("get_hits"));
        int cmdGet = Integer.parseInt(statsRs.get("cmd_get"));
        int hitPercent = getHits/cmdGet *100;
        SLA sla = new SLA();
        sla.setHitPercent(hitPercent);
        return sla;
    }
    
    private Map<String, String> getStat(EndpointAddress address) {
        InetSocketAddress sockAddress = new InetSocketAddress(address.getHost(), address.getPort());
        Map<String, String> statsRs = CaptainMemcacheClientUtil.stats(sockAddress);
        return statsRs;
    }
    
    public static void main(String []args) {
        SlaStats slaStats = new SlaStats();
        //slaStats.init();
        slaStats.getAbnormalEvent();
    }

}
  
	