/**
 * Project Name:captain-assigner File Name:StatisticsTask.java Package
 * Name:com.weibo.api.captain.assigner.stats Date:2016年7月16日上午10:29:22 Copyright (c) 2016, @weibo
 * All Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.stats;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.weibo.api.captain.assigner.dao.BizStatsDao;
import com.weibo.api.captain.assigner.dao.ConfigRecordingDao;
import com.weibo.api.captain.assigner.dao.IdcInfoDao;
import com.weibo.api.captain.assigner.dao.ProxyStatsDao;
import com.weibo.api.captain.assigner.dao.ResourceDao;
import com.weibo.api.captain.assigner.model.IdcInfo;
import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.common.CaptainConstants;
import com.weibo.api.captain.common.memcache.CaptainMemcacheClientUtil;
import com.weibo.api.captain.common.model.SLA;
import com.weibo.api.captain.common.service.NamingServiceBiz;
import com.weibo.api.captain.common.service.StaticConfigBiz;
import com.weibo.api.captain.common.util.CaptainUtil;
import com.weibo.platform.commons.switcher.SwitcherManager;
import com.weibo.platform.commons.switcher.SwitcherManagerFactoryLoader;
import com.weibo.vintage.model.EndpointAddress;
import com.weibo.vintage.model.NamingServiceCluster;
import com.weibo.vintage.model.NamingServiceNode;
import cn.sina.api.commons.util.ApiLogger;

/**
 * <pre> ClassName:StatisticsTask
 * 
 * description here! <pre/> Date: 2016年7月16日 上午10:29:22 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class StatisticsTask {
    private ConfigRecordingDao configRecordingDao;
    private ResourceDao resourceDao;
    private ProxyStatsDao proxyStatsDao;
    private BizStatsDao bizStatsDao;
    private IdcInfoDao idcInfoDao;
    private NamingServiceBiz namingServiceBiz;
    private StaticConfigBiz staticConfigBiz;
    private static String clusterName = "cn.sina.api.commons.cache.MemcacheClient/service";
    private static String baseUrl = "http://apo2.jpool.intra.sina.com.cn/api/openapi/app_pool/applicationpools_ip_list.php?";
    private static int PERIOD = 30; // 5分钟
    // 上一次统计对应的业务及业务sla
    private static Map<EndpointAddress, WorkingNodeStats> lastBizSlaStats = new ConcurrentHashMap<EndpointAddress, WorkingNodeStats>();
    // 获取所有的group及对应的业务方
    public static Map<String, Set<String>> allGroups = new ConcurrentHashMap<String, Set<String>>();
    private final static float AVG_TIME = 1f; // 1ms
    private final static int SLOW_REQUEST = 300;
    private static ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(1);
    private final static String SIGN_INCONSISTENCE = "signInconsistence";
    private final static String ABNORMAL_NODE = "abnormalNode";
    private static final String SWITCHER_CLUSTERMANAGER_ALARM_ALL = "feature.clustermanager.alarm.all";
    private static final String SWITCHER_CLUSTERMANAGER_BIZ_ALARM = "feature.clustermanager.biz.alarm";
    private static final SwitcherManager SWITCH_MANAGER = SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager();

    static {
        SWITCH_MANAGER.registerSwitcher(SWITCHER_CLUSTERMANAGER_ALARM_ALL, false);
        SWITCH_MANAGER.registerSwitcher(SWITCHER_CLUSTERMANAGER_BIZ_ALARM, false);
    }


    public void setResourceDao(ResourceDao resourceDao) {
        this.resourceDao = resourceDao;
    }

    public void setConfigRecordingDao(ConfigRecordingDao configRecordingDao) {
        this.configRecordingDao = configRecordingDao;
    }

    public void setNamingServiceBiz(NamingServiceBiz namingServiceBiz) {
        this.namingServiceBiz = namingServiceBiz;
    }

    public void setProxyStatsDao(ProxyStatsDao proxyStatsDao) {
        this.proxyStatsDao = proxyStatsDao;
    }

    public void setBizStatsDao(BizStatsDao bizStatsDao) {
        this.bizStatsDao = bizStatsDao;
    }

    public void setStaticConfigBiz(StaticConfigBiz staticConfigBiz) {
        this.staticConfigBiz = staticConfigBiz;
    }

    public void setIdcInfoDao(IdcInfoDao idcInfoDao) {
        this.idcInfoDao = idcInfoDao;
    }
    
    private void getAllGroups() {
        allGroups.clear();
        Set<String> groups = configRecordingDao.lookupAllGroups();
        List<RecordInfo> recordInfos = resourceDao.getResource(true);

        // check if empty, empty include null and 0 element.
        if (CollectionUtils.isEmpty(groups)) {
            ApiLogger.warn("get all groups false for not found groups from db");
            return;
        } else if (CollectionUtils.isEmpty(recordInfos)) {
            ApiLogger.warn("get all groups false for not found recordInfos from db");
            return;
        }

        for (String group : groups) {
            Set<String> bizs = new HashSet<String>();
            for (RecordInfo recordInfo : recordInfos) {
                List<IdcInfo> idcInfos = idcInfoDao.getIdcInfos(recordInfo.getId());
                for (IdcInfo idcInfo : idcInfos) {
                    if (group.equals(idcInfo.getGroup())) {
                        bizs.add(recordInfo.getBiz());
                        allGroups.put(group, bizs);
                        break;
                    }
                }
            }
        }
    }

    public void init() {
        getAllGroups();
        statisticAllProxyStats();
        scheduledExecutorService.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                try {
                    getAllGroups();
                    if (allGroups.isEmpty()) {
                        ApiLogger.warn("Ignore statistic proxy for allGroups is empty!");
                        return;
                    }
                    statisticAllProxyStats();
                } catch (Exception e) {
                    // catch loop exception
                    ApiLogger.error("stats proxy false", e);
                }
            }
        }, PERIOD, PERIOD, TimeUnit.SECONDS);
    }

    // 每个业务方都不一样
    private SLA getSla(String biz) {
        List<RecordInfo> recordInfos = resourceDao.getResource(true);
        for (RecordInfo recordInfo : recordInfos) {
            if (biz.equals(recordInfo.getBiz())) {
                SLA sla = new SLA();
                sla.setHitPercent(recordInfo.getHitPercent());
                sla.setReadTps(recordInfo.getReadTps());
                sla.setWriteTps(recordInfo.getWriteTps());
                if (sla.getHitPercent() == 0 && sla.getReadTps() == 0 && sla.getWriteTps() == 0) {// 默认从线上导入的数据是0
                    return null;
                }
                return sla;
            }
        }
        return null;
    }

    // 统计所有的状态：包含proxy及biz
    private void statisticAllProxyStats() {
        ApiLogger.debug("will stats groups: " + allGroups);
        String alarmResult = "";
        for (Entry<String, Set<String>> entry : allGroups.entrySet()) {
            String group = entry.getKey();
            Set<String> bizs = entry.getValue();
            ConfigInfo configInfo = getConfigInfo(group);

            // 先从configInfo获取，如果没有才创建新的
            Set<EndpointAddress> unreachableNodesAddress = configInfo.getUnreachableNodeAddress();
            if (unreachableNodesAddress == null) {
                unreachableNodesAddress = new HashSet<EndpointAddress>();
            }
            Set<EndpointAddress> workingNodesAddress = configInfo.getWorkingNodeAddress();
            if (workingNodesAddress == null) {
                workingNodesAddress = new HashSet<EndpointAddress>();
            }

            // 测试用
            // EndpointAddress test = new EndpointAddress("************",10001);
            // Set<EndpointAddress> workingNodesAddress = new HashSet<EndpointAddress>();
            // workingNodesAddress.add(test);

            String sign = configInfo.getSign();
            if (StringUtils.isBlank(sign)) {
                sign = "";
            }

            int actualNodes = getRealNodeNum(group);
            ProxyStats proxyStats = getProxyStats(unreachableNodesAddress, workingNodesAddress, sign, actualNodes, group, bizs);
            // Proxy异常
            if (proxyStats.getUnreachableNodeAddress().size() != 0 || proxyStats.getAbnormalNodeAddress().size() != 0
                    || !proxyStats.isNodesConsistence() || proxyStats.getSignConsistenceAddress().size() != 0
                    || proxyStats.getBiz().size() != 0) {
//                proxyStats.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//                // 从数据库查询，如果有该group信息，比较现有报警值是否跟上次一致，如果一致，更新时间，如果不一致，添加报警。如果没有该group直接加报警。
//                List<ProxyStats> allProxyStatsFromDB = proxyStatsDao.lookUpProxyStats();
//                if (allProxyStatsFromDB != null) {
//                    boolean isExist = false;
//                    for (ProxyStats singleProxyStats : allProxyStatsFromDB) {
//                        if (singleProxyStats.equals(proxyStats)) {
//                            proxyStats.setId(singleProxyStats.getId());
//                            isExist = true;
//                            proxyStatsDao.updateProxyStats(proxyStats);
//                        }
//                    }
//                    if (!isExist) {
//                        proxyStatsDao.addProxyStats(proxyStats);
//                    }
//                } else {
//                    proxyStatsDao.addProxyStats(proxyStats);
//                }
                alarmResult += proxyStats.toString();
            }
        }
        if (SWITCH_MANAGER.getSwitcher(SWITCHER_CLUSTERMANAGER_ALARM_ALL).isOpen()) {// 报警开关
            if (!alarmResult.equals("")) {
                AlarmUtil.sendAlarmInfo("clusterManager－proxy 异常", alarmResult, "ruihong3");
            }
        }
    }

    private String dealWithBizStats(String group, String biz, BizSlaStats bizSlaStats) {
        String result = "";
        SLA expectSla = getSla(biz);
        List<Float> hitPercent = new ArrayList<Float>();
        List<Long> readTps = new ArrayList<Long>();
        List<Long> writeTps = new ArrayList<Long>();
        List<Float> avgTime = new ArrayList<Float>();
        List<Integer> slowRequest = new ArrayList<Integer>();
        if (expectSla != null) {
            if (bizSlaStats.getHitPercent() < expectSla.getHitPercent() || bizSlaStats.getReadTps() > expectSla.getReadTps()
                    || bizSlaStats.getWriteTps() > expectSla.getWriteTps() || bizSlaStats.getAvgTime() > AVG_TIME
                    || bizSlaStats.getSlowRequest() > SLOW_REQUEST) {
                result = biz;
                bizSlaStats.setGroup(group);
                bizSlaStats.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                hitPercent.add(expectSla.getHitPercent());
                hitPercent.add(bizSlaStats.getHitPercent());
                readTps.add((long) expectSla.getReadTps());
                readTps.add(bizSlaStats.getReadTps());
                writeTps.add((long) expectSla.getWriteTps());
                writeTps.add(bizSlaStats.getWriteTps());
                avgTime.add(AVG_TIME);
                avgTime.add(bizSlaStats.getAvgTime());
                slowRequest.add(SLOW_REQUEST);
                slowRequest.add(bizSlaStats.getSlowRequest());
                BizStatsCompare bizStatsCompare = new BizStatsCompare();
                bizStatsCompare.setBiz(biz);
                bizStatsCompare.setGroup(group);
                bizStatsCompare.setHitPercent(hitPercent);
                bizStatsCompare.setReadTps(readTps);
                bizStatsCompare.setWriteTps(writeTps);
                bizStatsCompare.setAvgTime(avgTime);
                bizStatsCompare.setSlowRequest(slowRequest);
                bizStatsCompare.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                if (bizStatsDao.lookup(group, biz) != null) {
                    bizStatsDao.updateBizStatsCompare(bizStatsCompare);
                } else {
                    bizStatsDao.addBizStatsCompare(bizStatsCompare);
                }
            }
        }
        return result;
    }

    private BizSlaStats getBizSlaStats(String biz, Set<EndpointAddress> workingNodes, Set<EndpointAddress> abnormalNodes) {
        BizSlaStats bizSlaStats = new BizSlaStats();
        float avgTime = 0;
        int hitPercent = 0;
        long readTps = 0;
        long writeTps = 0;
        int slowRequest = 0;
        for (EndpointAddress workingNode : workingNodes) {
            if (abnormalNodes.contains(workingNode)) {// 排除问题节点的请求
                continue;
            }
            InetSocketAddress sockAddress = new InetSocketAddress(workingNode.getHost(), workingNode.getPort());
            try {
                Map<String, String> statsRs = CaptainMemcacheClientUtil.stats(sockAddress, biz);
                if (statsRs.size() == 0) {
                    continue;
                }
                long lastTotalTime = 0;
                long lastTotal = 0;
                long lastHit = 0;
                long lastRead = 0;
                long lastWrite = 0;
                if (lastBizSlaStats.containsKey(workingNode)) {
                    lastTotalTime = lastBizSlaStats.get(workingNode).getTotalTime();
                    lastTotal = lastBizSlaStats.get(workingNode).getTotal();
                    lastHit = lastBizSlaStats.get(workingNode).getHit();
                    lastRead = lastBizSlaStats.get(workingNode).getRead();
                    lastWrite = lastBizSlaStats.get(workingNode).getWrite();
                }
                long currentTotalTime = Long.parseLong(statsRs.get("cluster_" + biz + "_total_time"));
                long currentTotal = Long.parseLong(statsRs.get("cluster_" + biz + "_total"));
                long currentHit = Long.parseLong(statsRs.get("cluster_" + biz + "_hit"));
                long currentRead = Long.parseLong(statsRs.get("cluster_" + biz + "_read"));
                long currentWrite = Long.parseLong(statsRs.get("cluster_" + biz + "_write"));
                int currentSlowRequest = Integer.parseInt(statsRs.get("cluster_" + biz + "_server_query_slow"));
                float singleNodeAvgTime = (currentTotalTime - lastTotalTime) / CaptainUtil.roundToPositiveDivisor(currentTotal - lastTotal);
                float singleNodeHitPercent = (currentHit - lastHit) / CaptainUtil.roundToPositiveDivisor(currentRead - lastRead);
                long singleNodeReadTps = (currentRead - lastRead) / PERIOD;
                long singleNodeWriteTps = (currentWrite - lastWrite) / PERIOD;
                int singNodeSlowRequest = currentSlowRequest;
                avgTime += singleNodeAvgTime;
                hitPercent += singleNodeHitPercent;
                readTps += singleNodeReadTps;
                writeTps += singleNodeWriteTps;
                slowRequest += singNodeSlowRequest;
                WorkingNodeStats workingNodeStats = new WorkingNodeStats();
                workingNodeStats.setTotalTime(currentTotalTime);
                workingNodeStats.setTotal(currentTotal);
                workingNodeStats.setHit(currentHit);
                workingNodeStats.setRead(currentRead);
                workingNodeStats.setWrite(currentWrite);
                workingNodeStats.setSlow(currentSlowRequest);
                lastBizSlaStats.put(workingNode, workingNodeStats);
            } catch (Exception e) {
                continue;
            }
        }
        avgTime /= CaptainUtil.roundToPositiveDivisor(workingNodes.size());
        hitPercent /= CaptainUtil.roundToPositiveDivisor(workingNodes.size());
        readTps /= CaptainUtil.roundToPositiveDivisor(workingNodes.size());
        writeTps /= CaptainUtil.roundToPositiveDivisor(workingNodes.size());
        slowRequest /= CaptainUtil.roundToPositiveDivisor(workingNodes.size());
        bizSlaStats.setAvgTime(avgTime);
        bizSlaStats.setHitPercent(hitPercent * 100);
        bizSlaStats.setReadTps(readTps);
        bizSlaStats.setWriteTps(writeTps);
        bizSlaStats.setSlowRequest(slowRequest);
        return bizSlaStats;
    }

    private ProxyStats getProxyStats(Set<EndpointAddress> unreachableNodesAddress, Set<EndpointAddress> workingNodesAddress, String sign,
            int actualNodeNums, String group, Set<String> bizs) {
        int unreachableNodeNum = unreachableNodesAddress.size();
        int workingNodeNum = workingNodesAddress.size();
        int NodeNum = unreachableNodeNum + workingNodeNum;
        boolean nodesConsistence = (NodeNum == actualNodeNums ? true : false);

        Map<String, Set<EndpointAddress>> problemsNodes = getProblemsNode(workingNodesAddress, sign);
        Set<String> problemBizs = new HashSet<String>();
        if (SWITCH_MANAGER.getSwitcher(SWITCHER_CLUSTERMANAGER_BIZ_ALARM).isOpen()) {
            for (String biz : bizs) {
                BizSlaStats bizSlaStats = getBizSlaStats(biz, workingNodesAddress, problemsNodes.get(ABNORMAL_NODE));
                String result = dealWithBizStats(group, biz, bizSlaStats);
                if (!result.equals("")) {
                    problemBizs.add(result);
                }
            }
        }

        ProxyStats proxyStats = new ProxyStats();
        proxyStats.setUnreachableNodeAddress(unreachableNodesAddress);
        proxyStats.setNodesConsistence(nodesConsistence);
        proxyStats.setAbnormalNodeAddress(problemsNodes.get(ABNORMAL_NODE));
        proxyStats.setSignConsistenceAddress(problemsNodes.get(SIGN_INCONSISTENCE));
        proxyStats.setGroup(group);
        proxyStats.setBiz(problemBizs);
        return proxyStats;

    }

    // 获取问题节点
    private Map<String, Set<EndpointAddress>> getProblemsNode(Set<EndpointAddress> workingNodes, String sign) {
        Map<String, Set<EndpointAddress>> result = new HashMap<String, Set<EndpointAddress>>();
        Set<EndpointAddress> signInconsistenceNodes = new HashSet<EndpointAddress>();
        Set<EndpointAddress> abnormalNodes = new HashSet<EndpointAddress>();
        for (EndpointAddress workingNode : workingNodes) {
            InetSocketAddress socketAddress = new InetSocketAddress(workingNode.getHost(), workingNode.getPort());
            try {
                Map<String, String> statsRs = CaptainMemcacheClientUtil.stats(socketAddress);
                ApiLogger.debug(String.format("stats line %d for %s", statsRs.size(), workingNode.toString()));
                if (statsRs.size() == 0) {// 节点异常
                    signInconsistenceNodes.add(workingNode);
                    abnormalNodes.add(workingNode);
                } else {
                    if (!statsRs.get("sign").equals(sign)) {
                        signInconsistenceNodes.add(workingNode);
                    }
                }
            } catch (Exception e) {
                signInconsistenceNodes.add(workingNode);
                abnormalNodes.add(workingNode);
            }
        }
        result.put(SIGN_INCONSISTENCE, signInconsistenceNodes);
        result.put(ABNORMAL_NODE, abnormalNodes);
        return result;
    }

    // 通过现有接口查询前端机的机器数 ？
    private int getRealNodeNum(String group) {
        int result = 0;
        String appPool = "";
        if (group.split("\\.")[3].equals("pool")) {
            appPool = group.split("\\.")[2] + "-" + group.split("\\.")[4];
        } else {
            appPool = group.split("\\.")[2] + group.split("\\.")[3] + "-" + group.split("\\.")[5];
        }
        String strURL = baseUrl + "app_pool=openapi_" + appPool + "-docker";
        try {
            URL url = new URL(strURL);
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setRequestMethod("GET");
            httpConn.connect();

            BufferedReader reader = new BufferedReader(new InputStreamReader(httpConn.getInputStream()));
            String line;
            StringBuffer buffer = new StringBuffer();
            while ((line = reader.readLine()) != null) {
                buffer.append(line);
            }
            reader.close();
            httpConn.disconnect();
            result = Integer.parseInt(buffer.toString().split(":")[2].split("<br/>")[0]);
        } catch (Exception e) {
            ApiLogger.warn("statisticstask get real node num error,group=" + group, e);
        }
        return result;
    }

    // congigServer交互
    private ConfigInfo getConfigInfo(String group) {
        NamingServiceCluster clusters = namingServiceBiz.getServerCluster(group, clusterName);
        if (clusters == null) {// 重试一次
            clusters = namingServiceBiz.getServerCluster(group, clusterName);
        }
        ConfigInfo configInfo = new ConfigInfo();
        if (clusters != null) {
            Set<NamingServiceNode> unreachableNodes = clusters.getUnreachableNodes();
            Set<NamingServiceNode> workingNodes = clusters.getWorkingNodes();
            String sign = staticConfigBiz.getSign(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
            if (sign == null) {// 重试一次
                sign = staticConfigBiz.getSign(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
            }
            Set<EndpointAddress> unreachableNodesAddress = new HashSet<EndpointAddress>();
            for (NamingServiceNode unreachableNode : unreachableNodes) {
                unreachableNodesAddress.add(unreachableNode.getAddress());
            }
            Set<EndpointAddress> workingNodesAddress = new HashSet<EndpointAddress>();
            for (NamingServiceNode wokingNode : workingNodes) {
                workingNodesAddress.add(wokingNode.getAddress());
            }
            configInfo.setWorkingNodeAddress(workingNodesAddress);
            configInfo.setUnreachableNodeAddress(unreachableNodesAddress);
            configInfo.setSign(sign);
        }
        return configInfo;
    }

    public static void main(String[] args) throws IOException {
        // String group = "cache.service.friendship.pool.yf";
        // String biz = "graph-attention";
        // Set<String> bizs = new HashSet<String>();
        // bizs.add(biz);
        // allGroups.put(group, bizs);
         ApplicationContext context = new ClassPathXmlApplicationContext("captain-assigner.xml");
         StatisticsTask statisticsTask = (StatisticsTask) context.getBean("statisticsTask");
         statisticsTask.init();

    }
}

