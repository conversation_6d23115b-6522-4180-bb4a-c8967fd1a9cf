/**  
 * Project Name:captain-assigner  
 * File Name:WorkingNodeStats.java  
 * Package Name:com.weibo.api.captain.assigner.stats  
 * Date:2016年7月17日上午12:36:41  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.assigner.stats;  

/**  
 * <pre>  
 * ClassName:WorkingNodeStats 
 * 
 * description here!
 * <pre/>   
 * Date:     2016年7月17日 上午12:36:41 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class WorkingNodeStats {
    long totalTime;
    long total;
    long hit;
    long read;
    long write;
    long slow;
    public long getTotalTime() {
        return totalTime;
    }
    public void setTotalTime(long totalTime) {
        this.totalTime = totalTime;
    }
    public long getTotal() {
        return total;
    }
    public void setTotal(long total) {
        this.total = total;
    }
    public long getHit() {
        return hit;
    }
    public void setHit(long hit) {
        this.hit = hit;
    }
    public long getRead() {
        return read;
    }
    public void setRead(long read) {
        this.read = read;
    }
    public long getWrite() {
        return write;
    }
    public void setWrite(long write) {
        this.write = write;
    }
    public long getSlow() {
        return slow;
    }
    public void setSlow(long slow) {
        this.slow = slow;
    }
    
}
  
	