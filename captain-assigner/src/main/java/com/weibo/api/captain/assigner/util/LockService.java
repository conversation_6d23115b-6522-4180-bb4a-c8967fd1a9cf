package com.weibo.api.captain.assigner.util;

import com.google.common.collect.Maps;

import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class LockService implements ILockService {

    private final Map<String, Lock> lockMap = Maps.newHashMap();

    @Override
    public Lock getLock(String key) {
        Lock lock = lockMap.get(key);
        if (lock == null) {
            synchronized (this) {
                lock = lockMap.get(key);
                if (lock == null) {
                    lock = new ReentrantLock();
                    lockMap.put(key, lock);
                }
            }
        }
        return lock;
    }

}

