package com.weibo.api.captain.assigner.util;

import cn.sina.api.commons.util.ApiLogger;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.ResponseBody;
import okio.Buffer;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URISyntaxException;
import java.security.MessageDigest;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-05-26 14:59
 */
public class SinaWatchClient {
    private static final String DEFAULT_CONTENT_TYPE = "application/x-www-form-urlencoded";
    public static final String DEFAULT_HOST = "iconnect.monitor.sina.com.cn";
    public static final int DEFAULT_PORT = 80;
    public static final int DEFAULT_TIMEOUT = 6;
    private final String kid;
    private final String passwd;
    public String host;
    public int port;
    public int timeout;

    private OkHttpClient httpClient = null;

    public SinaWatchClient(String kid, String passwd) {
        this(DEFAULT_HOST, DEFAULT_PORT, DEFAULT_TIMEOUT, kid, passwd);
    }

    public SinaWatchClient(String host, int port, int timeout, String kid, String passwd) {
        this.host = host;
        this.port = port;
        this.kid = kid;
        this.passwd = passwd;
        this.timeout = timeout;
        httpClient = new OkHttpClient.Builder()
                .connectTimeout(timeout, TimeUnit.SECONDS)
                .readTimeout(timeout, TimeUnit.SECONDS)
                .build();
    }

    public String alert(AlertMessage message) {
        long startTime = System.currentTimeMillis();
        String url = genUrl("/v1/alert/send");
        FormBody.Builder bodyBuilder = new FormBody.Builder();
        for (Map.Entry<String, String> entry : message.toMap().entrySet()) {
            bodyBuilder.add(entry.getKey(), entry.getValue());
        }
        Request request = new Request.Builder().url(url).post(bodyBuilder.build())
                .build();
        request = getAuthedRequest(request);
        String result = call(request);
        ApiLogger.info("time-cost={}, finish send alert messsage: subject={}, result={}",
                System.currentTimeMillis() - startTime, message.subject, result);
        return result;
    }

    public String get(String url) throws IOException, java.net.URISyntaxException {
        String uri = "http://" + this.host + ":" + this.port + url;
        Request request = new Request.Builder().url(uri).build();
        request = getAuthedRequest(request);
        String result = call(request);
        return result;
    }

    private final String call(Request request) {
        long startTime = System.currentTimeMillis();
        try {
            ResponseBody body = httpClient.newCall(request).execute().body();
            return body != null ? body.string() : "";
        } catch (IOException e) {
            long cost = System.currentTimeMillis() - startTime;
            ApiLogger.error("time-cost=" + cost + "failed to call SinaWatch " +
                    request.method() + "url: " + request.url(), e);
        }
        return "";
    }

    private String genUrl(String path) {
        return "http://" + this.host + ":" + this.port + path;
    }

    private Request getAuthedRequest(Request request) {
        Request.Builder newBuilder = request.newBuilder();
        String contentType = request.header("Content-Type");
        if (StringUtils.isBlank(contentType)) {
            contentType = DEFAULT_CONTENT_TYPE;
            newBuilder.addHeader("Content-Type", contentType);
        }
        String contentMd5 = "";
        if (request.body() != null) {
            String bodyString = bodyToString(request);
            contentMd5 = md5(bodyString);
            newBuilder.addHeader("Content-MD5", contentMd5);
        }
        long expireTime = System.currentTimeMillis() / 60 + 60 * 10;
        newBuilder.addHeader("Expires", expireTime + "");

        String method = request.method().toUpperCase();

        // hard coded, make my life easier...
        String encodedHeaders = "";

        String encodedPath = request.url().encodedPath();
        String encodedQuery = request.url().encodedQuery();
        if (StringUtils.isNotBlank(encodedQuery)) {
            encodedPath += "?" + encodedQuery;
        }
        String stringToSign = method + "\n" + contentMd5 + "\n" + contentType + "\n" + expireTime + "\n"
                + encodedHeaders + encodedPath;
        String sign = this.calcSign(stringToSign, this.passwd);
        newBuilder.addHeader("Authorization", "sinawatch " + this.kid + ":" + sign);
        return newBuilder.build();
    }

    private String calcSign(String stringToSign, String passwd) {
        String algorithm = "HmacSHA1";
        try {
            Mac mac = Mac.getInstance(algorithm);
            mac.init(new SecretKeySpec(passwd.getBytes("UTF-8"), algorithm));
            byte[] signature = mac.doFinal(stringToSign.getBytes("UTF-8"));
            signature = Base64.encodeBase64(signature);
            return new String(signature).substring(5, 15);
        } catch (Exception e) {
            throw new RuntimeException("error to calcSign", e);
        }
    }

    private String md5(String body) {
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        try {
            byte[] btInput = body.getBytes();
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str).toLowerCase();
        } catch (Exception e) {
            throw new RuntimeException("error to calc md5", e);
        }
    }

    private static String bodyToString(final Request request) {
        final Request copy = request.newBuilder().build();
        final Buffer buffer = new Buffer();
        try {
            copy.body().writeTo(buffer);
            return buffer.readUtf8();
        } catch (IOException e) {
            throw new RuntimeException("failed to bodyToString", e);
        }
    }


    public static void main(String[] args) throws IOException, URISyntaxException {
        SinaWatchClient sinaWatchClient = new SinaWatchClient("2018010411", "HMT7UoBqn9XBobb6RTASZb2w6PPnZT");
        AlertMessage alertMessage = AlertMessage.of()
                .sv("test_sv")
                .service("test_service")
                .subject("test_subject")
                .content("test_content")
                .receiver("feed-diviner-dev")
                .receiverIsGroup(true)
                .mailto(true)
                .weiboto(true)
                .wechatto(true)
                .autoMerge(0)
                .html(1);
        String result = sinaWatchClient.alert(alertMessage);
        System.out.println(result);
    }

    public static class AlertMessage {
        String sv;
        String service;
        String object = "";
        String subject;
        String content;
        String receiver;
        boolean receiverIsGroup;
        boolean sendByMail = true;
        boolean sendByWeibo = false;
        boolean sendByWechat = false;
        int autoMerge = 0;
        int html = 1;


        public static AlertMessage of() {
            return new AlertMessage();
        }

        public LinkedHashMap<String, String> toMap() {
            LinkedHashMap<String, String> result = Maps.newLinkedHashMap();
            Preconditions.checkArgument(StringUtils.isNotBlank(sv));
            Preconditions.checkArgument(StringUtils.isNotBlank(service));
            Preconditions.checkArgument(StringUtils.isNotBlank(subject));
            Preconditions.checkArgument(StringUtils.isNotBlank(content));
            Preconditions.checkArgument(StringUtils.isNotBlank(receiver));
            result.put("sv", sv);
            result.put("service", service);
            if (object != null) {
                result.put("object", object);
            }
            result.put("subject", subject);
            result.put("content", content);
            String receiverPrefix = receiverIsGroup ? "g" : "";
            if (sendByMail) {
                result.put(receiverPrefix + "mailto", receiver);
            }
            if (sendByWeibo) {
                result.put(receiverPrefix + "weiboto", receiver);
            }
            if (sendByWechat) {
                result.put(receiverPrefix + "wechatto", receiver);
            }
            result.put("auto_merge", String.valueOf(autoMerge));
            result.put("html", String.valueOf(html));
            return result;
        }

        public AlertMessage sv(String sv) {
            this.sv = sv;
            return this;
        }

        public AlertMessage service(String service) {
            this.service = service;
            return this;
        }

        public AlertMessage object(String object) {
            this.object = object;
            return this;
        }

        public AlertMessage subject(String subject) {
            this.subject = subject;
            return this;
        }

        public AlertMessage content(String content) {
            this.content = content;
            return this;
        }


        public AlertMessage receiver(String receiver) {
            this.receiver = receiver;
            return this;
        }

        public AlertMessage receiverIsGroup(boolean receiverIsGroup) {
            this.receiverIsGroup = receiverIsGroup;
            return this;
        }

        public AlertMessage mailto(boolean sendByMail) {
            this.sendByMail = sendByMail;
            return this;
        }

        public AlertMessage weiboto(boolean sendByWeibo) {
            this.sendByWeibo = sendByWeibo;
            return this;
        }

        public AlertMessage wechatto(boolean sendByWechat) {
            this.sendByWechat = sendByWechat;
            return this;
        }

        public AlertMessage autoMerge(int autoMerge) {
            this.autoMerge = autoMerge;
            return this;
        }

        public AlertMessage html(int html) {
            this.html = html;
            return this;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(SinaWatchClient.class);
}
