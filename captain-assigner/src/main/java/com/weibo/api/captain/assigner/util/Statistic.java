/**
 * Project Name:captain-assigner File Name:Statistic.java Package
 * Name:com.weibo.api.captain.assigner.util Date:2016年8月3日下午10:26:23 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.weibo.api.captain.assigner.dao.ConfigRecordingDao;
import com.weibo.api.captain.assigner.dao.GenerateIdDao;
import com.weibo.api.captain.assigner.dao.IdcInfoDao;
import com.weibo.api.captain.assigner.dao.ResourceAccessSettingDao;
import com.weibo.api.captain.assigner.dao.ResourceDao;
import com.weibo.api.captain.assigner.model.ConfigRecording;
import com.weibo.api.captain.assigner.model.IdcInfo;
import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.assigner.model.ResourceAccessSetting;
import com.weibo.api.captain.common.CaptainConstants;
import com.weibo.api.captain.common.memcache.CaptainMemcacheClientUtil;
import com.weibo.api.captain.common.model.CacheAgentConfig;
import com.weibo.api.captain.common.model.CacheAgentGlobalConfig;
import com.weibo.api.captain.common.model.CacheAgentGroupConfig;
import com.weibo.api.captain.common.model.Idc;
import com.weibo.api.captain.common.model.SLA;
import com.weibo.api.captain.common.service.StaticConfigBiz;

/**
 * <pre> ClassName:Statistic
 * 
 * 同步线上数据工具 <pre/> Date: 2016年8月3日 下午10:26:23 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class Statistic {
    private StaticConfigBiz staticConfigBiz;
    private ResourceDao resourceDao;
    private ConfigRecordingDao configRecordingDao;
    private GenerateIdDao generateIdDao;
    private ResourceAccessSettingDao resourceAccessSettingDao;
    private IdcInfoDao idcInfoDao;

    private static Map<String, List<String>> namespaces = new HashMap<String, List<String>>();
    private static String fileName = "/Users/<USER>/Documents/result.txt";

    public static void init() {
        File file = new File(fileName);
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            while ((tempString = reader.readLine()) != null) {
                System.out.println(tempString);
                String key = tempString.split(" ")[0];
                List<String> values = new ArrayList<String>();
                for (int i = 0; i < tempString.split(" ")[1].split(",").length; i++) {
                    values.add(tempString.split(" ")[1].split(",")[i]);
                }
                namespaces.put(key, values);

            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {}
            }
        }
    }

    public void setStaticConfigBiz(StaticConfigBiz staticConfigBiz) {
        this.staticConfigBiz = staticConfigBiz;
    }

    public void setResourceDao(ResourceDao resourceDao) {
        this.resourceDao = resourceDao;
    }

    public void setConfigRecordingDao(ConfigRecordingDao configRecordingDao) {
        this.configRecordingDao = configRecordingDao;
    }

    public void setGenerateIdDao(GenerateIdDao generateIdDao) {
        this.generateIdDao = generateIdDao;
    }

    public void setResourceAccessSettingDao(ResourceAccessSettingDao resourceAccessSettingDao) {
        this.resourceAccessSettingDao = resourceAccessSettingDao;
    }

    public void setIdcInfoDao(IdcInfoDao idcInfoDao) {
        this.idcInfoDao = idcInfoDao;
    }

    private CacheAgentConfig getConfig(String group) {
        CacheAgentConfig cacheAgentConfig = staticConfigBiz.lookup(group, "all");
        if (cacheAgentConfig == null) {
            cacheAgentConfig = staticConfigBiz.lookup(group, "all");
        }
        if (cacheAgentConfig == null) {
            cacheAgentConfig = staticConfigBiz.lookup(group, "all");
        }
        return cacheAgentConfig;
    }

    private String generatorGroup(String type, String idc) {
        String group = "cache.service." + type + ".pool." + idc;
        return group;
    }

    private void manageResource() {
        for (Map.Entry<String, List<String>> entry : namespaces.entrySet()) {
            String type = entry.getKey();
            String groupAliyunTc = generatorGroup(type, Idc.aliyun_tc.name());
            CacheAgentConfig cacheAgentConfigAliyunTc = getConfig(groupAliyunTc);
            String groupAliyun = generatorGroup(type, Idc.aliyun.name());
            CacheAgentConfig cacheAgentConfigAliyun = getConfig(groupAliyun);
            String groupTc = generatorGroup(type, Idc.tc.name());
            CacheAgentConfig cacheAgentConfigTc = getConfig(groupTc);
            String groupYf = generatorGroup(type, Idc.yf.name());
            CacheAgentConfig cacheAgentConfigYf = getConfig(groupYf);
            for (String namespace : entry.getValue()) {
                // 对应机房数
                int machines = 0;
                if (cacheAgentConfigYf != null) {
                    for (CacheAgentGroupConfig groupConf : cacheAgentConfigYf.getGroupConfs()) {
                        if (namespace.equals(groupConf.getName())) {
                            machines += 1;
                            break;
                        }
                    }
                }
                if (cacheAgentConfigTc != null) {
                    for (CacheAgentGroupConfig groupConf : cacheAgentConfigTc.getGroupConfs()) {
                        if (namespace.equals(groupConf.getName())) {
                            machines += 2;
                            break;
                        }
                    }
                }
                if (cacheAgentConfigAliyun != null && cacheAgentConfigAliyunTc != null) {
                    for (CacheAgentGroupConfig groupConf : cacheAgentConfigAliyun.getGroupConfs()) {
                        if (namespace.equals(groupConf.getName())) {
                            machines += 12;
                            break;
                        }
                    }
                }
                // yf 单机房
                if (machines == 1) {
                    int layerMode = 0;
                    String slave = "-1";// 没有备份
                    for (CacheAgentGroupConfig groupConf : cacheAgentConfigYf.getGroupConfs()) {
                        if (namespace.equals(groupConf.getName())) {
                            if (groupConf.getSlave() != null) {
                                layerMode = 1;// 单机房独立主备
                                slave = "yf";
                            }
                            int masterCapacity = getMasterCapacity(groupConf.getMaster());
                            int masterL1Capacity = getMasterL1Capacity(groupConf.getMasterL1());
                            int masterL1Block = getMasterL1Block(groupConf.getMasterL1());
                            String masterIps = dealWithIps(groupConf.getMaster());
                            String slaveIps = dealWithIps(groupConf.getSlave());
                            String masterL1Ips = dealWithBlockIps(groupConf.getMasterL1());
                            String slaveL1Ips = dealWithBlockIps(groupConf.getSlaveL1());
                            String configStr = generatorConfigStr(cacheAgentConfigYf, groupConf);
                            RecordInfo recordInfo = buildRecordInfo(type, groupConf, 0f, 0, 0);
                            List<IdcInfo> idcInfoList = new ArrayList<IdcInfo>();
                            IdcInfo idcInfo = generatorIdcInfo(groupYf, Idc.yf.name(), masterIps, slaveIps, masterL1Ips, slaveL1Ips, slave,
                                    layerMode, masterCapacity, masterL1Capacity, masterL1Block);
                            idcInfoList.add(idcInfo);
                            ResourceAccessSetting resourceAccessSetting = generatorResourceAccessSetting(groupConf);
                            int id = processApply(recordInfo, resourceAccessSetting, idcInfoList);
                            if (!configStr.equals("")) {
                                processConfigRecord(groupYf, groupConf.getName(), configStr, layerMode, slave);
                            }
                            processDeploy("admin", "admin", id);

                        }
                    }
                }
                // tc
                if (machines == 2) {
                    int layerMode = 0;
                    String slave = "-1";// 没有备份
                    for (CacheAgentGroupConfig groupConf : cacheAgentConfigTc.getGroupConfs()) {
                        if (namespace.equals(groupConf.getName())) {
                            if (groupConf.getSlave() != null) {
                                layerMode = 1;
                                slave = "tc";
                            }
                            List<IdcInfo> idcInfoList = new ArrayList<IdcInfo>();
                            int masterCapacity = getMasterCapacity(groupConf.getMaster());
                            int masterL1Capacity = getMasterL1Capacity(groupConf.getMasterL1());
                            int masterL1Block = getMasterL1Block(groupConf.getMasterL1());
                            String masterIps = dealWithIps(groupConf.getMaster());
                            String slaveIps = dealWithIps(groupConf.getSlave());
                            String masterL1Ips = dealWithBlockIps(groupConf.getMasterL1());
                            String slaveL1Ips = dealWithBlockIps(groupConf.getSlaveL1());
                            IdcInfo idcInfo = generatorIdcInfo(groupTc, Idc.tc.name(), masterIps, slaveIps, masterL1Ips, slaveL1Ips, slave,
                                    layerMode, masterCapacity, masterL1Capacity, masterL1Block);
                            idcInfoList.add(idcInfo);
                            String configStr = generatorConfigStr(cacheAgentConfigTc, groupConf);
                            RecordInfo recordInfo = buildRecordInfo(type, groupConf, 0f,0,0);
                            ResourceAccessSetting resourceAccessSetting = generatorResourceAccessSetting(groupConf);
                            int id = processApply(recordInfo,resourceAccessSetting,idcInfoList);
                            if (!configStr.equals("")) {
                                processConfigRecord(groupTc, groupConf.getName(), configStr, layerMode, slave);
                            }
                            processDeploy("admin", "admin", id);

                        }
                    }
                }
                // tc,yf
                if (machines == 3) {
                    int layerModeTc = 0;
                    int layerModeYf = 0;
                    String slaveTc = "-1";// 没有备份
                    String slaveYf = "-1";
                    for (CacheAgentGroupConfig groupConf : cacheAgentConfigTc.getGroupConfs()) {
                        if (namespace.equals(groupConf.getName())) {
                            List<IdcInfo> idcInfoList = new ArrayList<IdcInfo>();
                            int masterCapacity = getMasterCapacity(groupConf.getMaster());
                            int masterL1Capacity = getMasterL1Capacity(groupConf.getMasterL1());
                            int masterL1Block = getMasterL1Block(groupConf.getMasterL1());
                            String masterIps = dealWithIps(groupConf.getMaster());
                            String slaveIps = dealWithIps(groupConf.getSlave());
                            String masterL1Ips = dealWithBlockIps(groupConf.getMasterL1());
                            String slaveL1Ips = dealWithBlockIps(groupConf.getSlaveL1());
                            String configStrTc = generatorConfigStr(cacheAgentConfigTc, groupConf);
                            for (CacheAgentGroupConfig groupConfYf : cacheAgentConfigYf.getGroupConfs()) {
                                if (namespace.equals(groupConfYf.getName())) {
                                    if (groupConf.getSlave() != null) {
                                        if (groupConf.getSlave().contains(groupConfYf.getMaster().get(0))) {
                                            layerModeTc = 2;// 多机房互为主备；
                                            layerModeYf = 2;
                                            slaveTc = "yf";
                                            slaveYf = "tc";
                                        } else {
                                            layerModeTc = 1;// 单机房独立主备；
                                            layerModeYf = 1;
                                            slaveTc = "tc";
                                            slaveYf = "yf";
                                        }
                                    } else if (groupConfYf.getSlave() != null) {
                                        layerModeYf = 1;
                                        slaveYf = "yf";
                                    }
                                    IdcInfo idcInfoTc = generatorIdcInfo(groupTc, Idc.tc.name(), masterIps, slaveIps, masterL1Ips,
                                            slaveL1Ips, slaveTc, layerModeTc, masterCapacity, masterL1Capacity, masterL1Block);
                                    idcInfoList.add(idcInfoTc);
                                    masterCapacity = getMasterCapacity(groupConfYf.getMaster());
                                    masterL1Capacity = getMasterL1Capacity(groupConfYf.getMasterL1());
                                    masterL1Block = getMasterL1Block(groupConfYf.getMasterL1());
                                    masterIps = dealWithIps(groupConfYf.getMaster());
                                    slaveIps = dealWithIps(groupConfYf.getSlave());
                                    masterL1Ips = dealWithBlockIps(groupConfYf.getMasterL1());
                                    slaveL1Ips = dealWithBlockIps(groupConfYf.getSlaveL1());
                                    IdcInfo idcInfoYf = generatorIdcInfo(groupYf, Idc.yf.name(), masterIps, slaveIps, masterL1Ips,
                                            slaveL1Ips, slaveYf, layerModeYf, masterCapacity, masterL1Capacity, masterL1Block);
                                    idcInfoList.add(idcInfoYf);
                                    String configStrYf = generatorConfigStr(cacheAgentConfigYf, groupConfYf);
                                    RecordInfo recordInfo = buildRecordInfo(type, groupConf, 0f,0,0);
                                    ResourceAccessSetting resourceAccessSetting = generatorResourceAccessSetting(groupConf);
                                    int id = processApply(recordInfo,resourceAccessSetting,idcInfoList);
                                    if (!configStrYf.equals("")) {
                                        processConfigRecord(groupYf, groupConf.getName(), configStrYf, layerModeYf, slaveYf);
                                    }
                                    if (!configStrTc.equals("")) {
                                        processConfigRecord(groupTc, groupConf.getName(), configStrTc, layerModeTc, slaveTc);
                                    }
                                    processDeploy("admin", "admin", id);
                                }
                            }

                        }
                    }
                }
                // 四个机房
                if (machines == 15) {
                    int layerMode = 2;// 多机房互为主备
                    String slaveTc = "-1";
                    String slaveYf = "-1";
                    String slaveAliyun = "-1";
                    String slaveAliyunTc = "-1";
                    for (CacheAgentGroupConfig groupConf : cacheAgentConfigTc.getGroupConfs()) {// tc
                        if (namespace.equals(groupConf.getName())) {
                            List<IdcInfo> idcInfoList = new ArrayList<IdcInfo>();
                            int masterCapacity = getMasterCapacity(groupConf.getMaster());
                            int masterL1Capacity = getMasterL1Capacity(groupConf.getMasterL1());
                            int masterL1Block = getMasterL1Block(groupConf.getMasterL1());
                            String masterIps = dealWithIps(groupConf.getMaster());
                            String slaveIps = dealWithIps(groupConf.getSlave());
                            String masterL1Ips = dealWithBlockIps(groupConf.getMasterL1());
                            String slaveL1Ips = dealWithBlockIps(groupConf.getSlaveL1());
                            String configStrTc = generatorConfigStr(cacheAgentConfigTc, groupConf);
                            String configStrYf = "";
                            String configStrAliyun = "";
                            String configStrAliyunTc = "";
                            for (CacheAgentGroupConfig groupConfYf : cacheAgentConfigYf.getGroupConfs()) {
                                if (namespace.equals(groupConfYf.getName())) {// yf
                                    slaveTc = "yf";
                                    slaveYf = "tc";

                                    IdcInfo idcInfoTc = generatorIdcInfo(groupTc, Idc.tc.name(), masterIps, slaveIps, masterL1Ips,
                                            slaveL1Ips, slaveTc, layerMode, masterCapacity, masterL1Capacity, masterL1Block);
                                    idcInfoList.add(idcInfoTc);
                                    masterCapacity = getMasterCapacity(groupConfYf.getMaster());
                                    masterL1Capacity = getMasterL1Capacity(groupConfYf.getMasterL1());
                                    masterL1Block = getMasterL1Block(groupConfYf.getMasterL1());
                                    masterIps = dealWithIps(groupConfYf.getMaster());
                                    slaveIps = dealWithIps(groupConfYf.getSlave());
                                    masterL1Ips = dealWithBlockIps(groupConfYf.getMasterL1());
                                    slaveL1Ips = dealWithBlockIps(groupConfYf.getSlaveL1());
                                    configStrYf = generatorConfigStr(cacheAgentConfigYf, groupConfYf);
                                    IdcInfo idcInfoYf = generatorIdcInfo(groupYf, Idc.yf.name(), masterIps, slaveIps, masterL1Ips,
                                            slaveL1Ips, slaveYf, layerMode, masterCapacity, masterL1Capacity, masterL1Block);
                                    idcInfoList.add(idcInfoYf);
                                }

                            }
                            for (CacheAgentGroupConfig groupConfigAliyunTc : cacheAgentConfigAliyunTc.getGroupConfs()) {
                                if (namespace.equals(groupConfigAliyunTc.getName())) {// aliyuntc
                                    if (groupConfigAliyunTc.getMaster().contains(groupConf.getMaster().get(0))) {// aliyuntc使用tc的配置
                                        slaveAliyun = "tc";
                                        slaveAliyunTc = "yf";
                                    } else {
                                        slaveAliyun = "yf";
                                        slaveAliyunTc = "tc";
                                    }
                                    masterCapacity = getMasterCapacity(groupConfigAliyunTc.getMaster());
                                    masterL1Capacity = getMasterL1Capacity(groupConfigAliyunTc.getMasterL1());
                                    masterL1Block = getMasterL1Block(groupConfigAliyunTc.getMasterL1());
                                    masterIps = dealWithIps(groupConfigAliyunTc.getMaster());
                                    slaveIps = dealWithIps(groupConfigAliyunTc.getSlave());
                                    masterL1Ips = dealWithBlockIps(groupConfigAliyunTc.getMasterL1());
                                    slaveL1Ips = dealWithBlockIps(groupConfigAliyunTc.getSlaveL1());
                                    configStrAliyunTc = generatorConfigStr(cacheAgentConfigYf, groupConfigAliyunTc);
                                    IdcInfo idcInfoAliyunTc =
                                            generatorIdcInfo(groupAliyunTc, Idc.aliyun_tc.name(), masterIps, slaveIps, masterL1Ips,
                                                    slaveL1Ips, slaveAliyunTc, layerMode, masterCapacity, masterL1Capacity, masterL1Block);
                                    idcInfoList.add(idcInfoAliyunTc);
                                    for (CacheAgentGroupConfig groupConfigAliyun : cacheAgentConfigAliyun.getGroupConfs()) {
                                        if (namespace.equals(groupConfigAliyun.getName())) {
                                            masterCapacity = getMasterCapacity(groupConfigAliyun.getMaster());
                                            masterL1Capacity = getMasterL1Capacity(groupConfigAliyun.getMasterL1());
                                            masterL1Block = getMasterL1Block(groupConfigAliyun.getMasterL1());
                                            masterIps = dealWithIps(groupConfigAliyun.getMaster());
                                            slaveIps = dealWithIps(groupConfigAliyun.getSlave());
                                            masterL1Ips = dealWithBlockIps(groupConfigAliyun.getMasterL1());
                                            slaveL1Ips = dealWithBlockIps(groupConfigAliyun.getSlaveL1());
                                            configStrAliyun = generatorConfigStr(cacheAgentConfigYf, groupConfigAliyun);
                                            IdcInfo idcInfoAliyun = generatorIdcInfo(groupAliyun, Idc.aliyun.name(), masterIps, slaveIps,
                                                    masterL1Ips, slaveL1Ips, slaveAliyun, layerMode, masterCapacity, masterL1Capacity,
                                                    masterL1Block);
                                            idcInfoList.add(idcInfoAliyun);
                                        }
                                    }
                                }
                            }
                            RecordInfo recordInfo = buildRecordInfo(type, groupConf, 0f,0,0);
                            ResourceAccessSetting resourceAccessSetting = generatorResourceAccessSetting(groupConf);
                            int id = processApply(recordInfo,resourceAccessSetting,idcInfoList);
                            if (!configStrYf.equals("")) {
                                processConfigRecord(groupYf, groupConf.getName(), configStrYf, layerMode, slaveYf);
                            }
                            if (!configStrTc.equals("")) {
                                processConfigRecord(groupTc, groupConf.getName(), configStrTc, layerMode, slaveTc);
                            }
                            if (!configStrAliyun.equals("")) {
                                processConfigRecord(groupTc, groupConf.getName(), configStrAliyun, layerMode, slaveAliyun);
                            }
                            if (!configStrAliyunTc.equals("")) {
                                processConfigRecord(groupTc, groupConf.getName(), configStrAliyunTc, layerMode, slaveAliyunTc);
                            }
                            processDeploy("admin", "admin", id);
                        }
                    }
                }
            }

        }
    }

    private int getMasterL1Block(List<List<String>> masterL1) {
        int result = 0;
        if (masterL1 != null) {
            result = masterL1.size();
        }
        return result;
    }

    private int getMasterL1Capacity(List<List<String>> masterL1) {
        int result = 0;
        if (masterL1 != null) {
            result = getMasterCapacity(masterL1.get(0));
        }
        return result;
    }

    private int getMasterCapacity(List<String> ips) {
        int result = 0;
        if (ips != null) {
            String ip = ips.get(0);
            InetSocketAddress sockAddress = new InetSocketAddress(ip.split(":")[0], Integer.parseInt(ip.split(":")[1]));
            Map<String, String> statsRs = CaptainMemcacheClientUtil.stats(sockAddress);
            if (statsRs != null && statsRs.get("limit_maxbytes") != null) {
                result = (int) (Math.ceil(Float.parseFloat(statsRs.get("limit_maxbytes")) / 1024 / 1024 / 1024) * ips.size());
            }
        }
        return result;
    }

    private RecordInfo buildRecordInfo(String type, CacheAgentGroupConfig cacheAgentGroupConfig, float hitPercent, int readTps,
            int writeTps) {
        String biz = cacheAgentGroupConfig.getName();
        RecordInfo recordInfo = new RecordInfo();
        recordInfo.setType(type);
        recordInfo.setBiz(biz);
        recordInfo.setHitPercent(hitPercent);
        recordInfo.setReadTps(readTps);
        recordInfo.setWriteTps(writeTps);
        recordInfo.setState(0);
        recordInfo.setApplyUser("admin");
        recordInfo.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        return recordInfo;

    }

    private SLA generateDefaultSla() {
        SLA sla = new SLA();
        sla.setHitPercent(0);
        sla.setReadTps(0);
        sla.setWriteTps(0);
        return sla;
    }

    private ResourceAccessSetting generatorResourceAccessSetting(CacheAgentGroupConfig cacheAgentGroupConfig) {
        ResourceAccessSetting resourceAccessSetting = new ResourceAccessSetting();
        resourceAccessSetting.setHash(cacheAgentGroupConfig.getHash());
        resourceAccessSetting.setDistribution(cacheAgentGroupConfig.getDistribution());
        resourceAccessSetting.setHashTag(cacheAgentGroupConfig.getHashTag());
        resourceAccessSetting.setSaveTag(cacheAgentGroupConfig.getSaveTag() == true ? 1 : 0);
        resourceAccessSetting.setAutoEjectHosts(cacheAgentGroupConfig.getAutoEjecThosts() == true ? 1 : 0);
        resourceAccessSetting.setTimeout(cacheAgentGroupConfig.getTimeout());
//        if (cacheAgentGroupConfig.getMasterEffectiveTime() != null) {
//            resourceAccessSetting.setMasterEffectiveTime(cacheAgentGroupConfig.getMasterEffectiveTime());
//        }
//        if (cacheAgentGroupConfig.getLruTimeout() != null) {
//            resourceAccessSetting.setLruTimeout(cacheAgentGroupConfig.getLruTimeout());
//        }
        resourceAccessSetting.setResourceType(cacheAgentGroupConfig.getRedis() == true ? 1 : 0);
        resourceAccessSetting.setServerRetryTimeout(cacheAgentGroupConfig.getServerRetryTimeout());
        resourceAccessSetting.setServerFailureLimit(cacheAgentGroupConfig.getServerFailureLimit());
        resourceAccessSetting.setPartialReply(cacheAgentGroupConfig.getPartialReply() == true ? 1 : 0);
        resourceAccessSetting.setExptime(cacheAgentGroupConfig.getExptime());
        resourceAccessSetting.setForceWriteAll(cacheAgentGroupConfig.getForceWriteAll() == true ? 1 : 0);
        resourceAccessSetting.setUpdateSlaveL1(cacheAgentGroupConfig.getUpdateSlaveL1() == true ? 1 : 0);
        resourceAccessSetting.setlocalAffinity(cacheAgentGroupConfig.getlocalAffinity() == true ? 1 : 0);
        resourceAccessSetting.setFlag(cacheAgentGroupConfig.getFlag());
        return resourceAccessSetting;
    }

    private String generatorConfigStr(CacheAgentConfig cacheAgentConfig, CacheAgentGroupConfig groupConf) {
        CacheAgentGlobalConfig cacheAgentGlobalConfig = cacheAgentConfig.getGlobalConfig();
        CacheAgentConfig result = new CacheAgentConfig();
        result.setGlobalConfig(cacheAgentGlobalConfig);
        List<CacheAgentGroupConfig> groupConfs = new ArrayList<CacheAgentGroupConfig>();
        groupConfs.add(groupConf);
        result.setGroupConfs(groupConfs);
        return result.toYamlStr();
    }

    private IdcInfo generatorIdcInfo(String group, String idcName, String masterIps, String slaveIps, String masterL1Ips, String slaveL1Ips,
            String slave, int layerMode, int masterCapacity, int masterL1Capacity, int masterL1Block) {
        IdcInfo idcInfo = new IdcInfo();
        idcInfo.setGroup(group);
        idcInfo.setIdc(idcName);
//        idcInfo.setLayerMode(layerMode);
        idcInfo.setMasterIps(masterIps);
        idcInfo.setMasterL1Ips(masterL1Ips);
        idcInfo.setSlaveIps(slaveIps);
        idcInfo.setSlaveL1Ips(slaveL1Ips);
        idcInfo.setSlave(slave);
        idcInfo.setMasterCapacity(masterCapacity);
        idcInfo.setMasterL1Block(masterL1Block);
        idcInfo.setMasterL1Capacity(masterL1Capacity);
        return idcInfo;
    }

    private String dealWithIps(List<String> ips) {
        String result = "";
        if (ips != null) {
            for (String masterIp : ips) {
                result += (masterIp + ",");
            }
        }
        if (!result.equals("")) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }

    private String dealWithBlockIps(List<List<String>> ips) {
        String result = "";
        if (ips != null) {
            for (List<String> singleBlockIps : ips) {
                result += dealWithIps(singleBlockIps) + "#";
            }
        }
        if (!result.equals("")) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }

    private int processApply(RecordInfo recordInfo, ResourceAccessSetting resourceAccessSetting, List<IdcInfo> idcInfos) {
        int proId = generateIdDao.generateId(recordInfo.getType(), recordInfo.getBiz());
        recordInfo.setProId(proId);
        //resourceAccessSetting.setProId(proId);
        if (resourceDao.addResource(recordInfo) == 0 || !resourceAccessSettingDao.addResourceAccessSetting(resourceAccessSetting)) {
            return 0;
        }
        for (IdcInfo idcInfo : idcInfos) {
            if (!idcInfoDao.addIdcInfo(idcInfo)) {
                return 0;
            }
        }
        return proId;
    }

    private void processConfigRecord(String group, String biz, String configStr, int layerMode, String slave) {
        String sign = staticConfigBiz.getSign(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
        if (sign == null) {// 重试一次
            sign = staticConfigBiz.getSign(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
        }
        ConfigRecording configRecording = new ConfigRecording();
        configRecording.setGroup(group);
        configRecording.setBiz(biz);
        configRecording.setContent(configStr + "/" + layerMode + "," + slave);
        configRecording.setSign(sign);
        configRecording.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        configRecordingDao.addConfigRecording(configRecording);
    }

    private void processDeploy(String deployUser, String assignUser, int id) {
        RecordInfo recordInfo = resourceDao.getResource(id);
        if (recordInfo != null) {
            recordInfo.setDeployUser(deployUser);
            recordInfo.setAssignUser(assignUser);
            recordInfo.setState(2);
            resourceDao.updateResource(recordInfo);
        }
    }

    public static void main(String[] args) {
        ApplicationContext context = new ClassPathXmlApplicationContext("captain-assigner.xml");
        Statistic statistic = (Statistic) context.getBean("statistic");
        init();
        // statistic.manageResource();
        // System.out.println(namespaces.get("unread.attention"));
        System.out.println("导入完成");

    }


}

