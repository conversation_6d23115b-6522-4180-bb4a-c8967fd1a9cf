/**
 * Project Name:captain-assigner File Name:Statistic2.java Package
 * Name:com.weibo.api.captain.assigner.util Date:2016年11月1日下午7:38:58 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.assigner.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.weibo.api.captain.assigner.dao.ConfigRecordingDao;
import com.weibo.api.captain.assigner.dao.GenerateIdDao;
import com.weibo.api.captain.assigner.dao.IdcInfoDao;
import com.weibo.api.captain.assigner.dao.ResourceAccessSettingDao;
import com.weibo.api.captain.assigner.dao.ResourceDao;
import com.weibo.api.captain.assigner.dao.ServicePoolDao;
import com.weibo.api.captain.assigner.model.ConfigRecording;
import com.weibo.api.captain.assigner.model.IdcInfo;
import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.assigner.model.ResourceAccessSetting;
import com.weibo.api.captain.common.CaptainConstants;
import com.weibo.api.captain.common.memcache.CaptainMemcacheClientUtil;
import com.weibo.api.captain.common.model.CacheAgentConfig;
import com.weibo.api.captain.common.model.CacheAgentGlobalConfig;
import com.weibo.api.captain.common.model.CacheAgentGroupConfig;
import com.weibo.api.captain.common.model.Idc;
import com.weibo.api.captain.common.service.StaticConfigBizImpl;

/**
 * 
 * <pre> ClassName: Statistic2
 * 
 * 同步线上数据工具 <pre/> date: 2016年11月20日 下午9:20:28 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
public class Statistic2 {
    private StaticConfigBizImpl staticConfigBiz;
    private ServicePoolDao servicePoolDao;
    private static Map<String, List<String>> namespaces = new HashMap<String, List<String>>();
    private static Map<String, List<Map<String, CacheAgentConfig>>> cacheAgentConfigs =
            new HashMap<String, List<Map<String, CacheAgentConfig>>>();
    private static String fileName = "/Users/<USER>/Documents/result_feedcontent";
    private final static int DEFAULT_ID = 0;
    private ResourceDao resourceDao;
    private ConfigRecordingDao configRecordingDao;
    private ResourceAccessSettingDao resourceAccessSettingDao;
    private IdcInfoDao idcInfoDao;
    private GenerateIdDao generateIdDao;
    private static List<String> rootNode = new ArrayList<String>(3);

    public void setStaticConfigBiz(StaticConfigBizImpl staticConfigBiz) {
        this.staticConfigBiz = staticConfigBiz;
    }

    public void setResourceDao(ResourceDao resourceDao) {
        this.resourceDao = resourceDao;
    }

    public void setConfigRecordingDao(ConfigRecordingDao configRecordingDao) {
        this.configRecordingDao = configRecordingDao;
    }

    public void setGenerateIdDao(GenerateIdDao generateIdDao) {
        this.generateIdDao = generateIdDao;
    }

    public void setResourceAccessSettingDao(ResourceAccessSettingDao resourceAccessSettingDao) {
        this.resourceAccessSettingDao = resourceAccessSettingDao;
    }

    public void setIdcInfoDao(IdcInfoDao idcInfoDao) {
        this.idcInfoDao = idcInfoDao;
    }

    public void setServicePoolDao(ServicePoolDao servicePoolDao) {
        this.servicePoolDao = servicePoolDao;
    }

    public static void init() {
        File file = new File(fileName);
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            while ((tempString = reader.readLine()) != null) {
                System.out.println(tempString);
                String key = tempString.split(" ")[0];
                List<String> values = new ArrayList<String>();
                for (int i = 0; i < tempString.split(" ")[1].split(",").length; i++) {
                    values.add(tempString.split(" ")[1].split(",")[i]);
                }
                namespaces.put(key, values);

            }
            rootNode.add("yf");
            rootNode.add("tc");
            rootNode.add("sx");
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {}
            }
        }
    }

    public String generatorGroup(String type, String idc) {
        String group = "cache.service." + type + ".pool." + idc;
        return group;
    }

    public String getType (String group) {
        return group.split("cache.service.")[1].split(".pool")[0];
    }
    public String getIdc (String group) {
        return group.split("pool.")[1];
    }


    private CacheAgentConfig getConfig(String group) {
        CacheAgentConfig cacheAgentConfig = staticConfigBiz.lookup(group, "all");
        if (cacheAgentConfig == null) {
            cacheAgentConfig = staticConfigBiz.lookup(group, "all");
        }
        if (cacheAgentConfig == null) {
            cacheAgentConfig = staticConfigBiz.lookup(group, "all");
        }
        return cacheAgentConfig;
    }

    private void addServicePool(int proxyState, String configAddr) {
        for (Map.Entry<String, List<String>> entry : namespaces.entrySet()) {
            String type = entry.getKey();
            List<Map<String, CacheAgentConfig>> configList = new ArrayList<Map<String, CacheAgentConfig>>();
            String idcList = "";
            for (Idc idcValue : Idc.values()) {
                String group = generatorGroup(type, idcValue.name());
                // 从configserver拉数据，有说明机房存在，插数据库
                CacheAgentConfig cacheAgentConfig = getConfig(group);
                if (cacheAgentConfig != null) {
                    idcList += (idcValue.name() + ",");
                    Map<String, CacheAgentConfig> configInfo = new HashMap<String, CacheAgentConfig>();
                    configInfo.put(idcValue.name(), cacheAgentConfig);
                    configList.add(configInfo);

                }
            }
            if (!"".equals(idcList)) {
                idcList = idcList.substring(0, idcList.length() - 1);
            }
            servicePoolDao.addServicePoolInfo(type, proxyState, configAddr, idcList);
            cacheAgentConfigs.put(type, configList);
        }
    }


    private void handleResource() {
        // type,list
        for (Map.Entry<String, List<Map<String, CacheAgentConfig>>> entry : cacheAgentConfigs.entrySet()) {
            String type = entry.getKey();
            List<String> namespacesForTheType = namespaces.get(type);
            for (int i = 0; i < namespacesForTheType.size(); i++) {
                String namespace = namespacesForTheType.get(i);
                RecordInfo recordInfo = buildRecordInfo(type, namespace, 0, 0, 0, "admin");
                List<IdcInfo> idcInfoList = new ArrayList<IdcInfo>();
                ResourceAccessSetting resourceAccessSetting = null;
                List<Map<String, CacheAgentConfig>> cacheAgentConfigs = entry.getValue();
                // 对于一个namespace先考虑复用情况。
                Map<String, String> reuseList = new HashMap<String, String>();
                Map<String, CacheAgentGroupConfig> rootNodeConfig = new HashMap<String, CacheAgentGroupConfig>();
                for (int j=0;j<cacheAgentConfigs.size();j++) {//提取root机房对应namespace的配置
                    Map<String, CacheAgentConfig> cacheAgentConfigMap = cacheAgentConfigs.get(j);
                    for (Map.Entry<String, CacheAgentConfig> idcEntry : cacheAgentConfigMap.entrySet()) {
                        String idcValue = idcEntry.getKey();
                        if (rootNode.contains(idcValue)) {
                            CacheAgentConfig cacheAgentConfig = idcEntry.getValue();
                            for (CacheAgentGroupConfig groupConf : cacheAgentConfig.getGroupConfs()) {
                                if (namespace.equals(groupConf.getName())) {
                                    rootNodeConfig.put(idcValue, groupConf);
                                }
                            }
                        }
                    }
                }
               
                for (int j = 0; j < cacheAgentConfigs.size(); j++) {
                    Map<String, CacheAgentConfig> cacheAgentConfigMap = cacheAgentConfigs.get(j);
                    for (Map.Entry<String, CacheAgentConfig> idcEntry : cacheAgentConfigMap.entrySet()) {
                        String idcValue = idcEntry.getKey();
                        if (rootNode.contains(idcValue)) {//种子机房没有复用情况
                            reuseList.put(idcValue, "");
                            continue;
                        }
                        CacheAgentConfig cacheAgentConfig = idcEntry.getValue();
                        for (CacheAgentGroupConfig groupConf : cacheAgentConfig.getGroupConfs()) {
                            if (namespace.equals(groupConf.getName())) {
                                String reuseIdc = "";
                                if (rootNodeConfig.get("yf") != null) {
                                    if (isReUse(groupConf,rootNodeConfig.get("yf"))){
                                        reuseIdc = "yf";
                                    }
                                }
                                if (reuseIdc.equals("")) {
                                    if (rootNodeConfig.get("tc") != null) {
                                        if (isReUse(groupConf,rootNodeConfig.get("tc"))){
                                            reuseIdc = "tc";
                                        }
                                    }
                                }
                                if (reuseIdc.equals("")) {
                                    if (rootNodeConfig.get("sx") != null) {
                                        if (isReUse(groupConf,rootNodeConfig.get("sx"))){
                                            reuseIdc = "sx";
                                        }
                                    }
                                }
                                reuseList.put(idcValue, reuseIdc);
                            }
                        }
                    }
                }
                 
                for (int j = 0; j < cacheAgentConfigs.size(); j++) {
                    Map<String, CacheAgentConfig> cacheAgentConfigMap = cacheAgentConfigs.get(j);
                    for (Map.Entry<String, CacheAgentConfig> idcEntry : cacheAgentConfigMap.entrySet()) {
                        String idcValue = idcEntry.getKey();
                        CacheAgentConfig cacheAgentConfig = idcEntry.getValue();
                        for (CacheAgentGroupConfig groupConf : cacheAgentConfig.getGroupConfs()) {
                            if (namespace.equals(groupConf.getName())) {
                                resourceAccessSetting = generatorResourceAccessSetting(groupConf);
                                String slave = "-1";
                                String slaveL1Idc = "";
                                for (int k = 0; k < cacheAgentConfigs.size(); k++) {
                                    for (Map.Entry<String, CacheAgentConfig> idcEntryTemp : cacheAgentConfigs.get(k).entrySet()) {
                                        if (idcEntryTemp.getKey().equals(idcValue)) {
                                            continue;
                                        }
                                        CacheAgentConfig cacheAgentConfigTemp = idcEntryTemp.getValue();
                                        for (CacheAgentGroupConfig groupConfTemp : cacheAgentConfigTemp.getGroupConfs()) {
                                            if (namespace.equals(groupConfTemp.getName())) {
                                                if (reuseList.get(idcValue).equals("")) {
                                                    if (reuseList.get(idcEntryTemp.getKey()).equals("")) {
                                                        if (groupConf.getSlave() != null && groupConf.getSlave().size() != 0) {
                                                            if (groupConf.getSlave().contains(groupConfTemp.getMaster().get(0))) {
                                                                slave = idcEntryTemp.getKey();
                                                            } 
                                                        }
                                                        if (!CollectionUtils.isEmpty(groupConf.getSlaveL1())) {
                                                            if (!CollectionUtils.isEmpty(groupConfTemp.getMasterL1()) || !CollectionUtils.isEmpty(groupConfTemp.getMaster())) {
                                                                List<List<String>> slaveL1s = groupConf.getSlaveL1();
                                                                for (List<String> slaveL1 : slaveL1s) {
                                                                    if (!CollectionUtils.isEmpty(groupConfTemp.getMasterL1())) {
                                                                        if (!idcEntryTemp.getKey().equals("aliyun_tc") && slaveL1.contains(groupConfTemp.getMasterL1().get(0).get(0))) {
                                                                            slaveL1Idc += idcEntryTemp.getKey() + ",";
                                                                            break;
                                                                        }
                                                                    } else if (!idcEntryTemp.getKey().equals("aliyun_tc") && !CollectionUtils.isEmpty(groupConfTemp.getMaster())) {
                                                                        if (slaveL1.contains(groupConfTemp.getMaster().get(0))) {
                                                                            slaveL1Idc += idcEntryTemp.getKey() + ",";
                                                                            break;
                                                                        }
                                                                    }
                                                                   
                                                                }
                                                            }
                                                        }
                                                    } 
                                                } else {
                                                    slave = "-1";
                                                    slaveL1Idc = "";
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                                if (!slaveL1Idc.equals("")) {
                                    slaveL1Idc = slaveL1Idc.substring(0, slaveL1Idc.length() - 1);
                                }
                                if (groupConf.getSlave() != null && reuseList.get(idcValue).equals("") && slave.equals("-1")) {
                                    slave = idcValue;
                                }
                                String masterIps = dealWithIps(groupConf.getMaster());
                                String slaveIps = dealWithIps(groupConf.getSlave());
                                String masterL1Ips = dealWithBlockIps(groupConf.getMasterL1());
                                String slaveL1Ips = dealWithBlockIps(groupConf.getSlaveL1());
                                int masterCapacity = getMasterCapacity(groupConf.getMaster());
                                int masterL1Capacity = getMasterL1Capacity(groupConf.getMasterL1());
                                int masterL1Block = getMasterL1Block(groupConf.getMasterL1());
                                IdcInfo idcInfo =
                                        generatorIdcInfo(0, generatorGroup(type, idcValue), idcValue, masterIps, slaveIps, masterL1Ips,
                                                slaveL1Ips, slave, masterCapacity, masterL1Capacity, masterL1Block, slaveL1Idc, "",reuseList.get(idcValue));
                                idcInfoList.add(idcInfo);
                                String configStr = generatorConfigStr(cacheAgentConfig, groupConf);
                                String group = generatorGroup(type, idcValue);
                                // System.out.println(idcInfoList.size());
                                processConfigRecord(group, namespace, configStr);
                            }

                        }
                    }
                }
                int proId = processApply(recordInfo, resourceAccessSetting, idcInfoList);
                processDeploy("admin", "admin", proId);
                System.out.println(namespace + "导入完成");
            }
        }
    }
    
    private boolean isReUse(CacheAgentGroupConfig groupConf, CacheAgentGroupConfig groupConfTemp) {
        if ((groupConf.getMaster().size() == groupConfTemp.getMaster().size())
                && (groupConf.getMaster().contains(groupConfTemp.getMaster().get(0)))) {
            boolean sameSlave = isSameSlave(groupConf, groupConfTemp);
            
            boolean sameMasterL1 = true;
            
            if (!CollectionUtils.isEmpty(groupConf.getMasterL1())) {
                if (!CollectionUtils.isEmpty(groupConfTemp.getMasterL1())) {
                    sameMasterL1 = IsSame(groupConf.getMasterL1(), groupConfTemp.getMasterL1());
                } else {
                    sameMasterL1 = false;
                }
            } else if (!CollectionUtils.isEmpty(groupConfTemp.getMasterL1())) {
                sameMasterL1 = false;
            }
           
            boolean sameSlaveL1 = true;
            
            if (!CollectionUtils.isEmpty(groupConf.getSlaveL1())) {
                if (!CollectionUtils.isEmpty(groupConfTemp.getSlaveL1())) {
                    sameSlaveL1 = IsSame(groupConf.getSlaveL1(), groupConfTemp.getSlaveL1());
                } else {
                    sameSlaveL1 = false;
                }
            } else if (!CollectionUtils.isEmpty(groupConfTemp.getSlaveL1())) {
                sameSlaveL1 = false;
            }
            if (sameSlave && sameMasterL1 && sameSlaveL1 ) {
                return true;
            }
        }
        return false;
    }

    private boolean isSameSlave(CacheAgentGroupConfig groupConf, CacheAgentGroupConfig groupConfTemp) {
        boolean sameSlave = true;
        if (groupConf.getSlave() != null) {
            if (groupConfTemp.getSlave() != null) {
                sameSlave = (groupConf.getSlave().size() == groupConfTemp.getSlave().size())
                        && (groupConf.getSlave().contains(groupConfTemp.getSlave().get(0)));
            } else {
                sameSlave = false;
            }
        } else if (groupConfTemp.getSlave() != null) {
            sameSlave = false;
        }
        return sameSlave;
    }
    
    private boolean IsSame(List<List<String>> list1,List<List<String>> list2) {
        if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
            return false;
        }
        if (list1.size() != list2.size()) {
            return false;
        }
        //1,2  3
        int i = 0;
        for (List<String> list : list1) {
           for (List<String> listTemp : list2) {
               if (list.containsAll(listTemp) && listTemp.containsAll(list) ){
                   i++;
               }
           }
        }
        if (i == list1.size()) {
            return true;
        }
        return false;
    }

    private IdcInfo generatorIdcInfo(int backupProId, String group, String idcName, String masterIps, String slaveIps, String masterL1Ips,
            String slaveL1Ips, String slave, int masterCapacity, int masterL1Capacity, int masterL1Block, String slaveL1Idc, String masterL1idc,
            String reuseIdc) {
        IdcInfo idcInfo = new IdcInfo();
        idcInfo.setBackupProId(backupProId);
        idcInfo.setGroup(group);
        idcInfo.setIdc(idcName);
        idcInfo.setMasterIps(masterIps);
        idcInfo.setMasterL1Ips(masterL1Ips);
        idcInfo.setSlaveIps(slaveIps);
        idcInfo.setSlaveL1Ips(slaveL1Ips);
        idcInfo.setSlave(slave);
        idcInfo.setMasterCapacity(masterCapacity);
        idcInfo.setMasterL1Block(masterL1Block);
        idcInfo.setMasterL1Capacity(masterL1Capacity);
        idcInfo.setSlaveL1Idc(slaveL1Idc);
        idcInfo.setMasterL1Idc(masterL1idc);
        idcInfo.setReuseIdc(reuseIdc);
        idcInfo.setState(1);
        return idcInfo;
    }

    private String dealWithIps(List<String> ips) {
        String result = "";
        if (ips != null) {
            for (String masterIp : ips) {
                result += (masterIp + ",");
            }
        }
        if (!result.equals("")) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }

    private String dealWithBlockIps(List<List<String>> ips) {
        String result = "";
        if (ips != null) {
            for (List<String> singleBlockIps : ips) {
                result += dealWithIps(singleBlockIps) + "#";
            }
        }
        if (!result.equals("")) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }

    private int getMasterL1Block(List<List<String>> masterL1) {
        int result = 0;
        if (masterL1 != null) {
            result = masterL1.size();
        }
        return result;
    }

    private int getMasterL1Capacity(List<List<String>> masterL1) {
        int result = 0;
        if (masterL1 != null) {
            result = getMasterCapacity(masterL1.get(0));
        }
        return result;
    }

    private int getMasterCapacity(List<String> ips) {
        int result = 0;
        if (ips != null) {
            String ip = "";
            for (String ipAndPort : ips) {
                if (ipAndPort.split(":").length > 1) {
                    ip = ipAndPort;
                    break;
                }
            }
            if (!ip.equals("")) {
                InetSocketAddress sockAddress = new InetSocketAddress(ip.split(":")[0], Integer.parseInt(ip.split(":")[1]));
                Map<String, String> statsRs = CaptainMemcacheClientUtil.stats(sockAddress);
                if (statsRs != null && statsRs.get("limit_maxbytes") != null) {
                    result = (int) (Math.ceil(Float.parseFloat(statsRs.get("limit_maxbytes")) / 1024 / 1024 / 1024) * ips.size());
                }
            }
        }
        return result;
    }

    private RecordInfo buildRecordInfo(String type, String biz, float hitPercent, int readTps, int writeTps, String applyUser) {
        RecordInfo recordInfo = new RecordInfo();
        recordInfo.setType(type);
        recordInfo.setBiz(biz);
        recordInfo.setHitPercent(hitPercent);
        recordInfo.setReadTps(readTps);
        recordInfo.setWriteTps(writeTps);
        recordInfo.setApplyUser(applyUser);
        recordInfo.setAssignUser("admin");
        recordInfo.setDeployUser("admin");
        recordInfo.setReturnUser("admin");
        return recordInfo;
    }

    private ResourceAccessSetting generatorResourceAccessSetting(CacheAgentGroupConfig cacheAgentGroupConfig) {
        ResourceAccessSetting resourceAccessSetting = new ResourceAccessSetting();
        resourceAccessSetting.setHash(cacheAgentGroupConfig.getHash());
        resourceAccessSetting.setDistribution(cacheAgentGroupConfig.getDistribution());
        resourceAccessSetting.setHashTag(cacheAgentGroupConfig.getHashTag());
        resourceAccessSetting.setSaveTag(cacheAgentGroupConfig.getSaveTag() == true ? 1 : 0);
        resourceAccessSetting.setAutoEjectHosts(cacheAgentGroupConfig.getAutoEjecThosts() == true ? 1 : 0);
        resourceAccessSetting.setTimeout(cacheAgentGroupConfig.getTimeout());
//        if (cacheAgentGroupConfig.getMasterEffectiveTime() != null) {
//            resourceAccessSetting.setMasterEffectiveTime(cacheAgentGroupConfig.getMasterEffectiveTime());
//        }
//        if (cacheAgentGroupConfig.getLruTimeout() != null) {
//            resourceAccessSetting.setLruTimeout(cacheAgentGroupConfig.getLruTimeout());
//        }
        resourceAccessSetting.setResourceType(cacheAgentGroupConfig.getRedis() == true ? 1 : 0);
        resourceAccessSetting.setServerRetryTimeout(cacheAgentGroupConfig.getServerRetryTimeout());
        resourceAccessSetting.setServerFailureLimit(cacheAgentGroupConfig.getServerFailureLimit());
        resourceAccessSetting.setPartialReply(cacheAgentGroupConfig.getPartialReply() == true ? 1 : 0);
        resourceAccessSetting.setExptime(cacheAgentGroupConfig.getExptime());
        return resourceAccessSetting;
    }

    private String generatorConfigStr(CacheAgentConfig cacheAgentConfig, CacheAgentGroupConfig groupConf) {
        CacheAgentGlobalConfig cacheAgentGlobalConfig = cacheAgentConfig.getGlobalConfig();
        CacheAgentConfig result = new CacheAgentConfig();
        result.setGlobalConfig(cacheAgentGlobalConfig);
        List<CacheAgentGroupConfig> groupConfs = new ArrayList<CacheAgentGroupConfig>();
        groupConfs.add(groupConf);
        result.setGroupConfs(groupConfs);
        return result.toYamlStr();
    }

    private int processApply(RecordInfo recordInfo, ResourceAccessSetting resourceAccessSetting, List<IdcInfo> idcInfos) {
        recordInfo.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        int proId = generateIdDao.generateId(recordInfo.getType(), recordInfo.getBiz());// 使用数据库自增id
        recordInfo.setProId(proId);
        int backupProId = DEFAULT_ID;
        if ((backupProId = resourceDao.addResource(recordInfo)) == DEFAULT_ID) {
            return DEFAULT_ID;
        }
        resourceAccessSetting.setBackupProId(backupProId);
        if (!resourceAccessSettingDao.addResourceAccessSetting(resourceAccessSetting)) {
            return DEFAULT_ID;
        }
        if (idcInfos != null) {
            for (IdcInfo idcInfo : idcInfos) {
                idcInfo.setBackupProId(backupProId);
                if (!idcInfoDao.addIdcInfo(idcInfo)) {
                    return DEFAULT_ID;
                }
            }
        }
        return recordInfo.getProId();
    }

    private void processConfigRecord(String group, String biz, String configStr) {
        String sign = staticConfigBiz.getSign(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
        if (sign == null) {// 重试一次
            sign = staticConfigBiz.getSign(group, CaptainConstants.DEFAULT_STATIC_CONFIG_KEY);
        }
        ConfigRecording configRecording = new ConfigRecording();
        configRecording.setGroup(group);
        configRecording.setBiz(biz);
        configRecording.setContent(configStr);
        configRecording.setSign(sign);
        configRecording.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        configRecordingDao.addConfigRecording(configRecording);
    }

    private void processDeploy(String deployUser, String assignUser, int id) {
        RecordInfo recordInfo = resourceDao.getResource(id);
        if (recordInfo != null) {
            recordInfo.setDeployUser(deployUser);
            recordInfo.setAssignUser(assignUser);
            recordInfo.setDeployUser("admin");
            recordInfo.setReturnUser("admin");
            recordInfo.setState(2);
            resourceDao.updateResource(recordInfo);
        }
    }

    public static void main(String[] args) {
        ApplicationContext context = new ClassPathXmlApplicationContext("captain-assigner.xml");
        ApplicationContext ctx = new ClassPathXmlApplicationContext("captain-common.xml");
        Statistic2 statistic = (Statistic2) context.getBean("statistic2");
        init();
        statistic.staticConfigBiz = (StaticConfigBizImpl) ctx.getBean("staticConfigBiz");
        statistic.staticConfigBiz.setBaseUrl("http://feed.config.api.weibo.com");
        statistic.addServicePool(0, "http://feed.config.api.weibo.com");
        statistic.handleResource();
        System.out.println("导入完成！");
    }

}

