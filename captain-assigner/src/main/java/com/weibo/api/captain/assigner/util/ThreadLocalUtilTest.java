package com.weibo.api.captain.assigner.util;

import com.weibo.api.captain.common.util.ThreadLocalUtil;

public class ThreadLocalUtilTest {
    public static void main(String[] args) {
        // 创建多个线程来测试 ThreadLocalUtil
        Runnable task1 = () -> {
            ThreadLocalUtil.setServer("config1", "serverA");
            System.out.println("Thread 1 - Config1: " + ThreadLocalUtil.getServer("config1"));
            System.out.println("Thread 1 - Config2 (not set): " + ThreadLocalUtil.getServer("config2"));
        };

        Runnable task2 = () -> {
            ThreadLocalUtil.setServer("config2", "serverB");
            System.out.println("Thread 2 - Config1 (not set): " + ThreadLocalUtil.getServer("config1"));
            System.out.println("Thread 2 - Config2: " + ThreadLocalUtil.getServer("config2"));
        };

        Thread thread1 = new Thread(task1);
        Thread thread2 = new Thread(task2);

        thread1.start();
        thread2.start();

        // 等待线程完成
        try {
            thread1.join();
            thread2.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 清理 ThreadLocal
        ThreadLocalUtil.clear();
        System.out.println("After clear - Thread 1 - Config1: " + ThreadLocalUtil.getServer("config1"));
        System.out.println("After clear - Thread 2 - Config2: " + ThreadLocalUtil.getServer("config2"));
    }
}
