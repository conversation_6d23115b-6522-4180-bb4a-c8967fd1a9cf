<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd  
           http://www.springframework.org/schema/context  
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

	<import resource="captain-common.xml"></import>

	<bean id="captainDataSourceMaster" class="com.mchange.v2.c3p0.ComboPooledDataSource">
		<property name="driverClass">
			<value>com.mysql.jdbc.Driver</value>
		</property>
		<!--<property name="jdbcUrl">-->
			<!--<value>*******************************************************************************************************************************************************;-->
			<!--</value>-->
		<!--</property>-->
		<!--<property name="user">-->
			<!--<value>captain</value>-->
		<!--</property>-->
		<!--<property name="password">-->
			<!--<value>captain</value>-->
		<!--</property>-->
		<property name="jdbcUrl">
			<value>*******************************************************************************************************************************************************;
			</value>
		</property>
		<property name="user">
			<value>captain</value>
		</property>
		<property name="password">
			<value>3b6e70207a1f9cc</value>
		</property>
		<property name="testConnectionOnCheckin">
	       <value>false</value>
        </property>
        <property name="testConnectionOnCheckout">
           <value>true</value>
        </property>
        <property name="idleConnectionTestPeriod">
          <value>60</value>
        </property>
	</bean>

	<bean id="captainDataSourceSlave" class="com.mchange.v2.c3p0.ComboPooledDataSource">
		<property name="driverClass">
			<value>com.mysql.jdbc.Driver</value>
		</property>
		<!--<property name="jdbcUrl">-->
			<!--<value>*******************************************************************************************************************************************************;-->
			<!--</value>-->
		<!--</property>-->
		<!--<property name="user">-->
			<!--<value>captain</value>-->
		<!--</property>-->
		<!--<property name="password">-->
			<!--<value>captain</value>-->
		<!--</property>-->
		<property name="jdbcUrl">
			<value>*******************************************************************************************************************************************************;
			</value>
		</property>
		<property name="user">
			<value>captain</value>
		</property>
		<property name="password">
			<value>6f65fe32ae28b2d</value>
		</property>
	</bean>
	<bean id="jdbcTemplate" class="cn.sina.api.data.dao.util.JdbcTemplate">
		<property name="dataSource">
			<ref bean="captainDataSourceMaster" />
		</property>
		<!-- <property name="dataSourceSlaves">
			<list>
				<ref bean="captainDataSourceSlave" />
			</list>
		</property> -->
	</bean>
	<bean id="configRecordingDao"
		class="com.weibo.api.captain.assigner.dao.ConfigRecordingDaoImpl">
		<property name="jdbcTemplate" ref="jdbcTemplate"></property>
	</bean>

	<bean id="resourceDao" class="com.weibo.api.captain.assigner.dao.ResourceDaoImpl">
		<property name="jdbcTemplate" ref="jdbcTemplate"></property>
	</bean>
	<bean id="resourceAccessSettingDao"
		class="com.weibo.api.captain.assigner.dao.ResourceAccessSettingDaoImpl">
		<property name="jdbcTemplate" ref="jdbcTemplate"></property>
	</bean>
	<bean id="idcInfoDao" class="com.weibo.api.captain.assigner.dao.IdcInfoDaoImpl">
		<property name="jdbcTemplate" ref="jdbcTemplate"></property>
	</bean>
	<bean id="generateIdDao" class="com.weibo.api.captain.assigner.dao.GenerateId">
		<property name="jdbcTemplate" ref="jdbcTemplate"></property>
	</bean>
	<bean id="resourceService"
		class="com.weibo.api.captain.assigner.service.ResourceServiceImpl" init-method="init">
		<property name="resourceDao" ref="resourceDao" />
		<property name="configRecordingDao" ref="configRecordingDao" />
		<property name="proxyStatsDao" ref="proxyStatsDao"></property>
		<property name="bizStatsDao" ref="bizStatsDao"></property>
		<property name="resourceAccessSettingDao" ref="resourceAccessSettingDao"></property>
		<property name="idcInfoDao" ref="idcInfoDao"></property>
		<property name="generateIdDao" ref="generateIdDao"></property>
		<property name="servicePoolDao" ref="servicePoolDao" />
	</bean>
	<bean id="proxyStatsDao" class="com.weibo.api.captain.assigner.dao.ProxyStatsDaoImpl">
		<property name="jdbcTemplate" ref="jdbcTemplate"></property>
	</bean>
	<bean id="bizStatsDao" class="com.weibo.api.captain.assigner.dao.BizStatsDaoImpl">
		<property name="jdbcTemplate" ref="jdbcTemplate"></property>
	</bean>
	<bean id="servicePoolDao" class="com.weibo.api.captain.assigner.dao.ServicePoolDaoImpl">
	    <property name="jdbcTemplate" ref="jdbcTemplate"></property>
	</bean>
	<bean id="statisticsTask" class="com.weibo.api.captain.assigner.stats.StatisticsTask">
		<property name="configRecordingDao" ref="configRecordingDao"></property>
		<property name="resourceDao" ref="resourceDao" />
		<property name="namingServiceBiz" ref="namingServiceBiz" />
		<property name="proxyStatsDao" ref="proxyStatsDao" />
		<property name="bizStatsDao" ref="bizStatsDao" />
		<property name="staticConfigBiz" ref="staticConfigBiz"></property>
	</bean>
	<bean id="statistic" class="com.weibo.api.captain.assigner.util.Statistic">
		<property name="staticConfigBiz" ref="staticConfigBiz"></property>
		<property name="resourceDao" ref="resourceDao" />
		<property name="configRecordingDao" ref="configRecordingDao" />
		<property name="resourceAccessSettingDao" ref="resourceAccessSettingDao"></property>
		<property name="idcInfoDao" ref="idcInfoDao"></property>
		<property name="generateIdDao" ref="generateIdDao"></property>
	</bean>

	<bean id="console4Netty"
		class="com.weibo.api.captain.assigner.stats.ConsoleServer4Netty"
		init-method="start" destroy-method="shutdown">
		<property name="serverPort" value="880"></property>
	</bean>

	<bean id="txManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="captainDataSourceMaster" />
	</bean>

	<bean id="statistic2" class="com.weibo.api.captain.assigner.util.Statistic2">
		<property name="staticConfigBiz" ref="staticConfigBiz"></property>
		<property name="resourceDao" ref="resourceDao" />
		<property name="configRecordingDao" ref="configRecordingDao" />
		<property name="resourceAccessSettingDao" ref="resourceAccessSettingDao"></property>
		<property name="idcInfoDao" ref="idcInfoDao"></property>
		<property name="generateIdDao" ref="generateIdDao"></property>
		<property name="servicePoolDao" ref="servicePoolDao"></property>
	</bean>
	<tx:annotation-driven transaction-manager="txManager" />



    <bean id="consistencyCheckTask" class="com.weibo.api.captain.assigner.stats.ConsistencyCheckTask">
        <property name="idcInfoDao" ref="idcInfoDao"></property>
        <property name="resourceDao" ref="resourceDao"></property>
        <property name="generateIdDao" ref="generateIdDao"></property>
        <property name="statistic2" ref="statistic2"></property>
		<property name="staticConfigBiz" ref="staticConfigBiz"></property>
		<property name="servicePoolDao" ref="servicePoolDao"></property>
    </bean>
</beans>
