<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>captain</artifactId>
		<groupId>com.weibo.api</groupId>
		<version>0.39-SNAPSHOT</version>
	</parent>

	<groupId>com.weibo.api.captain</groupId>
	<artifactId>captain-common</artifactId>
	<packaging>jar</packaging>
	<name>Archetype - captain-common</name>

	<properties>
		<vintage-client.version>1.1.12</vintage-client.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>net.spy</groupId>
			<artifactId>spymemcached</artifactId>
			<version>2.11.4</version>
		</dependency>

		<dependency>
			<groupId>com.weibo.api</groupId>
			<artifactId>vintage-client</artifactId>
			<version>${vintage-client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.weibo.api</groupId>
			<artifactId>api-commons</artifactId>
			<version>4.60</version>
		</dependency>
		<dependency>
			<groupId>org.yaml</groupId>
			<artifactId>snakeyaml</artifactId>
			<version>1.15</version>
		</dependency>
		<dependency>
			<groupId>org.jboss.netty</groupId>
			<artifactId>netty</artifactId>
			<version>3.2.10.Final</version>
		</dependency>

	</dependencies>
</project>
