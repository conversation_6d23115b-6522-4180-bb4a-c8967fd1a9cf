package com.weibo.api.captain.common;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.weibo.api.captain.common.model.CacheAgentConfig;
import com.weibo.api.captain.common.model.CacheAgentSLAThreshold;
import com.weibo.api.captain.common.model.CacheAgentStat;
import com.weibo.api.captain.common.model.MemcacheNodeStat;
import com.weibo.api.captain.common.model.MemcacheSlaThreshold;
import org.apache.commons.lang.StringUtils;

import com.google.code.hs4j.network.util.ConcurrentHashSet;
import com.weibo.vintage.model.EndpointAddress;
import com.weibo.vintage.model.NamingServiceCluster;

/**  
 * Package: com.weibo.api.captain.core  
 *  
 * File: GlobalInstance.java  
 * 
 *  <pre>
 *  包含如下几类变量：
 *  1 服务节点：1）业务对应的mc节点；2）client访问的cacheAgent节点；
 *  2 服务状态：1）mc的stat统计；  2）cacheAgent的配置状态； 3）cacheAgent的sla；
 *  </pre>
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class GlobalInstance {

	/** 服务、cluster对应的cacheAgent部署，Map&ltserviceName, Map&ltclustername, NamingServiceCluster&gt&gt */
	public static Map<String, Map<String, NamingServiceCluster>> cacheAgentServiceClusterLatest = new ConcurrentHashMap<String, Map<String,NamingServiceCluster>>();
	public static Map<String, Map<String, NamingServiceCluster>> cacheAgentServiceClusterProbing = new ConcurrentHashMap<String, Map<String,NamingServiceCluster>>();
	
	/** cacheAgent中的config，Map&ltgroupId, Map&ltgroupKey, CacheAgentConfig&gt&gt，目前的key默认是：all */
	public static Map<String, Map<String, CacheAgentConfig>> cacheAgentConfigsLatest = new ConcurrentHashMap<String, Map<String,CacheAgentConfig>>();
	public static Map<String, Map<String, CacheAgentConfig>> cacheAgentConfigsProbing = new ConcurrentHashMap<String, Map<String,CacheAgentConfig>>();
		
	/** cache agent 的统计信息, Map&ltEndpointAddress, CacheAgentStat&gt */
	public static Map<EndpointAddress, CacheAgentStat> cacheAgentStatLatest = new ConcurrentHashMap<EndpointAddress, CacheAgentStat>();
	public static Map<EndpointAddress, CacheAgentStat> cacheAgentStatProbing = new ConcurrentHashMap<EndpointAddress, CacheAgentStat>();
	
	/** cache agent 子业务的统计信息, Map&ltEndpointAddress, Map&ltEndpointAddress, Map&ltString, CacheAgentStat&gt&gt */
//	public static Map<EndpointAddress, Map<String, CacheAgentStat>> cacheAgentBizStatLatest = new ConcurrentHashMap<EndpointAddress, Map<String, CacheAgentStat>>();
//	public static Map<EndpointAddress, Map<String, CacheAgentStat>> cacheAgentBizStatProbing = new ConcurrentHashMap<EndpointAddress, Map<String, CacheAgentStat>>();
	
	/** memcached 的统计信息, Map&ltEndpointAddress, MemcacheNodeStat&gt */
	public static Map<EndpointAddress, MemcacheNodeStat> mcStatLatest = new ConcurrentHashMap<EndpointAddress, MemcacheNodeStat>();
	public static Map<EndpointAddress, MemcacheNodeStat> mcStatProbing = new ConcurrentHashMap<EndpointAddress, MemcacheNodeStat>();
	
	/** cacheAgent slas Map&ltcluster_id, SlaThreshold>&gt */
	public static Map<String, CacheAgentSLAThreshold> cacheAgentClusterSlaThreshold = new ConcurrentHashMap<String, CacheAgentSLAThreshold>();
	
	/** Map&ltGroupId, MemcacheSlaThreshold&gt */
	public static Map<String, MemcacheSlaThreshold> mcSlaThreshold = new ConcurrentHashMap<String, MemcacheSlaThreshold>();
	
	/** Map&ltEndpointAddress, groupId&gt */
	public static Map<EndpointAddress, String> mcAddressGroupIdsLatest = new ConcurrentHashMap<EndpointAddress, String>();
	public static Map<EndpointAddress, String> mcAddressGroupIdsProbing = new ConcurrentHashMap<EndpointAddress, String>();
	
	/** 服务配置的serviceIds 和gorupIds */
	public static Set<String> groupIds = new ConcurrentHashSet<String>();
	public static Set<String> serviceIds = new ConcurrentHashSet<String>();
	
	/**
	 * 根据clusterName获取sla阀值
	 * @param clusterName
	 * @return
	 */
	public static CacheAgentSLAThreshold getCacheAgentSlaThreshold(String clusterName) {
		if(StringUtils.isBlank(clusterName)) {
			return CaptainConstants.DEFAULT_CACHE_AGENT_SLA_THRESHOLD;
		}
		
		CacheAgentSLAThreshold sla = GlobalInstance.cacheAgentClusterSlaThreshold.get(clusterName);
		if(sla == null) {
			sla = CaptainConstants.DEFAULT_CACHE_AGENT_SLA_THRESHOLD;
		}
		
		return sla;
	}
	
	/**
	 * 根据address获取mc的sla 阀值
	 * @param addr
	 * @return
	 */
	public static MemcacheSlaThreshold getMcSlaThreshold(EndpointAddress addr) {
		if(addr == null) {
			return CaptainConstants.DEFAULT_MEMCACHE_SLA_THRESHOLD;
		}
		
		String groupId = mcAddressGroupIdsLatest.get(addr);
		if(StringUtils.isBlank(groupId) || !mcSlaThreshold.containsKey(groupId)) {
			return CaptainConstants.DEFAULT_MEMCACHE_SLA_THRESHOLD;
		}
		
		return mcSlaThreshold.get(groupId);
	}
}
