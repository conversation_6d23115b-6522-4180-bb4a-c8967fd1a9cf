/**  
 * Project Name:captain-common  
 * File Name:Test.java  
 * Package Name:com.weibo.api.captain.common  
 * Date:2016年7月14日下午10:09:49  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.common;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.weibo.api.captain.common.memcache.CaptainMemcacheClientUtil;
import com.weibo.api.captain.common.model.SLA;
import com.weibo.vintage.model.EndpointAddress;

import net.spy.memcached.MemcachedClient;

/**  
 * <pre>  
 * ClassName:Test 
 * 
 * description here!
 * <pre/>   
 * Date:     2016年7月14日 下午10:09:49 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class Test {
    private void getAbnormalEvent(String group) {
        
    }
    private List<String> getGroup() {
        List<String> groups = new ArrayList<String>();
        return groups;
    }
    
    private Map<String, String> getStat(EndpointAddress address) {
        InetSocketAddress sockAddress = new InetSocketAddress(address.getHost(), address.getPort());
        Map<String, String> statsRs = CaptainMemcacheClientUtil.stats(sockAddress);
        return statsRs;
    }
    
    public static void main(String []args) throws IOException {
//        MemcachedClient mcc = new MemcachedClient(new InetSocketAddress("127.0.0.1", 11211));
//        System.out.println("Connection to server sucessfully");
//        System.out.println("Stats:"+mcc.getStats());
        EndpointAddress endpointAddress = new EndpointAddress("127.0.0.1", 11211);
        Test test = new Test();
        Map<String, String> statsRs = test.getStat(endpointAddress);
        Iterator<Map.Entry<String, String>> iterator = statsRs.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            System.out.println(entry.getKey() + ":"+entry.getValue());
        }
        
    }

}
  
	