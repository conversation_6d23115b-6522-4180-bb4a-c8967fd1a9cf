/**  
 * Project Name:captain-common  
 * File Name:Test1.java  
 * Package Name:com.weibo.api.captain.common  
 * Date:2016年10月18日下午5:17:03  
 * Copyright (c) 2016, <EMAIL> All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.common;  
/**  
 * <pre>  
 * ClassName:Test1 
 * 
 * description here!
 * <pre/>   
 * Date:     2016年10月18日 下午5:17:03 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
import com.weibo.api.captain.common.service.NamingServiceBizImpl;
import com.weibo.api.captain.common.service.StaticConfigBizImpl;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

public class Test1 {
	public static void main(String[] args) {
//		NamingServiceBizImpl test = new NamingServiceBizImpl();
//		String s = test.getBaseUrl();
//		System.out.println(s);
        ApplicationContext ctx = new ClassPathXmlApplicationContext("captain-common.xml");          
        StaticConfigBizImpl obj = (StaticConfigBizImpl)ctx.getBean("staticConfigBiz");
        obj.setBaseUrl("***********:10");
        System.out.println(obj.getBaseUrl());   
    }  
}
  
