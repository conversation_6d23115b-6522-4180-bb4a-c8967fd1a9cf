/**  
 * Project Name:captain-assigner  
 * File Name:LayerType.java  
 * Package Name:com.weibo.api.captain.assigner.model  
 * Date:2016年5月17日下午2:39:18  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.common.constant;

import java.util.HashMap;
import java.util.Map;

/**  
 * <pre>  
 * ClassName:LayerType 
 * 
 * description here!
 * <pre/>   
 * Date:     2016年5月17日 下午2:39:18 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class LayerType {
    
    private static Map<Integer, String> idNames = new HashMap<Integer, String>();

    private static String TYPE_UNKNOWN = "unknown";

    static {
        idNames.put(0, "M");
        idNames.put(1, "M-S");
        idNames.put(2, "L1-M");
        idNames.put(3, "L1-M-S");
    }

    public static String getName(int id) {
        String name = idNames.get(id);
        if (null != name) {
            return name;
        }
        return TYPE_UNKNOWN;

    }

}
  
	