/**  
 * Project Name:captain-common  
 * File Name:RoleType.java  
 * Package Name:com.weibo.api.captain.common.constant  
 * Date:2016年5月13日下午2:58:09  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.common.constant;

import java.util.HashMap;
import java.util.Map;

/**  
 * <pre>  
 * ClassName:RoleType 
 * 
 * 预定义系统使用者的角色。
 * <pre/>   
 * Date:     2016年5月13日 下午2:58:09 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class RoleType {

	/**
	 * 类加载时就进行初始化，之后不会修改，所以不用考虑线程安全问题。
	 */
	private static Map<Integer, String> idNames = new HashMap<Integer, String>();
	
	private static String TYPE_UNKNOWN = "unknown";
	
	/**
	 * 预定义业务的id name对应关系，如果需要中文说明，可以增加一个idCnNames;
	 * 如果要扩展新的业务类型，这里增加
	 */
	static{
		idNames.put(1, "root");
		idNames.put(2, "da");
		idNames.put(3, "front-op");
		idNames.put(4, "developer");
		
	}
	
	/**
	 * 
	 * getName: 根据id获得业务 type. <br/>  
	 * 如果id不存在，返回TYPE_UNKNOWN.<br/>  
	 *  
	 * <AUTHOR>  
	 * @param id
	 * @return  
	 * @since JDK 1.8
	 */
	public static String getName(int id) {
		String name = idNames.get(id);
		if(name != null){
			return name;
		}
		return TYPE_UNKNOWN;
	}
}
  
	