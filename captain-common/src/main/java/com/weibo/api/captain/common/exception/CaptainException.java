package com.weibo.api.captain.common.exception;

import java.util.HashMap;
import java.util.Map;

import com.weibo.vintage.exception.ExcepFactor;


/**  
 * Package: com.weibo.api.captain.core.exception  
 *  
 * File: CaptainException.java   
 *  
 * Author: fishermen    
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainException extends RuntimeException{

	private static final long serialVersionUID = -5587974914045321615L;
	
	private final Map<String, Object> parameters = new HashMap<String, Object>();
	private ExcepFactor factor;
	

	public CaptainException(ExcepFactor factor) {
		this(factor,factor.getErrorMsg());
	}

	public CaptainException(ExcepFactor factor, Object message) {
		super((message==null?factor.getErrorMsg():message.toString()));
		this.factor = factor;
	}
	
	protected CaptainException(ExcepFactor factor, Object[] args) {
		this(factor,factor.getErrorMsg(args));
	}

	//FIXME 默认异常不应该输出详细错误信息？
	protected CaptainException(Exception e) {
		this(ExcepFactor.E_DEFAULT,e.getMessage());
	}
	
	protected CaptainException(String message){
		this(ExcepFactor.E_DEFAULT,message);
	}

	public void setTraceHeader(String name, Object value) {
		getTraceHeaders().put(name, value);
	}

	public Map<String, Object> getTraceHeaders() {
		return parameters;
	}

	public ExcepFactor getFactor() {
		return factor;
	}

	/**
	 * 只供构造对象时使用
	 * @param factor
	 */
	protected void setFactor(ExcepFactor factor) {
		this.factor = factor;
	}
}
