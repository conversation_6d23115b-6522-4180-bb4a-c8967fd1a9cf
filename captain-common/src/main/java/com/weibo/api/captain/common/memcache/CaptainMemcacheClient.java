package com.weibo.api.captain.common.memcache;

import java.net.SocketAddress;
import java.util.Map;

/**
 * Package: com.weibo.api.captain.common
 * 
 * File: MemcacheClient.java
 * 
 * <pre>
 *  因为需要根据协议扩展mc协议，同时使用时需要根据host随时新建client，所以此处专门对mcclient做封装，后续
 *  根据需要进行协议、client调整。
 *  </>
 *  
 * Author: fishermen
 * 
 * Copyright @ 2015 Corpration Name
 * 
 */
public interface CaptainMemcacheClient {

	/**
	 * Get all of the stats from all of the connections.
	 * 
	 * @return a Map of a Map of stats replies by So<PERSON><PERSON><PERSON><PERSON>
	 * @throws IllegalStateException
	 *             in the rare circumstance where queue is too full to accept
	 *             any more requests
	 */
	public Map<SocketAddress, Map<String, String>> getStats();

	/**
	 * Get a set of stats from all connections.
	 * @param prefix
	 * @return
	 */
	public Map<SocketAddress, Map<String, String>> getStats(String statsArg);
	
	public void shutdownClient();
}
