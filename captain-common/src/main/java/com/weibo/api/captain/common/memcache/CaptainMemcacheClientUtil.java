package com.weibo.api.captain.common.memcache;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import cn.sina.api.commons.util.ApiLogger;

/**  
 * Package: com.weibo.api.captain.common.memcache  
 *  
 * File: CaptainMemcacheClientUtil.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainMemcacheClientUtil {

//	private static Map<InetSocketAddress, CaptainMemcacheClient> memcacheClients = new ConcurrentHashMap<InetSocketAddress, CaptainMemcacheClient>();
	
	/**
	 * mc client cmd： stats
	 * @param address
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, String> stats(InetSocketAddress address) {
		if(address == null) {
			return Collections.EMPTY_MAP;
		}
		
		CaptainMemcacheClient client = getMemcacheClient(address);
		if(client == null) {
			return Collections.EMPTY_MAP;
		}
		
		Map<SocketAddress, Map<String, String>> result = null;
		
		try {
            result = client.getStats();
        } catch (Exception e) {
            ApiLogger.warn(String.format("stats false, addr: %s", address), e);
        } finally{
            shutdownClient(client);
        }
		
		if(result != null && result.containsKey(address)) {
			return result.get(address);
		}
		
		return Collections.EMPTY_MAP;
	}
	
	/**
	 * mc client cmd： stats statsArg
	 * @param address
	 * @param statsArg
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, String> stats(InetSocketAddress address, String statsArg) {
		if(address == null) {
			return Collections.EMPTY_MAP;
		}
		
		CaptainMemcacheClient client = getMemcacheClient(address);
		if(client == null) {
			return Collections.EMPTY_MAP;
		}
		
		Map<SocketAddress, Map<String, String>> result = null;
        try {
            result = client.getStats(statsArg);
        } catch (Exception e) {
            ApiLogger.warn(String.format("stats false, addr: %s, statsArg: %s", address, statsArg), e);
        } finally{
            shutdownClient(client);
        }
        
		if(result != null && result.containsKey(address)) {
			return result.get(address);
		}
		
		return Collections.EMPTY_MAP;
	}
	
	private static CaptainMemcacheClient getMemcacheClient(InetSocketAddress address) {
	    try {
            CaptainMemcacheClient client = new CaptainSpyMemcacheClient(address);
//            memcacheClients.put(address, client);
            ApiLogger.debug(String.format("create new memcache client for mc [%s]", address));
            return client;
        } catch (IOException e) {  
            ApiLogger.warn(String.format("create new memcache client for mc [%s] false", address), e); 
            	
        }
	    return null;
	}
	
	public static void shutdownClient(CaptainMemcacheClient client) {
	    if(client != null) {
	        try {
                client.shutdownClient();
            } catch (Exception e) {
                ApiLogger.warn("shutdown false, but i donnot care?", e); 
            }
	    }
	}
}
