package com.weibo.api.captain.common.memcache;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.Map;

import net.spy.memcached.MemcachedClient;

/**  
 * Package: com.weibo.api.captain.common.memcache  
 *  
 * File: CaptainMemcacheClientFrmSpy.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainSpyMemcacheClient implements CaptainMemcacheClient{

	private MemcachedClient memcachedClient;
	
	public CaptainSpyMemcacheClient(InetSocketAddress... ia) throws IOException{
		memcachedClient = new MemcachedClient(ia);
	}
	
	@Override
	public Map<SocketAddress, Map<String, String>> getStats() {
		return memcachedClient.getStats();
	}
	
	@Override
	public Map<SocketAddress, Map<String, String>> getStats(String statsArg) {
		return memcachedClient.getStats(statsArg);
	}

    @Override
    public void shutdownClient() {
        memcachedClient.shutdown();
    }
}
