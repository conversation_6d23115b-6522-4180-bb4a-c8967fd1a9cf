package com.weibo.api.captain.common.model;

import java.util.ArrayList;
import java.util.List;

import com.weibo.api.captain.common.util.CaptainJsonUtil;
import com.weibo.api.captain.common.util.CaptainUtil;
import org.apache.commons.lang.StringUtils;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.JsonBuilder;


/**
 * Package: com.weibo.api.captain.core.model
 * 
 * File: CacheAgentBizStat.java
 * 
 * Author: fishermen
 * 
 * Copyright @ 2015 Corpration Name
 * 
 */
public class CacheAgentBizStat implements JsonAble {

	private CacheAgentStat serverStat; 
	
	/** FIXME: 待cacheService上线带timestamp的版本后，此处去掉初始化 */
	private long timestamp = System.currentTimeMillis() / 1000;
	
	private long crequestTotal;
	private long cmdTotal;
	private long cmdRead;
	private long cmdHit;
	private long cmdTotalTime;
	private long cmdTimeouts;
	
	private CacheAgentSla sla = new CacheAgentSla();

	public CacheAgentBizStat(CacheAgentStat cacheAgentStat) {
		this.serverStat = cacheAgentStat;
	}
	
	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public long getCrequestTotal() {
		return crequestTotal;
	}

	public void setCrequestTotal(long crequestTotal) {
		this.crequestTotal = crequestTotal;
	}

	public long getCmdTotal() {
		return cmdTotal;
	}

	public void setCmdTotal(long cmdTotal) {
		this.cmdTotal = cmdTotal;
	}

	public long getCmdRead() {
		return cmdRead;
	}

	public void setCmdRead(long cmdRead) {
		this.cmdRead = cmdRead;
	}

	public long getCmdHit() {
		return cmdHit;
	}

	public void setCmdHit(long cmdHit) {
		this.cmdHit = cmdHit;
	}

	public long getCmdTotalTime() {
		return cmdTotalTime;
	}

	public void setCmdTotalTime(long cmdTotalTime) {
		this.cmdTotalTime = cmdTotalTime;
	}
	
	public long getCmdTimeouts() {
		return cmdTimeouts;
	}

	public void setCmdTimeouts(long cmdTimeouts) {
		this.cmdTimeouts = cmdTimeouts;
	}
	
	public CacheAgentSla getSla() {
		return sla;
	}
	
	public CacheAgentStat getServerStat() {
		return serverStat;
	}

	@Override
	public JsonBuilder toJsonBuilder() {
		JsonBuilder jb = new JsonBuilder();
		jb.append("timestamp", timestamp);
		jb.append("crequest_total", crequestTotal);
		jb.append("commands_total", cmdTotal);
		jb.append("commands_read", cmdRead);
		jb.append("commands_hit", cmdHit);
		jb.append("commands_total_time", cmdTotalTime);
		jb.append("total_server_timedout", cmdTimeouts);
		
		jb.append("sla", sla.toJsonBuilder());
		
		jb.flip();
		return jb;
	}
	
	@Override
	public String toJson() {
		return toJsonBuilder().toString();
	}
	
	public static String aggregateSlaJson(String bizName, List<CacheAgentBizStat> stats) {
		if(StringUtils.isBlank(bizName)) {
			ApiLogger.warn(String.format("found malformed param in CacheAgentBizStat.aggregateJson, bizName = [%s], stats = [%s]", bizName, stats));
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<JsonBuilder> jsonArr = new ArrayList<JsonBuilder>();
		List<CacheAgentSla> bizSlas = new ArrayList<CacheAgentSla>();
		for(CacheAgentBizStat stat : stats) {
			bizSlas.add(stat.getSla());
			jsonArr.add(stat.getSla().toJsonBuilder(stat.getServerStat().getAddr()));
		}
		
		CacheAgentSla sla = CacheAgentSla.aggregateSla(bizSlas);
		
		JsonBuilder jb = new JsonBuilder();
		
		jb.append("sub_biz", bizName);
		jb.append("aggregate", sla.toJson());
		jb.appendJsonArr("slas", jsonArr);
		
		jb.flip();
		return jb.toString();
	}
	
	@Override
	public String toString() {
		return toJson();
	}

	public void correctSla(CacheAgentBizStat lastStat) {
		if(lastStat == null) {
			return;
		}
		
		long seconds = this.timestamp - lastStat.timestamp;
		if(seconds < 1) {
			ApiLogger.warn(String.format("found malformed param in agentBizStat.correctSla, this.bizStat = [%s], lastBizStat = [%s]", this, lastStat));
			return;
		}
		
		long tps = (cmdTotal - lastStat.cmdTotal) / seconds;
		long timeoutsPerSecond = (cmdTimeouts - lastStat.cmdTimeouts) / seconds;
		// 注意处理除0的情况
		long avgTimeInMills = (cmdTotalTime - lastStat.cmdTotalTime) / CaptainUtil.roundToPositiveDivisor(cmdTotal - lastStat.cmdTotal);
		int hitRate = 100;
		if(this.cmdRead > lastStat.cmdRead) {
			hitRate = (int)((this.cmdHit - lastStat.cmdHit) * 100 / CaptainUtil.roundToPositiveDivisor(this.cmdRead - lastStat.cmdRead));
		}
		
		
		this.sla.setTps(tps);
		this.sla.setTimeoutsPerSecond(timeoutsPerSecond);
		this.sla.setAvgTimeInMills(avgTimeInMills);
		this.sla.setHitRate(hitRate);
		
	}

}
