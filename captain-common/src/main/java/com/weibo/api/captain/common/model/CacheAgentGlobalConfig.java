package com.weibo.api.captain.common.model;

import cn.sina.api.commons.util.JsonBuilder;


/**  
 * Package: com.weibo.api.captain.core.model  
 *  
 * File: CacheAgentGlobalConfig.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentGlobalConfig {

	private Long lruMaxMemory;
	private Integer getSignTime;
	private Integer heartBeatTime;
	private String sign;
	
	public Long getLruMaxMemory() {
		return lruMaxMemory;
	}

	public void setLruMaxMemory(Long lruMaxMemory) {
		this.lruMaxMemory = lruMaxMemory;
	}

	public Integer getGetSignTime() {
		return getSignTime;
	}

	public void setGetSignTime(Integer getSignTime) {
		this.getSignTime = getSignTime;
	}

	public Integer getHeartBeatTime() {
		return heartBeatTime;
	}

	public void setHeartBeatTime(Integer heartBeatTime) {
		this.heartBeatTime = heartBeatTime;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	@Override
	public String toString() {
		JsonBuilder jb = new JsonBuilder();
		if(lruMaxMemory != null) {
			jb.append(CacheAgentConfigElement.lru_max_memory.name(), lruMaxMemory);
		}
		
		if(getSignTime != null) {
			jb.append(CacheAgentConfigElement.get_sign_time.name(), getSignTime);
		}
		
		if(heartBeatTime != null) {
			jb.append(CacheAgentConfigElement.heart_beat_time.name(), heartBeatTime);
		}
		
		if(sign != null) {
			jb.append(CacheAgentConfigElement.sign.name(), sign);
		}
		
		jb.flip();
		return jb.toString();
	}
	
	
}
