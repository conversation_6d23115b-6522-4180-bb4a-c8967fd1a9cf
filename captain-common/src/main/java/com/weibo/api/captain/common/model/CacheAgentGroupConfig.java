package com.weibo.api.captain.common.model;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.Util;

import com.weibo.api.captain.common.CaptainConstants;
import com.weibo.api.captain.common.util.CaptainUtil;
import com.weibo.vintage.model.EndpointAddress;


/**
 * Package: com.weibo.api.captain.core.model
 *
 * File: CacheAgentGroupConfig.java
 *
 * 为了保持与yaml中的group配置属性一致，此处采用yaml 配置中的同名属性名，只是去掉了"_"，改用cameral 方式。
 *
 * Author: fishermen
 *
 * Copyright @ 2015 Corpration Name
 *
 */
public class CacheAgentGroupConfig {
    private String name;
    private String hash;
    private String distribution;
    private String hashTag;
    private Boolean saveTag;
    private Boolean autoEjecThosts;
    private Long timeout;
    private Long masterEffectiveTime;
    private Long lruTimeout;
    private Boolean redis;
    private Long serverRetryTimeout;
    private Integer serverFailureLimit;
    private Boolean partialReply;
    private Long exptime;
    private Boolean forceWriteAll;
	private Boolean updateSlaveL1;
	private Boolean localAffinity;
	private Integer flag;
    private List<String> master;
    private List<String> masterElapse;
    private List<List<String>> masterL1;
    private List<String> slave;  /* slave may be the master of another mem-set */
    private List<List<String>> slaveL1; /* slave l1 may be the l1 of another mem-set */
    private List<List<String>> ssdcache;

    public String getName() {
        return name;
    }

	public void setName(String name) {
		this.name = name;
	}

	public String getHash() {
		return hash;
	}

	public void setHash(String hash) {
		this.hash = hash;
	}

	public String getDistribution() {
		return distribution;
	}

	public void setDistribution(String distribution) {
		this.distribution = distribution;
	}

	public String getHashTag() {
		return hashTag;
	}

	public void setHashTag(String hashTag) {
		this.hashTag = hashTag;
	}

	public Boolean getSaveTag() {
		return saveTag;
	}

	public void setSaveTag(Boolean saveTag) {
		this.saveTag = saveTag;
	}

	public Boolean getAutoEjecThosts() {
		return autoEjecThosts;
	}

	public void setAutoEjecThosts(Boolean autoEjecThosts) {
		this.autoEjecThosts = autoEjecThosts;
	}

	public Long getTimeout() {
		return timeout;
	}

	public void setTimeout(Long timeout) {
		this.timeout = timeout;
	}

	public Long getLruTimeout() {
		return lruTimeout;
	}

	public void setLruTimeout(Long lruTimeout) {
		this.lruTimeout = lruTimeout;
	}

	public Boolean getRedis() {
		return redis;
	}

	public void setRedis(Boolean redis) {
		this.redis = redis;
	}

	public Long getServerRetryTimeout() {
		return serverRetryTimeout;
	}

	public void setServerRetryTimeout(Long serverRetryTimeout) {
		this.serverRetryTimeout = serverRetryTimeout;
	}

	public Integer getServerFailureLimit() {
		return serverFailureLimit;
	}

	public void setServerFailureLimit(Integer serverFailureLimit) {
		this.serverFailureLimit = serverFailureLimit;
	}

	public Boolean getPartialReply() {
		return partialReply;
	}

	public void setPartialReply(Boolean partialReply) {
		this.partialReply = partialReply;
	}

	public Long getExptime() {
		return exptime;
	}

	public void setExptime(Long exptime) {
		this.exptime = exptime;
	}

	public Long getMasterEffectiveTime() {
		return masterEffectiveTime;
	}

	public void setMasterEffectiveTime(Long masterEffectiveTime) {
		this.masterEffectiveTime = masterEffectiveTime;
	}

	public List<String> getMaster() {
		return master;
	}

	public void setMaster(List<String> master) {
		this.master = master;
	}

	public List<String> getMasterElapse() {
		return masterElapse;
	}

	public void setMasterElapse(List<String> masterElapse) {
		this.masterElapse = masterElapse;
	}

	public List<List<String>> getMasterL1() {
		return masterL1;
	}

	public void setMasterL1(List<List<String>> masterL1) {
		this.masterL1 = masterL1;
	}

	public List<String> getSlave() {
		return slave;
	}

	public void setSlave(List<String> slave) {
		this.slave = slave;
	}

	public List<List<String>> getSlaveL1() {
		return slaveL1;
	}

	public void setSlaveL1(List<List<String>> slaveL1) {
		this.slaveL1 = slaveL1;
	}

	public List<List<String>> getSsdcache() {
		return ssdcache;
	}

	public void setSsdcache(List<List<String>> ssdcache) {
		this.ssdcache = ssdcache;
	}

    /**
     * 抽取出所有的mc node
     *
     * @return
     */
    public List<EndpointAddress> extractAllMcNodes() {

        List<EndpointAddress> addrs = new ArrayList<EndpointAddress>();
        addrs.addAll(parseHostPorts(master));
        addrs.addAll(parseHostPorts(masterElapse));

        if (masterL1 != null && masterL1.size() > 0) {
            for (List<String> servers : masterL1) {
                addrs.addAll(parseHostPorts(servers));
            }
        }

        addrs.addAll(parseHostPorts(slave));
        if (slaveL1 != null && slaveL1.size() > 0) {
            for (List<String> servers : slaveL1) {
                addrs.addAll(parseHostPorts(servers));
            }
        }

        if (ssdcache != null && ssdcache.size() > 0) {
            for (List<String> servers : ssdcache) {
                addrs.addAll(parseHostPorts(servers));
            }
        }

        return addrs;
    }

    /**
     * 解析hostport为endpointAddress
     * host可能的格式： ip:port:weight, ip:port:weight nick, ip:port nick, ip:port
     *
     * @param hostPort
     * @return
     */
    @SuppressWarnings("unchecked")
    private List<EndpointAddress> parseHostPorts(List<String> servers) {
        if (servers == null || servers.size() == 0) {
            return Collections.EMPTY_LIST;
        }

        List<EndpointAddress> addrList = new ArrayList<EndpointAddress>();
        EndpointAddress addr = null;
        for (String hostStr : servers) {
            hostStr = hostStr.trim();
            String[] hostOriStrArr = hostStr.split(" ");
            String[] hostPortWeightArr = hostOriStrArr[0].split(CaptainConstants.COLON);

            int port = Util.convertInt(hostPortWeightArr[1]);


            try {
                addr = new EndpointAddress(hostPortWeightArr[0], port);
                addrList.add(addr);
            } catch (Exception e) {
                ApiLogger.warn(String.format("Found invalid hostPort [%s] in [%s]", hostPortWeightArr[0], servers), e);
            }
        }

        return addrList;
    }

    @Override
    public String toString() {
        return new StringBuilder(256)
                .append("{")
                .append("name:").append(name)
                .append(", ").append(CacheAgentConfigElement.hash).append(":").append(hash)
                .append(", ").append(CacheAgentConfigElement.distribution).append(":").append(distribution)
                .append(", ").append(CacheAgentConfigElement.hash_tag).append(":").append(hashTag)
                .append(", ").append(CacheAgentConfigElement.save_tag).append(":").append(saveTag)
                .append(", ").append(CacheAgentConfigElement.auto_eject_hosts).append(":").append(autoEjecThosts)
                .append(", ").append(CacheAgentConfigElement.timeout).append(":").append(timeout)
                .append(", ").append(CacheAgentConfigElement.server_retry_timeout).append(":").append(serverRetryTimeout)
                .append(", ").append(CacheAgentConfigElement.server_failure_limit).append(":").append(serverFailureLimit)
                .append(", ").append(CacheAgentConfigElement.partial_reply).append(":").append(partialReply)
                .append(", ").append(CacheAgentConfigElement.exptime).append(":").append(exptime)
                .append(", ").append(CacheAgentConfigElement.force_write_all).append(":").append(forceWriteAll==null?false:forceWriteAll)
				.append(", ").append(CacheAgentConfigElement.update_slave_l1).append(":").append(updateSlaveL1==null?true:updateSlaveL1)
				.append(", ").append(CacheAgentConfigElement.local_affinity).append(":").append(localAffinity==null?false:localAffinity)
				.append(", ").append(CacheAgentConfigElement.flag).append(":").append(flag == null ? 0 : flag)
                .append(", ").append(CacheAgentConfigElement.master_effective_time).append(":").append(masterEffectiveTime)
                .append(", ").append(CacheAgentConfigElement.master).append(":").append(master)
                .append(", ").append(CacheAgentConfigElement.master_elapse).append(":").append(masterElapse)
                .append(", ").append(CacheAgentConfigElement.master_l1).append(":").append(masterL1)
                .append(", ").append(CacheAgentConfigElement.slave).append(":").append(slave)
                .append(", ").append(CacheAgentConfigElement.slave_l1).append(":").append(slaveL1)
                .append(", ").append(CacheAgentConfigElement.ssdcache).append(":").append(ssdcache)
                .append("}")
                .toString();
    }

    public Boolean getForceWriteAll() {
        if (null == forceWriteAll) {
            return false;
        }
        return forceWriteAll;
    }

    public void setForceWriteAll(Boolean forceWriteAll) {
        if(CaptainUtil.ignoreNewField()){
            return;
        }
        if (forceWriteAll == null) {
            forceWriteAll = false;
        }
        this.forceWriteAll = forceWriteAll;
    }

	public Boolean getUpdateSlaveL1() {
		if (null == updateSlaveL1) {
			return true;
		}
		return updateSlaveL1;
	}

	public void setUpdateSlaveL1(Boolean updateSlaveL1) {
		if(CaptainUtil.ignoreNewField()){
			return;
		}
		if (updateSlaveL1 == null) {
			updateSlaveL1 = true;
		}
		this.updateSlaveL1 = updateSlaveL1;
	}

	public Boolean getlocalAffinity() {
		if(null == localAffinity){
			return false;
		}
		return localAffinity;
	}

	public void setlocalAffinity(Boolean localAffinity) {
		if(CaptainUtil.ignoreNewField()){
			return;
		}
		if (localAffinity == null) {
			localAffinity = false;
		}
		this.localAffinity = localAffinity;
	}

	public Integer getFlag() {
		if (this.flag == null) {
			return 0;
		}
		return this.flag;
	}

	public void setFlag(Integer flag) {
		if (flag == null) {
			flag = 0;
		}
		this.flag = flag;
	}
}
