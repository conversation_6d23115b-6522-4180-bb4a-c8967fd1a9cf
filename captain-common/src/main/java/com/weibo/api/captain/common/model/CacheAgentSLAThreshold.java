package com.weibo.api.captain.common.model;

import cn.sina.api.commons.util.JsonBuilder;

/**  
 * Package: com.weibo.api.captain.core  
 *  
 * File: SLAThreshold.java   
 *  
 * SLA指标的阀值，对具体业务sla不同，可以通过spring bean设置不同的值。
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentSLAThreshold implements JsonAble {

	private int tps = 20000;
	
	private int ctps = 20000;
	
	/** 超时个数 */
	private long timeoutsPerSecond = 10;
	
	private int avgTimeInMills = 5;
	
	/** 命中率， 基数是100*/
	private int hitRate = 80;
	
	public int getTps() {
		return tps;
	}
	public void setTps(int tps) {
		this.tps = tps;
	}
	
	public int getCtps() {
		return ctps;
	}
	public void setCtps(int ctps) {
		this.ctps = ctps;
	}
	public long getTimeoutsPerSecond() {
		return timeoutsPerSecond;
	}
	public void setTimeoutsPerSecond(long timeoutsPerSecond) {
		this.timeoutsPerSecond = timeoutsPerSecond;
	}
	public int getAvgTimeInMills() {
		return avgTimeInMills;
	}
	public void setAvgTimeInMills(int avgTimeInMills) {
		this.avgTimeInMills = avgTimeInMills;
	}
	public int getHitRate() {
		return hitRate;
	}
	public void setHitRate(int hitRate) {
		this.hitRate = hitRate;
	}
	
	@Override
	public int hashCode() {
		int hash = 0;
		int factor = 31;
		hash += hash * factor + tps;
		hash += hash * factor + ctps;
		hash += hash * factor + timeoutsPerSecond;
		hash += hash * factor + avgTimeInMills;
		hash += hash * factor + hitRate;
		
		return hash;
	}
	
	@Override
	public boolean equals(Object obj) {
		if(!(obj instanceof CacheAgentSLAThreshold)) {
			return false;
		}
		
		CacheAgentSLAThreshold caObj = (CacheAgentSLAThreshold)obj;
		return this.tps == caObj.tps
				&& this.ctps == caObj.ctps
				&& this.timeoutsPerSecond == caObj.timeoutsPerSecond
				&& this.avgTimeInMills == caObj.avgTimeInMills
				&& this.hitRate == caObj.hitRate;
	}
	
	@Override
	public JsonBuilder toJsonBuilder() {
		JsonBuilder jb = new JsonBuilder();
		jb.append("tps", tps);
		jb.append("ctps", ctps);
		jb.append("timeouts_per_second", timeoutsPerSecond);
		jb.append("average_time", avgTimeInMills);
		jb.append("hit_rate", hitRate);
		
		jb.flip();
		return jb;
	}
	
	@Override
	public String toJson() {
		return toJsonBuilder().toString();
	}
	
	@Override
	public String toString() {
		return toJson();
	}
	
	
	
}
