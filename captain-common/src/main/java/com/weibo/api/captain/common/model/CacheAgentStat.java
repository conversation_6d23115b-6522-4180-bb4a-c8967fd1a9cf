package com.weibo.api.captain.common.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.weibo.api.captain.common.CaptainConstants;
import com.weibo.api.captain.common.util.CaptainJsonUtil;
import com.weibo.api.captain.common.util.CaptainUtil;
import org.apache.commons.lang.StringUtils;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.JsonBuilder;

import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.model  
 *  
 * File: CacheAgentStat.java 
 * 
 * <pre>
 *	cacheAgent 的统计信息，包括子业务、整体统计。
 * </pre>
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentStat implements JsonAble {

	private EndpointAddress addr;
	private String version;
	private String configGroup;
	private String configKey = CaptainConstants.DEFAULT_STATIC_CONFIG_KEY;
	private String sign;
	
	private long uptime;
	private int connections;
	
	private long crequestTotal;
	
	private long cmdTotal;
	private long cmdGet;
	private long cmdGets;
	private long cmdSet;
	private long cmdCas;
	private long cmdHit;
	private long cmdRead;
	private long cmdWrite;
	private long cmdDelete;
	private long cmdTotalTime;
	private long totalTimedout;
	
	private CacheAgentSla sla = new CacheAgentSla();
	
	/** 每个子业务的sla 统计 */
	private Map<String, CacheAgentBizStat> bizStats = new ConcurrentHashMap<String, CacheAgentBizStat>();
	
	public CacheAgentStat(EndpointAddress addr) {
		this.addr = addr;
	}
	
	public EndpointAddress getAddr() {
		return addr;
	}

	public void setAddr(EndpointAddress addr) {
		this.addr = addr;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getConfigGroup() {
		return configGroup;
	}

	public void setConfigGroup(String configGroup) {
		this.configGroup = configGroup;
	}

	public String getConfigKey() {
		return configKey;
	}
	
	public long getUptime() {
		return uptime;
	}

	public void setUptime(long uptime) {
		this.uptime = uptime;
	}

	public long getCrequestTotal() {
		return crequestTotal;
	}

	public void setCrequestTotal(long crequestTotal) {
		this.crequestTotal = crequestTotal;
	}

	public void setConfigKey(String configKey) {
		this.configKey = configKey;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public int getConnections() {
		return connections;
	}

	public void setConnections(int connections) {
		this.connections = connections;
	}

	public long getCmdTotal() {
		return cmdTotal;
	}

	public void setCmdTotal(long cmdTotal) {
		this.cmdTotal = cmdTotal;
	}

	public long getCmdGet() {
		return cmdGet;
	}

	public void setCmdGet(long cmdGet) {
		this.cmdGet = cmdGet;
	}

	public long getCmdGets() {
		return cmdGets;
	}

	public void setCmdGets(long cmdGets) {
		this.cmdGets = cmdGets;
	}

	public long getCmdSet() {
		return cmdSet;
	}

	public void setCmdSet(long cmdSet) {
		this.cmdSet = cmdSet;
	}

	public long getCmdCas() {
		return cmdCas;
	}

	public void setCmdCas(long cmdCas) {
		this.cmdCas = cmdCas;
	}

	public long getCmdHit() {
		return cmdHit;
	}

	public void setCmdHit(long cmdHit) {
		this.cmdHit = cmdHit;
	}

	public long getCmdRead() {
		return cmdRead;
	}

	public void setCmdRead(long cmdRead) {
		this.cmdRead = cmdRead;
	}

	public long getCmdWrite() {
		return cmdWrite;
	}

	public void setCmdWrite(long cmdWrite) {
		this.cmdWrite = cmdWrite;
	}

	public long getCmdDelete() {
		return cmdDelete;
	}

	public void setCmdDelete(long cmdDelete) {
		this.cmdDelete = cmdDelete;
	}

	public long getCmdTotalTime() {
		return cmdTotalTime;
	}

	public void setCmdTotalTime(long cmdTotalTime) {
		this.cmdTotalTime = cmdTotalTime;
	}

	public long getTotalTimedout() {
		return totalTimedout;
	}

	public void setTotalTimedout(long totalTimedout) {
		this.totalTimedout = totalTimedout;
	}
	
	public CacheAgentSla getSla() {
		return sla;
	}

	public Map<String, CacheAgentBizStat> getBizStats() {
		return bizStats;
	}

	public void setBizStats(Map<String, CacheAgentBizStat> bizStats) {
		this.bizStats = bizStats;
	}

	/**
	 * 修正cacheService的sla，因为cacheService返回的是固定时常计算的，这里直接计算出更准确的数值
	 * @param lastStat
	 */
	public void correctSla(CacheAgentStat lastStat) {
		//如果lastStat 为null，直接使用cacheService自己的统计值
		if(lastStat == null) {
			return;
		}
		
		long seconds = uptime - lastStat.uptime;
		if(seconds < 1) {
			ApiLogger.warn(String.format("found malformed param in agentStat.correctSla, stat: %s, lastSla: %s", this, lastStat));
			return;
		}
		
		long tps = (cmdTotal - lastStat.cmdTotal) / seconds;
		long timeoutsPerSecond = (totalTimedout - lastStat.totalTimedout) / seconds;
		// 注意处理除0的情况
		long avgTimeInMills = (cmdTotalTime - lastStat.cmdTotalTime) / CaptainUtil.roundToPositiveDivisor(cmdTotal - lastStat.cmdTotal);
		int hitRate = 100;
		if(this.cmdRead > lastStat.cmdRead) {
			hitRate = (int)((this.cmdHit - lastStat.cmdHit) * 100L / CaptainUtil.roundToPositiveDivisor(this.cmdRead - lastStat.cmdRead));
		}
		
		
		this.sla.setTps(tps);
		this.sla.setTimeoutsPerSecond(timeoutsPerSecond);
		this.sla.setAvgTimeInMills(avgTimeInMills);
		this.sla.setHitRate(hitRate);
		
	}

	public JsonBuilder toJsonBuilderForConsistence() {
		JsonBuilder jb = new JsonBuilder();
		jb.append("host", this.addr.getHost());
		jb.append("port", this.addr.getPort());
		
		if(!StringUtils.isBlank(version)) {
			jb.append("version", version);
		}
		
		if(!StringUtils.isBlank(configGroup)) {
			jb.append("conf_group", configGroup);
		}
		
		if(!StringUtils.isBlank(configKey)) {
			jb.append("conf_key", configKey);
		}
		
		if(!StringUtils.isBlank(sign)) {
			jb.append("sign", sign);
		}
		
		jb.flip();
		return jb;
	}
	
	@Override
	public JsonBuilder toJsonBuilder() {
		JsonBuilder jb = new JsonBuilder();
		if(addr != null) {
			jb.append("host", addr.getHost());
			jb.append("port", addr.getPort());
		}
		
		if(!StringUtils.isBlank(version)) {
			jb.append("version", version);
		}
		
		if(!StringUtils.isBlank(configGroup)) {
			jb.append("conf_group", configGroup);
		}
		
		if(!StringUtils.isBlank(configKey)) {
			jb.append("conf_key", configKey);
		}
		
		if(!StringUtils.isBlank(sign)) {
			jb.append("sign", sign);
		}
		
		jb.append("connections", connections);
		
				
		if(sla != null) {
			JsonBuilder slaJb = new JsonBuilder();
			slaJb.append("tps", sla.getTps());
			slaJb.append("ctps", sla.getCtps());
			slaJb.append("timeouts_per_second", sla.getTimeoutsPerSecond());
			slaJb.append("average_time", sla.getAvgTimeInMills());
			slaJb.append("hit_rate", sla.getHitRate());
			slaJb.flip();
			
			jb.append("sla", slaJb);
		}
		
		jb.flip();
		return jb;
	}
	
	@Override
	public String toJson() {
		return toJsonBuilder().toString();
	}
	
	public static String aggregateJson(List<CacheAgentStat> stats) {
		if(stats == null || stats.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<JsonBuilder> jsonList = new ArrayList<JsonBuilder>();
		for(CacheAgentStat stat : stats) {
			jsonList.add(stat.toJsonBuilder());
		}
		
		JsonBuilder jb = new JsonBuilder();
		jb.appendJsonArr("stats", jsonList);
		jb.flip();
		
		return jb.toString();
	}
	
	public static String aggregateSlaJson(List<CacheAgentStat> stats) {
		if(stats == null || stats.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<CacheAgentSla> slas = new ArrayList<CacheAgentSla>();
		List<JsonBuilder> jsonList = new ArrayList<JsonBuilder>();
		for(CacheAgentStat stat : stats) {
			jsonList.add(stat.getSla().toJsonBuilder(stat.getAddr()));
			slas.add(stat.getSla());
		}
		
		CacheAgentSla aggregateSla = CacheAgentSla.aggregateSla(slas);
		JsonBuilder jb = new JsonBuilder();
		jb.append("aggregate", aggregateSla.toJson());
		jb.appendJsonArr("slas", jsonList);
		
		jb.flip();
		
		return jb.toString();
	}
	
	public static String toJsonForAbnormalService(String serviceId, Map<String, List<CacheAgentStat>> clusterStats, List<EndpointAddress> unreachableNodes) {
		if(StringUtils.isBlank(serviceId)
				|| (clusterStats.isEmpty() && unreachableNodes.isEmpty())) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		JsonBuilder serviceJb = new JsonBuilder();
		serviceJb.append("service", serviceId);
		
		List<JsonBuilder> clusterJsons = new ArrayList<JsonBuilder>();
		for(Map.Entry<String, List<CacheAgentStat>> entry : clusterStats.entrySet()) {
			JsonBuilder clusterJb = new JsonBuilder();
			clusterJb.append("cluster", entry.getKey());
			
			List<JsonBuilder> jsonList = new ArrayList<JsonBuilder>();
			for(CacheAgentStat stat : entry.getValue()) {
				jsonList.add(stat.toJsonBuilder());
			}
			clusterJb.appendJsonArr("stats", jsonList);
			
			clusterJb.flip();
			clusterJsons.add(clusterJb);
		}
		
		serviceJb.appendJsonArr("clusters", clusterJsons);
		
		if(unreachableNodes.size() > 0) {
			List<JsonBuilder> unreachableJbs = new ArrayList<JsonBuilder>();
			for(EndpointAddress addr : unreachableNodes) {
				JsonBuilder tmpJb = new JsonBuilder();
				tmpJb.append("host", addr.getHost());
				tmpJb.append("port", addr.getPort());
				tmpJb.flip();
				
				unreachableJbs.add(tmpJb);
			}
			serviceJb.appendJsonArr("unreachable", unreachableJbs);
		}
		
		serviceJb.flip();
		return serviceJb.toString();
		
	}
	
	
}
