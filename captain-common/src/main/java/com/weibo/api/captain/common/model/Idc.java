/**  
 * Project Name:captain-assigner  
 * File Name:Idc.java  
 * Package Name:com.weibo.api.captain.assigner.util  
 * Date:2016年8月3日下午10:38:38  
 * Copyright (c) 2016, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.common.model;  

/**  
 * <pre>  
 * ClassName:Idc 
 * 
 * 所有机房信息
 * yf:永丰，tc:土城,aliyun:阿里云，aliyun_tc:阿里云－土城，bx：北显,yz：亦庄,xdl：新大楼,yhg：雍和宫,ft：丰台,ja：静安,xd：西单,qxg：七星岗,nfjd：南方基地,sx：沙溪,xg：香港,aliyun_wlcb:阿里云乌兰察布,aliyun_zb：阿里云张北,wq:环京武清机房,aliyun_wq:阿里云武清,huawei_wq:华为云武清
 * <pre/>   
 * Date:     2016年8月3日 下午10:38:38 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public enum Idc {
    yf,tc,aliyun,aliyun_tc,bx,yz,xdl,yhg,ft,ja,xd,qxg,nfjd,sx,xg,huawei,huawei_tc,jxg,qxj,aliyun_wlcb,aliyun_zb,wq,aliyun_wq,huawei_wq
}