package com.weibo.api.captain.common.model;

import java.util.ArrayList;
import java.util.List;

import com.weibo.api.captain.common.util.CaptainJsonUtil;
import com.weibo.api.captain.common.util.CaptainUtil;
import org.apache.commons.collections.CollectionUtils;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.JsonBuilder;

import com.weibo.vintage.model.EndpointAddress;


/**  
 * Package: com.weibo.api.captain.core.model  
 *  
 * File: McStat.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class MemcacheNodeStat implements JsonAble {

	private EndpointAddress address;
	private long uptime;
	private long currItems;
	private long bytes;
	private int currConnections;
	private long cmdGet;
	private long cmdSet;
	private long getHits;
	private long evictions;
	private long bytesRead;
	private long bytesWritten;
	private long limitMaxBytes;
	
	private MemcacheSla sla = new MemcacheSla();
	
	public EndpointAddress getAddress() {
		return address;
	}

	public void setAddress(EndpointAddress address) {
		this.address = address;
	}

	public long getUptime() {
		return uptime;
	}

	public void setUptime(long uptime) {
		this.uptime = uptime;
	}


	public long getCurrItems() {
		return currItems;
	}

	public void setCurrItems(long currItems) {
		this.currItems = currItems;
	}

	public long getBytes() {
		return bytes;
	}

	public void setBytes(long bytes) {
		this.bytes = bytes;
	}

	public int getCurrConnections() {
		return currConnections;
	}

	public void setCurrConnections(int currConnections) {
		this.currConnections = currConnections;
	}

	public long getCmdGet() {
		return cmdGet;
	}

	public void setCmdGet(long cmdGet) {
		this.cmdGet = cmdGet;
	}

	public long getCmdSet() {
		return cmdSet;
	}

	public void setCmdSet(long cmdSet) {
		this.cmdSet = cmdSet;
	}

	public long getGetHits() {
		return getHits;
	}

	public void setGetHits(long getHits) {
		this.getHits = getHits;
	}

	public long getEvictions() {
		return evictions;
	}

	public void setEvictions(long evictions) {
		this.evictions = evictions;
	}

	public long getBytesRead() {
		return bytesRead;
	}

	public void setBytesRead(long bytesRead) {
		this.bytesRead = bytesRead;
	}

	public long getBytesWritten() {
		return bytesWritten;
	}

	public void setBytesWritten(long bytesWritten) {
		this.bytesWritten = bytesWritten;
	}

	public long getLimitMaxBytes() {
		return limitMaxBytes;
	}

	public void setLimitMaxBytes(long limitMaxBytes) {
		this.limitMaxBytes = limitMaxBytes;
	}
	
	public MemcacheSla getSla() {
		return sla;
	}

	public void setSla(MemcacheSla sla) {
		this.sla = sla;
	}

	public void correctSla(MemcacheNodeStat lastStat) {
		if(lastStat == null) {
			return;
		}
		
		long seconds = this.uptime - lastStat.uptime;
		if(seconds < 1) {
			ApiLogger.warn(String.format("ignore correctSla for stats are too frequent, stat = [%s], lastStat = [%s]", this, lastStat));
			return;
		}
		
		long tps = (cmdGet + cmdSet - lastStat.cmdGet - lastStat.cmdSet) / seconds;
		long inFlow = (bytesRead - lastStat.bytesRead) / seconds;
		long outFlow = (bytesWritten - lastStat.bytesWritten) / seconds;
		long evictionPerSecond = (evictions - lastStat.evictions) /seconds;
		int hitRate = 100;
		if(cmdGet > lastStat.cmdGet) {
			hitRate = (int)((getHits - lastStat.getHits) * 100 / CaptainUtil.roundToPositiveDivisor(cmdGet - lastStat.cmdGet));
		}
		
		
		sla.setTps(tps);
		sla.setHitRate(hitRate);
		sla.setInFlow(inFlow);
		sla.setOutFlow(outFlow);
		sla.setEvictionPerSecond(evictionPerSecond);
	}

	@Override
	public JsonBuilder toJsonBuilder(){
		JsonBuilder jb = new JsonBuilder();
		if(address != null) {
			jb.append("host", address.getHost());
			jb.append("port", address.getPort());
		}
		jb.append("uptime", uptime);
		jb.append("curr_items", currItems);
		jb.append("bytes", bytes);
		jb.append("curr_connections", currConnections);
		jb.append("cmd_get", cmdGet);
		jb.append("cmd_set", cmdSet);
		jb.append("get_hits", getHits);
		jb.append("evictions", evictions);
		jb.append("bytes_read", bytesRead);
		jb.append("bytes_written", bytesWritten);
		jb.append("limit_maxbytes", limitMaxBytes);
		
		jb.append("sla", sla.toJsonBuilder());
		
		jb.flip();
		return jb;
	}
	
	@Override
	public String toJson() {
		return toJsonBuilder().toString();
	}
	
	public String toSlaJson (){
		return sla.toJsonBuilder(address).toString();
	}
	
	@Override
	public String toString() {
		return toJson();	
	}
	
	public static String aggregateToJson(List<MemcacheNodeStat> stats) {
		if(CollectionUtils.isEmpty(stats)) {
			return null;
		}
		
		List<JsonBuilder> statJbs = new ArrayList<JsonBuilder>(stats.size());
		for(MemcacheNodeStat st : stats) {
			statJbs.add(st.toJsonBuilder());
		}
		
		JsonBuilder jb = new JsonBuilder();
		jb.appendJsonArr("stats", statJbs);
		jb.flip();
		
		return jb.toString();
	}
	
	public static String aggregateToSlaJson(List<MemcacheNodeStat> stats){
		if(CollectionUtils.isEmpty(stats)) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<JsonBuilder> slaJbs = new ArrayList<JsonBuilder>();
		List<MemcacheSla> slas = new ArrayList<MemcacheSla>();
		
		for(MemcacheNodeStat st : stats) {
			slas.add(st.getSla());
			slaJbs.add(st.getSla().toJsonBuilder(st.getAddress()));
		}
		
		MemcacheSla aggregateSla = MemcacheSla.aggregateSla(slas);
		
		JsonBuilder jb = new JsonBuilder();
		jb.append("aggregate", aggregateSla.toJson());
		jb.appendJsonArr("slas", slaJbs);
		
		jb.flip();
		return jb.toString();
	}

}
