/**
 * Project Name:captain-common File Name:SLA.java Package Name:com.weibo.api.captain.common.model
 * Date:2016年5月13日下午3:08:00 Copyright (c) 2016, @weibo All Rights Reserved.
 * 
 */

package com.weibo.api.captain.common.model;

/**
 * <pre> ClassName:SLA
 * 
 * 系统SLA，这个先放到这里，后续如果系统规模足够大，则新建core工程，这些公共model可以移到core project。
 * 
 * <pre/> Date: 2016年5月13日 下午3:08:00 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public class SLA {

    private int readTps;
    private int writeTps;
    private float hitPercent;

    public int getReadTps() {
        return readTps;
    }

    public void setReadTps(int readTps) {
        this.readTps = readTps;
    }

    public int getWriteTps() {
        return writeTps;
    }

    public void setWriteTps(int writeTps) {
        this.writeTps = writeTps;
    }

    public float getHitPercent() {
        return hitPercent;
    }

    public void setHitPercent(float hitPercent) {
        this.hitPercent = hitPercent;
    }

}

