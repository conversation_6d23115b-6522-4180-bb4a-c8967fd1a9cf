package com.weibo.api.captain.common.service;
/**  
 * Package: com.weibo.api.captain.core.service  
 *  
 * File: StaticConfigService.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public interface CacheAgentStaticConfigService {

	/**
	 * 根据groupId、key获取静态配置
	 * @param groupId
	 * @param key
	 * @return
	 */
	String getConfig(String groupId, String key);
	
	/**
	 * 更新静态配置
	 * @param groupId
	 * @param key
	 * @param configStr
	 * @return
	 */
	String register(String groupId, String key, String configStr);
	
	/**
	 * 取消注册
	 * @param groupId
	 * @param key
	 * @return
	 */
	String unregister(String groupId, String key);
}
