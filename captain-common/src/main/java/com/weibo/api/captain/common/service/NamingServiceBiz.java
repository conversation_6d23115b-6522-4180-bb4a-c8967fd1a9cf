package com.weibo.api.captain.common.service;

import java.util.List;

import com.weibo.vintage.model.NamingServiceCluster;

/**  
 * Package: com.weibo.api.captain.core.config.service  
 *  
 * File: NamingService.java 
 * 
 *  <pre>
 *   vintage-client 不提供获取业务的所有集群列表，所以这里还是直接用http获取吧。
 *   cacheAgent是用命名服务动态注册到configServer，这里用来获取各个服务的cacheAgent列表。
 *  </pre>
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public interface NamingServiceBiz {

	/**
	 * <pre>
	 * 访问configServer，从而获取某个命名服务在所有机房的部署情况。
	 * 命名服务的获取流程：
	 * 1 获取业务对应的所有clusterId；
	 * 2 根据serverId、clusterId获取所有的serverNode
	 * 
	 * </pre>
	 * @param bizName
	 * @return
	 */
	List<NamingServiceCluster> getServerCluster(String bizName);
	
	/**
	 * 获取业务在某个cluster的部署情况
	 * @param bizName
	 * @param clusterName
	 * @return
	 */
	NamingServiceCluster getServerCluster(String bizName, String clusterName);
	
}
