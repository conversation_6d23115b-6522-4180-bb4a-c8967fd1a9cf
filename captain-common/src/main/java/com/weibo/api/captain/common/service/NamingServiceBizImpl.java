package com.weibo.api.captain.common.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import com.weibo.api.captain.common.util.ApacheHttpClient;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.JsonNode;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.common.util.CaptainHttpUtil;
import com.weibo.api.captain.common.util.CaptainJsonUtil;
import com.weibo.vintage.model.HttpResponseMessage;
import com.weibo.vintage.model.HttpStatusCode;
import com.weibo.vintage.model.NamingServiceCluster;
import com.weibo.vintage.model.ResponsePacket;
import com.weibo.vintage.utils.JsonHelper;
import com.weibo.vintage.utils.UrlHelper;
import com.weibo.vintage.utils.VintageConstants;

/**  
 * Package: com.weibo.api.captain.core.config.service.impl  
 *  
 * File: NamingServiceImpl.java
 * <pre>
 * 访问configServer，从而获取某个命名服务在所有机房的部署情况。
 * 命名服务的获取流程：
 * 1 获取业务对应的所有clusterId；
 * 2 根据serverId、clusterId获取所有的serverNode
 * 
 * NamingServiceClient 只能处理业务接口，对于运维接口不提供相关功能，所以这里需要增加vintage-client
 * 无法处理的接口功能。 
 *  
 *  </pre>
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class NamingServiceBizImpl implements NamingServiceBiz {	
	/** 命名服务的后台管理接口 */
	private static final String URL_NAMING_ADMIN = "/naming/admin";
	
	/** 查看某个服务的集群列表 */
	private static final String ACTION_ADMIN_GET_CLUSTER = "getcluster";
	
	/** 命名服务的服务节点管理接口 */
	private static final String URL_NAMING_SERVICE = "/naming/service";
	
	/** 查看某服务的集群下所有服务节点 */
	private static final String ACTION_SERVICE_LOOKUP = "lookup";
	
	private String baseUrl;
	
	@Resource(name="httpClient")
	private ApacheHttpClient httpClient;
	
	public String getBaseUrl() {
		return baseUrl;
	}

	public void setBaseUrl(String baseUrl) {
		this.baseUrl = baseUrl;
	}

	public ApacheHttpClient getHttpClient() {
		return httpClient;
	}

	public void setHttpClient(ApacheHttpClient httpClient) {
		this.httpClient = httpClient;
	}

	/**
	 * 
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<NamingServiceCluster> getServerCluster(String serviceName) {
		if(StringUtils.isBlank(serviceName)) {
			return Collections.EMPTY_LIST;
		}
		
		//获取所有的cluster_id
		String url = buildAdminUrl() +
				UrlHelper.buildUrlParams("action", ACTION_ADMIN_GET_CLUSTER, "service", serviceName);
		List<String> clusterNames = queryNamingClusterId(url);
		
		if(clusterNames == null || clusterNames.size() == 0){
			return Collections.EMPTY_LIST;
		}
		
		//获取所有的nodeId
		List<NamingServiceCluster> clusters = new ArrayList<NamingServiceCluster>();
		for(String name : clusterNames) {
			NamingServiceCluster cluster = getServerCluster(serviceName, name);
			if(cluster != null) {
				clusters.add(cluster);
			}
		}
				
		return clusters;
	}
	
	@Override
	/**
	 * 调用示例：curl "http://ip:port/naming/service?action=lookup&service=yf-rpc-test&cluster=user-pool1" 
	 */
	public NamingServiceCluster getServerCluster(String serviceName,
			String clusterName) {
		String url = buildCommonUrl() + UrlHelper.buildUrlParams("action", ACTION_SERVICE_LOOKUP, "service", serviceName, "cluster", clusterName);
		return queryNamingCluster(url);
	}
	
	private String buildCommonUrl(){
		return this.baseUrl + URL_NAMING_SERVICE + "?";
	}
	
	private String buildAdminUrl(){
		return this.baseUrl + URL_NAMING_ADMIN + "?";
	}
	
	private List<String> queryNamingClusterId(String url){		
		
		List<String> clusterIds = null;
		HttpResponseMessage message = CaptainHttpUtil.doHttpGet(httpClient, url);
		
		if(message == null){
			ApiLogger.warn(String.format("Query configServer for namingService false, url = %s, rsMsg = null", url));
			return Collections.EMPTY_LIST;
		}
		
		if(message.getStatusCode() != HttpStatusCode.NOT_MODIFIED) {
			ResponsePacket packet = CaptainHttpUtil.parseResponse(message.getContent());
			if(CaptainHttpUtil.validateResponse(packet, VintageConstants.CONFIG_SERVICE_LOOKUP)) {
				String body = packet.getBody();
				JsonNode jsNode = JsonHelper.parserStringToJsonNode(body);
				if(jsNode != null){
					clusterIds = CaptainJsonUtil.getJsonMultiValues(jsNode.get("clusters"));
				}
			}
		}
		
		ApiLogger.info(String.format("Query configServer for namingService success, url = %s, clusterIds = %s", url, clusterIds));
		return clusterIds;
	}
	
	private NamingServiceCluster queryNamingCluster(String url){
		HttpResponseMessage message = CaptainHttpUtil.doHttpGet(httpClient, url);
		if(message == null) {
			ApiLogger.warn(String.format("Query configServer for namingCluster failed, result is null, url=%s", url));
			return null;
		} else if (message.getStatusCode() == HttpStatusCode.NOT_MODIFIED) {
			ApiLogger.warn(String.format("Query configServer for namingCluster success, result is not modified, url = %s, rsMsg = %s", url, message));
			return null;
		} else {
			ResponsePacket packet = CaptainHttpUtil.parseResponse(message.getContent());
			if (CaptainHttpUtil.validateResponse(packet, VintageConstants.CONFIG_SERVICE_LOOKUP)) {
				String jsonData = packet.getBody();
				NamingServiceCluster cluster = NamingServiceCluster.parser(jsonData);
				if (cluster != null) {
					ApiLogger.info(String.format("Query configServer for namingCluster success, url = %s, cluster = %s",
							url, cluster));
					return cluster;
				} else {
					ApiLogger.warn(String.format("Query configServer for namingCluster failed, result is null, url = %s, cluster = null", url));
				}
			}
			return null;
		}
	}
	
}
