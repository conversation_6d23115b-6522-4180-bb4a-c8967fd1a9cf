package com.weibo.api.captain.common.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import com.weibo.api.captain.common.CaptainConstants;
import com.weibo.api.captain.common.model.CacheAgentConfig;
import com.weibo.api.captain.common.util.ApacheHttpClient;
import com.weibo.api.captain.common.util.CaptainHttpUtil;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.JsonNode;

import cn.sina.api.commons.util.ApiLogger;
import com.weibo.vintage.json.JsonUtil;
import com.weibo.vintage.model.HttpResponseMessage;
import com.weibo.vintage.model.HttpStatusCode;
import com.weibo.vintage.model.ResponsePacket;
import com.weibo.vintage.model.StaticsConfigMap;
import com.weibo.vintage.utils.JsonHelper;
import com.weibo.vintage.utils.UrlHelper;
import com.weibo.vintage.utils.VintageConstants;

/**
 * Package: com.weibo.api.captain.core.config.service.impl
 * 
 * File: ConfigServiceImpl.java
 * 
 * <pre>
 * 访问configServer，从而进行静态配置更新、获取等底层业务操作。
 * mc配置是静态配置服务，层次为：groupId-->key-->configVal。目前用的key都是“all”。
 * 
 * </pre>
 * 
 * Author: fishermen
 * 
 * Copyright @ 2015 Corpration Name
 * 
 */
public class StaticConfigBizImpl implements StaticConfigBiz {

	private static String STATIC_CONFIG_URI = "/1/config/service";
	
	/** 查看ConfigServer中所有已经注册的服务列表*/
    private static final String ACTION_ADMIN_GET_SERVICE = "getservice";
    
    private static final String PREFIX = "cache";
    
    private static final String BEGIN_STRING = "cache.";
    
    private static final String END_STRING = ".pool";

	private String baseUrl;

	/** 访问vintage资源，所以这里保持和vintage使用相同的httpClient */
	@Resource(name = "httpClient")
	private ApacheHttpClient httpClient;

	@Override
	public boolean register(String groupId, String key, String configStr) {
		if(StringUtils.isBlank(groupId)
				|| StringUtils.isBlank(key)
				|| StringUtils.isBlank(configStr)) {
			ApiLogger.warn(String.format("register config false, [groupId=%s], [key=%s], [configStr=%s]",
					groupId, key, configStr));
			return false;
		}
		
		if (!CacheAgentConfig.validateStr(configStr)) {
			ApiLogger.warn(String.format(
					"update config false, groupId:%s, key:%s, configStr:%s",
					groupId, key, configStr));
			return false;
		}
		
		// build name-value pairs
		Map<String, String> nameValuePairs = new HashMap<String, String>();
		nameValuePairs.put("action", VintageConstants.CONFIG_SERVICE_REGISTER);
		nameValuePairs.put("group", CaptainHttpUtil.encoding(groupId));
		nameValuePairs.put("key", CaptainHttpUtil.encoding(key));
		nameValuePairs.put("value", CaptainHttpUtil.encoding(configStr));

		List<String> url = getWriteStaticConfigUrl();
		String responseData = CaptainHttpUtil.doHttpPost(getBaseUrl(),httpClient, url.get(0), nameValuePairs);
		ResponsePacket packet = CaptainHttpUtil.parseResponse(responseData, true);
		boolean registerSuccess = false;
		if (CaptainHttpUtil.validateResponse(packet,
				VintageConstants.CONFIG_SERVICE_REGISTER)) {
			registerSuccess = packet.getJsonNodeBody() != null
					&& packet.getJsonNodeBody().get("success") != null
					&& JsonUtil.getJsonBooleanValue(packet.getJsonNodeBody()
							.get("success"), false);

			ApiLogger.info(String.format("static config 1 register, url = %s, params = %s, rs = %s, rsMsg = %s",
					url.get(0), nameValuePairs, registerSuccess, packet));
		}else {
			ApiLogger.info(String.format("static config register false for rsp is invalid, url:%s, params:%s, rs:%s. rsMsg = %s",
					url, nameValuePairs, registerSuccess, packet));
		}
		if(registerSuccess && url.size()>1){
			boolean registerSuccessNew = false;
			String responseDataNew = CaptainHttpUtil.doHttpPost(getBaseUrl(),httpClient, url.get(1), nameValuePairs);
			ResponsePacket packetNew = CaptainHttpUtil.parseResponse(responseDataNew, true);
			if (CaptainHttpUtil.validateResponse(packetNew,
					VintageConstants.CONFIG_SERVICE_REGISTER)){
				registerSuccessNew = packetNew.getJsonNodeBody() != null
						&& packetNew.getJsonNodeBody().get("success") != null
						&& JsonUtil.getJsonBooleanValue(packetNew.getJsonNodeBody()
						.get("success"), false);

				ApiLogger.info(String.format("static config register 2, url = %s, params = %s, rs = %s, rsMsg = %s",
						url.get(1), nameValuePairs, registerSuccessNew, packetNew));

			}else{
				ApiLogger.info(String.format("static config register false for rsp is invalid, url:%s, params:%s, rs:%s. rsMsg = %s",
						url.get(1), nameValuePairs, registerSuccessNew, packetNew));
			}
		}

		//如果register成功

		return registerSuccess;

	}




    @Override
	public boolean registerCluster(String group) {
		ApiLogger.info("register cluster :" + group);
		if (StringUtils.isBlank(group)) {
			ApiLogger.warn(String.format("register cluster false, [group=%s]",group));
			return false;
		}
		Map<String, String> nameValuePairs = new HashMap<String, String>();
		nameValuePairs.put("action", CaptainConstants.CONFIG_SERVICE_ADDSERVICE);
		nameValuePairs.put("service", CaptainHttpUtil.encoding(group));
		nameValuePairs.put("type", CaptainHttpUtil.encoding("dynamic"));

		List<String> url = getWriteNameAdminUrl();

		String responseData = CaptainHttpUtil.doHttpPost(getBaseUrl(),httpClient, url.get(0), nameValuePairs);

        ResponsePacket packet = CaptainHttpUtil.parseResponse(responseData, true);
        boolean result = false;
        if (CaptainHttpUtil.validateResponse(packet,
                CaptainConstants.CONFIG_SERVICE_ADDSERVICE)) {

            result = packet.getJsonNodeBody() != null
                    && packet.getJsonNodeBody().getTextValue().equals("true");
        }
//        ApiLogger.info(String.format("register cluster false, [group=%s]",group));
		ApiLogger.info(String.format("static config register %s, url=%s, params=%s, packet=%s",  result,
				url.get(0), nameValuePairs, packet));
        if(result && url.size()>1){
			String responseDataNew = CaptainHttpUtil.doHttpPost(getBaseUrl(),httpClient, url.get(1), nameValuePairs);
			ResponsePacket packetNew = CaptainHttpUtil.parseResponse(responseDataNew, true);
			boolean resultNew = false;
			if (CaptainHttpUtil.validateResponse(packetNew,
					CaptainConstants.CONFIG_SERVICE_ADDSERVICE)) {

				resultNew = packetNew.getJsonNodeBody() != null
						&& packetNew.getJsonNodeBody().getTextValue().equals("true");
			}
			ApiLogger.info(String.format("static config register %s, url=%s, params=%s, packet=%s",  resultNew,
					url.get(1), nameValuePairs, packetNew));
		}
        return result;
	}

	private List<String> getWriteNameAdminUrl() {//用于write
		List<String> res = new ArrayList();
		res.add(baseUrl + VintageConstants.NAMING_ADMIN_URL);
		return res;
	}

	private String getNameAdminUrl() {//用于read的
		return baseUrl + VintageConstants.NAMING_ADMIN_URL;
	}

	public boolean unregister(String groupId, String key) {
		if(StringUtils.isBlank(groupId) || StringUtils.isBlank(key)) {
			ApiLogger.warn(String.format("static config unregister false for malformed params, groupId = %s, key = %s ", 
					groupId, key));
			return false;
		}
		
		// build name-value pairs
		Map<String, String> nameValuePairs = new HashMap<String, String>();
		nameValuePairs.put("action", VintageConstants.CONFIG_SERVICE_UNREGISTER);
		nameValuePairs.put("group", CaptainHttpUtil.encoding(groupId));
		nameValuePairs.put("key", CaptainHttpUtil.encoding(key));

		List<String> url = getWriteStaticConfigUrl();

		String responseData = CaptainHttpUtil.doHttpPost(httpClient, url.get(0), nameValuePairs);
		ResponsePacket packet = CaptainHttpUtil.parseResponse(responseData, true);
		boolean result = false;
		if (CaptainHttpUtil.validateResponse(packet,
				VintageConstants.CONFIG_SERVICE_UNREGISTER)) {
			result = packet.getJsonNodeBody() != null
					&& packet.getJsonNodeBody().get("success") != null
					&& JsonUtil.getJsonBooleanValue(packet.getJsonNodeBody()
							.get("success"), false);
		}
		ApiLogger.info(String.format("static config register %s, url=%s, params=%s, packet=%s",  result,
				url.get(0), nameValuePairs, packet));

		if(result && url.size()>1){
			String responseDataNew = CaptainHttpUtil.doHttpPost(httpClient, url.get(1), nameValuePairs);
			ResponsePacket packetNew = CaptainHttpUtil.parseResponse(responseDataNew, true);
			boolean resultNew = false;
			if (CaptainHttpUtil.validateResponse(packetNew,
					VintageConstants.CONFIG_SERVICE_UNREGISTER)) {
				resultNew = packetNew.getJsonNodeBody() != null
						&& packetNew.getJsonNodeBody().get("success") != null
						&& JsonUtil.getJsonBooleanValue(packetNew.getJsonNodeBody()
						.get("success"), false);
			}
			ApiLogger.info(String.format("static config register %s, url=%s, params=%s, packet=%s",  resultNew,
					url.get(1), nameValuePairs, packetNew));
		}
		return result;
	}

	public String getSign(String groupId, String key) {
		if(StringUtils.isBlank(groupId) || StringUtils.isBlank(key)) {
			ApiLogger.warn(String.format("static-config lookup false, for malformed params, groupId=%s, key=%s", groupId, key));
			return null;
		}

		StringBuilder urlBuilder = new StringBuilder();
		urlBuilder.append(getStaticConfigUrl()).append("?action=").append(VintageConstants.CONFIG_SERVICE_LOOKUP)
				.append("&group=").append(CaptainHttpUtil.encoding(groupId))
				.append("&key=").append(CaptainHttpUtil.encoding(key));


		String responseData = CaptainHttpUtil.doHttpGetContent(httpClient, urlBuilder.toString(), true);
		ResponsePacket responsePacket = CaptainHttpUtil.parseResponse(responseData, true);

		if (CaptainHttpUtil.validateResponse(responsePacket, VintageConstants.CONFIG_SERVICE_LOOKUP)) {
			String jsonData = responsePacket.getJsonNodeBody().toString();
			StaticsConfigMap staticConfig = StaticsConfigMap.parser(jsonData);
			if (staticConfig != null) {
				return staticConfig.getSign();
			}
		}
		return null;
	}

	/**
	 * 获取groupId、key对应的configVal
	 * @param groupId
	 * @return
	 */
	public CacheAgentConfig lookup(String groupId, String key) {
		if(StringUtils.isBlank(groupId) || StringUtils.isBlank(key)) {
			ApiLogger.warn(String.format("static-config lookup false, for malformed params, groupId=%s, key=%s", groupId, key));
			return null;
		}
		
		StringBuilder urlBuilder = new StringBuilder();
		urlBuilder.append(getStaticConfigUrl()).append("?action=").append(VintageConstants.CONFIG_SERVICE_LOOKUP)
				.append("&group=").append(CaptainHttpUtil.encoding(groupId))
				.append("&key=").append(CaptainHttpUtil.encoding(key));
		
		
		String responseData = CaptainHttpUtil.doHttpGetContent(httpClient, urlBuilder.toString(), true);
		
		Map<String, CacheAgentConfig> configMap = parseRspForCacheAgentConfig(responseData);
		CacheAgentConfig config = configMap != null ? configMap.get(key) : null;
			
		ApiLogger.info(String.format("static-config lookup by group-key, url=[%s], config=[%s]", urlBuilder, config));
		
		return config;
	}
	
	@Override
	public Map<String, CacheAgentConfig> lookup(String groupId) {
		if(StringUtils.isBlank(groupId)) {
			ApiLogger.warn(String.format("static-config lookup false, for groupId = %s", groupId));
			return null;
		}
		
		String url = getStaticConfigUrl();
		StringBuilder urlBuilder = new StringBuilder();
		urlBuilder.append(url).append("?action=").append(VintageConstants.CONFIG_SERVICE_LOOKUP)
				.append("&group=").append(CaptainHttpUtil.encoding(groupId));
		
		// call the statics configuration server's URL and accept the GZIP stream
		String responseData = CaptainHttpUtil.doHttpGetContent(httpClient, urlBuilder.toString(), true);
		Map<String, CacheAgentConfig> result = parseRspForCacheAgentConfig(responseData);
		
		ApiLogger.info(String.format("static-config lookup by group, url=[%s], lookupRs=[%s]", urlBuilder, result));
		return result;
	}
	
	private Map<String, CacheAgentConfig> parseRspForCacheAgentConfig(String responseData) {
		// parser the data that receiving from remote server
		ResponsePacket responsePacket = CaptainHttpUtil.parseResponse(responseData, true);

		if (CaptainHttpUtil.validateResponse(responsePacket, VintageConstants.CONFIG_SERVICE_LOOKUP)) {
			String jsonData = responsePacket.getJsonNodeBody().toString();
			StaticsConfigMap staticConfig = StaticsConfigMap.parser(jsonData);
			if (staticConfig != null) {
				return CacheAgentConfig.parseVintageStaticConfigMap(staticConfig);
			}
		}
		return null;
	}

	@Override
	public String getBaseUrl() {
		return baseUrl;
	}

	@Override
	public void setBaseUrl(String baseUrl) {
		this.baseUrl = baseUrl;
	}

	public ApacheHttpClient getHttpClient() {
		return httpClient;
	}

	public void setHttpClient(ApacheHttpClient httpClient) {
		this.httpClient = httpClient;
	}

	private List<String> getWriteStaticConfigUrl() {//用于write
		List<String> res = new ArrayList();
		res.add(baseUrl + STATIC_CONFIG_URI);
		ApiLogger.info("read master getWriteStaticConfigUrl: " + res);
		return res;
	}

	private String getStaticConfigUrl() {//用于read
		ApiLogger.info("read master getStaticConfigUrl: " + this.baseUrl + STATIC_CONFIG_URI);
		return this.baseUrl + STATIC_CONFIG_URI;
	}



    @Override
    public List<String> getService() {
        String url = getNameAdminUrl() +
                UrlHelper.buildUrlParams("?action", ACTION_ADMIN_GET_SERVICE); 
        Set<String> result = new HashSet<String>();
        List<String> serviceNames = queryNamingService(url);
        if(serviceNames == null || serviceNames.size() == 0){
            return Collections.EMPTY_LIST;
        }
        for (String serviceName:serviceNames) {
            if (serviceName.startsWith(PREFIX)) {
                Pattern p=Pattern.compile(BEGIN_STRING+"(service\\.|service2\\.0\\.)"+"(\\w+.*)"+END_STRING);
                Matcher m=p.matcher(serviceName);
                while(m.find()){
                    result.add(m.group(2));
                }
            }
        }
        return new ArrayList<String>(result);
    }
	
    private List<String> queryNamingService(String url) {

        List<String> serviceIds = new ArrayList<String>();
        HttpResponseMessage message = CaptainHttpUtil.doHttpGet(httpClient, url);

        if (message == null) {
            ApiLogger.warn(String.format("Query configServer for namingService false, url = %s, rsMsg = null", url));
            return Collections.EMPTY_LIST;
        }

        if (message.getStatusCode() != HttpStatusCode.NOT_MODIFIED) {
            ResponsePacket packet = CaptainHttpUtil.parseResponse(message.getContent());
            if (CaptainHttpUtil.validateResponse(packet, VintageConstants.CONFIG_SERVICE_LOOKUP)) {
                String body = packet.getBody();
                JsonNode jsNode = JsonHelper.parserStringToJsonNode(body);
                if (jsNode == null) {
                    return Collections.EMPTY_LIST;
                }     
                if (jsNode != null) {
                    JsonNode jsonNode = jsNode.get("services");
                    if (jsonNode == null) {
                        return Collections.EMPTY_LIST;
                    }
                    Iterator<JsonNode> values = jsonNode.getElements();
                    while (values.hasNext()) {
                        JsonNode nodeName = values.next().get("name");
                        if (nodeName == null) {
                            return Collections.EMPTY_LIST;
                        }
                        if (nodeName.getTextValue().trim().length() > 0) {
                            serviceIds.add(nodeName.getTextValue().trim());
                        }
                        
                    }
                }
            }
        }

        ApiLogger.info(String.format("Query configServer for namingService success, url = %s, clusterIds = %s", url, serviceIds));
        return serviceIds;
    }

}
