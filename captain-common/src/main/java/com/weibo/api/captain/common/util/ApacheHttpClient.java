package com.weibo.api.captain.common.util;

import cn.sina.api.commons.util.ApiLogger;
import com.weibo.vintage.model.DefaultHttpResponseMessage;
import com.weibo.vintage.model.HttpResponseMessage;
import com.weibo.vintage.model.HttpStatusCode;
import com.weibo.vintage.thread.StandardThreadExecutor;
import com.weibo.vintage.utils.HttpManager;
import com.weibo.vintage.utils.VintageConstants;
import com.weibo.vintage.utils.VintageLogger;
import com.weibo.vintage.utils.VintageUtils;
import org.apache.commons.httpclient.*;
import org.apache.commons.httpclient.cookie.CookiePolicy;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpClientParams;
import org.apache.commons.httpclient.params.HttpConnectionManagerParams;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPInputStream;

import static com.weibo.vintage.utils.VintageLogger.debug;
import static com.weibo.vintage.utils.VintageLogger.warn;
import static java.lang.String.format;
import static org.apache.commons.lang.StringUtils.isEmpty;

/**
 * ApapcheHttpClient包含api-commons的ApacheHttpClient功能,同时添加获取HTTP
 * code的方法;支持用户指定是否接受GZIP数据流
 * 
 * 
 */
public class ApacheHttpClient {
	private final static String DEFAULT_CHARSET = "utf-8";
	private AccessLog accessLog = new AccessLog.DefaultHttpClientAceessLog();
	private ExecutorService httpPool;
	private MultiThreadedHttpConnectionManager connectionManager;
	private HttpClient client;

	private int maxConnectionPerHost = 50;
	private int connectionTimeout = 3000;
	private int socketTimeout = 500;
	private int minThread = 1;
	private int maxThread = 300;
	private int maxSize;

	public ApacheHttpClient() {
		this(150, 500, 500, 1024 * 1024);
	}

	public ApacheHttpClient(int maxConPerHost, int conTimeOutMs,
                            int soTimeOutMs, int maxSize) {
		this(maxConPerHost, conTimeOutMs, soTimeOutMs, maxSize, 1, 300);
	}

	public ApacheHttpClient(int maxConnectionPerHost, int connectionTimeout,
                            int socketTimeout, int packetMaxSize, int minThread, int maxThread) {
		this.maxConnectionPerHost = maxConnectionPerHost;
		this.connectionTimeout = connectionTimeout;
		this.socketTimeout = socketTimeout;
		this.maxSize = packetMaxSize;
		this.minThread = minThread;
		this.maxThread = maxThread;
		connectionManager = new MultiThreadedHttpConnectionManager();
		HttpConnectionManagerParams params = connectionManager.getParams();
		params.setMaxTotalConnections(600); // 这个值要小于TOMCAT线程池是800
		params.setDefaultMaxConnectionsPerHost(this.maxConnectionPerHost);
		params.setConnectionTimeout(this.connectionTimeout);
		params.setSoTimeout(this.socketTimeout);

		HttpClientParams clientParams = new HttpClientParams();
		// 忽略cookie 避免 Cookie rejected 警告
		clientParams.setCookiePolicy(CookiePolicy.IGNORE_COOKIES);
		client = new HttpClient(clientParams, connectionManager);

		httpPool = new StandardThreadExecutor(this.minThread, this.maxThread);
//		Runtime.getRuntime().addShutdownHook(new Thread(new Runnable() {
//			@Override
//			public void run() {
//				httpPool.shutdown();
//				connectionManager.shutdown();
//			}
//		}));
	}

	public String getAsync(final String url) {
		return getAsync(url, this.socketTimeout);
	}

	/**
	 * 异步获取指定的HTTP接口内容
	 * 
	 * @param url
	 *            HTTP接口
	 * @param isZipContent
	 *            是否接受GZIP数据格式
	 * @return 远程HTTP接口返回的内容
	 */
	public String getAsync(final String url, final boolean isZipContent) {
		return getAsync(url, this.socketTimeout, isZipContent);
	}

	public String getAsync(final String url, final long timeout) {
		return getAsync(url, timeout, false);
	}

	/**
	 * 异步获取指定的HTTP接口内容
	 * 
	 * @param url
	 *            HTTP接口
	 * @param timeout
	 *            访问远程HTTP接口超时时间
	 * @param isZipContent
	 *            是否接受GZIP数据格式
	 * @return
	 */
	public String getAsync(final String url, final long timeout,
			final boolean isZipContent) {
		return extractContent(getAsyncMessage(url, timeout, isZipContent));
	}
	
	public HttpResponseMessage getAsyncMessage(final String url) {
		return getAsyncMessage(url, this.socketTimeout, false);
	}
	
	public HttpResponseMessage getAsyncMessage(final String url, final long timeout,
			final boolean isZipContent) {
		Future<HttpResponseMessage> future = httpPool.submit(new Callable<HttpResponseMessage>() {
			public HttpResponseMessage call() throws Exception {
				//添加GZIP请求头
				if (isZipContent) {
					Map<String, String> gzipHeaders = new HashMap<String, String>();
					gzipHeaders.put(VintageConstants.ACCEPT_ENCODING,
							VintageConstants.GZIP_ENCODING);
					return getMessage(url, gzipHeaders, DEFAULT_CHARSET);
				} else {
					return getMessage(url, null, DEFAULT_CHARSET);
				}

			}
		});
		try {
			return future.get(timeout, TimeUnit.MILLISECONDS);
		} catch (Exception e) {
			warn("getAsync error url:" + url + " msg:" + e.getMessage());
			return null;
		}
	}

	public Future<String> getAsyncFuture(final String url) {
		Future<String> future = httpPool.submit(new Callable<String>() {
			public String call() throws Exception {

				return get(url);
			}
		});
		return future;
	}

	public String get(String url) {
		return get(url, DEFAULT_CHARSET);
	}

	public String get(String url, Map<String, String> headers) {
		return get(url, headers, DEFAULT_CHARSET);
	}

	public String get(String url, String charset) {
		return get(url, null, charset);
	}

	public HttpResponseMessage getMessage(String url, Map<String, String> headers, String charset) {
		if (HttpManager.isBlockResource(url)) {
			debug("getURL blockResource url=" + url);
			return null;
		}
		HttpMethod get = new GetMethod(url);
		HttpMethodParams params = new HttpMethodParams();
		params.setContentCharset(charset);
		params.setUriCharset(charset);
		get.setParams(params);
		addHeader(get, headers);
		return executeMethod(url, get, null, charset);
	}
	public String get(String url, Map<String, String> headers, String charset) {
		return extractContent(getMessage(url, headers, charset));
	}

	public String postAsync(final String url, final Map<String, ?> nameValues) {
		Future<String> future = httpPool.submit(new Callable<String>() {
			public String call() throws Exception {
				return post(url, nameValues);
			}
		});
		try {
			return future.get(this.socketTimeout, TimeUnit.MILLISECONDS);
		} catch (Exception e) {
			warn(format("getAsync error url:%s post:%s msg:%s", url,
					mapToString(nameValues), e.getMessage()));
			return "";
		}
	}

	public String post(String url, Map<String, ?> nameValues) {
		return post(url, nameValues, DEFAULT_CHARSET);
	}

	public String post(String configServer,String url, Map<String, ?> nameValues) {
		return post(configServer,url, nameValues, DEFAULT_CHARSET);
	}

	public String post(String configServer,String url, Map<String, ?> nameValues, String charset) {
		return post(configServer,url, nameValues, null, charset);
	}
	public String post(String url, Map<String, ?> nameValues, String charset) {
		return post(url, nameValues, null, charset);
	}

	public String post(String configServer,String url, Map<String, ?> nameValues,
					   Map<String, String> headers, String charset) {
		if (HttpManager.isBlockResource(url)) {
			debug("requestURL blockResource url=" + url);
			return "";
		}
		PostMethod post = new PostMethod(url);
		HttpMethodParams params = new HttpMethodParams();
		params.setContentCharset(charset);
		post.setParams(params);
		addHeader(post, headers);
		if (nameValues != null && !nameValues.isEmpty()) {
			List<NameValuePair> list = new ArrayList<NameValuePair>(
					nameValues.size());
			for (Map.Entry<String, ?> entry : nameValues.entrySet()) {
				if (entry.getKey() != null && !entry.getKey().isEmpty()) {
					list.add(new NameValuePair(entry.getKey(), entry.getValue()
							.toString()));
				} else {
					try {
						post.setRequestEntity(new StringRequestEntity(entry
								.getValue().toString(), "text/xml", "utf-8"));
					} catch (UnsupportedEncodingException e) {
					}
				}
			}
			if (!list.isEmpty()) {
				post.setRequestBody(list.toArray(new NameValuePair[list.size()]));
			}
		}
		return extractContent(executeMethod(configServer,url, post, mapToString(nameValues), charset));
	}

	public String post(String url, Map<String, ?> nameValues,
			Map<String, String> headers, String charset) {
		if (HttpManager.isBlockResource(url)) {
			debug("requestURL blockResource url=" + url);
			return "";
		}
		PostMethod post = new PostMethod(url);
		HttpMethodParams params = new HttpMethodParams();
		params.setContentCharset(charset);
		post.setParams(params);
		addHeader(post, headers);
		if (nameValues != null && !nameValues.isEmpty()) {
			List<NameValuePair> list = new ArrayList<NameValuePair>(
					nameValues.size());
			for (Map.Entry<String, ?> entry : nameValues.entrySet()) {
				if (entry.getKey() != null && !entry.getKey().isEmpty()) {
					list.add(new NameValuePair(entry.getKey(), entry.getValue()
							.toString()));
				} else {
					try {
						post.setRequestEntity(new StringRequestEntity(entry
								.getValue().toString(), "text/xml", "utf-8"));
					} catch (UnsupportedEncodingException e) {
					}
				}
			}
			if (!list.isEmpty()) {
				post.setRequestBody(list.toArray(new NameValuePair[list.size()]));
			}
		}
		return extractContent(executeMethod(url, post, mapToString(nameValues), charset));
	}

	private static void addHeader(HttpMethod method, Map<String, String> headers) {
		if (headers != null && !headers.isEmpty()) {
			for (Map.Entry<String, String> entry : headers.entrySet()) {
				method.setRequestHeader(entry.getKey(), entry.getValue());
			}
		}
	}

	private String mapToString(Map<String, ?> nameValues) {
		StringBuffer sb = new StringBuffer();
		if (nameValues == null) {
			return sb.toString();
		}
		for (Map.Entry<String, ?> entry : nameValues.entrySet()) {
			if (entry.getValue() instanceof String) {
				sb.append(entry.getKey() + "=" + entry.getValue() + "&");
			} else if (entry.getValue() instanceof String[]) {
				String[] values = (String[]) entry.getValue();
				for (String value : values) {
					sb.append(entry.getKey() + "=" + value + "&");
				}
			}
		}
		VintageUtils.trim(sb, '&');
		return sb.toString();
	}

	private HttpResponseMessage executeMethod(String url, HttpMethod httpMethod,
			String postString, String charset) {
		String result = null;
		long start = System.currentTimeMillis();
		int len = 0;
		try {
			HttpResponseMessage responseMessage = doExecuteMethod(httpMethod,
					charset);
			len = responseMessage == null ? 0 : StringUtils
					.length(responseMessage.getContent());
			return responseMessage;
		} catch (ApiHttpClientExcpetion e) {
			warn(format("executeMethod failed url:%s charset:%s",
					url, charset), e);
			return null;
		} finally {
			accessLog(
					System.currentTimeMillis() - start,
					httpMethod.getName(),
					httpMethod.getStatusLine() != null ? httpMethod
							.getStatusCode() : -1, len, url,
					httpMethod.getQueryString(), postString, result);
		}
	}

	private HttpResponseMessage executeMethod(String configServer,String url, HttpMethod httpMethod,
											  String postString, String charset) {
		String result = null;
		long start = System.currentTimeMillis();
		int len = 0;
		try {
			HttpResponseMessage responseMessage = doExecuteMethod(httpMethod,
					charset);
			len = responseMessage == null ? 0 : StringUtils
					.length(responseMessage.getContent());
			if (len>0){
				if (responseMessage.getHeaders()!=null){
					String masterServer=responseMessage.getHeader(ThreadLocalUtil.MasterSeerverHeader);
					if (!StringUtils.isEmpty(masterServer)){
						ApiLogger.info("get master server from config server:"+configServer+" master server:"+masterServer);
						ThreadLocalUtil.setServer(configServer,masterServer);
					}
				}
			}
			return responseMessage;
		} catch (ApiHttpClientExcpetion e) {
			warn(format("executeMethod failed url:%s charset:%s",
					url, charset), e);
			return null;
		} finally {
			accessLog(
					System.currentTimeMillis() - start,
					httpMethod.getName(),
					httpMethod.getStatusLine() != null ? httpMethod
							.getStatusCode() : -1, len, url,
					httpMethod.getQueryString(), postString, result);
		}
	}
	
	/**
	 * 处理GZIP数据流
	 * @param httpMethod
	 * @param out
	 * @param beginTime
	 * @return
	 * @throws ApiHttpClientExcpetion
	 */
	private int processGZipInputStream(HttpMethod httpMethod, OutputStream out,
			long beginTime) throws ApiHttpClientExcpetion {
		// 处理压缩过的配置信息的逻辑
		InputStream inputStream = null;
		GZIPInputStream gzipInputStream = null;
		int readLen = 0;
		try {
			inputStream = httpMethod.getResponseBodyAsStream();
			if (inputStream == null) {
				return readLen;
			}
			byte[] buffer = new byte[1024];
			gzipInputStream = new GZIPInputStream(inputStream);

			int len;
			while ((len = gzipInputStream.read(buffer)) != -1) {
				if (System.currentTimeMillis() - beginTime > this.socketTimeout) {
					throw new ReadTimeOutException(format(
							"read so timeout time:%s soTimeOut:%s",
							(System.currentTimeMillis() - beginTime),
							socketTimeout));
				}
				out.write(buffer, 0, len);
				readLen += len;
				if (readLen > maxSize) {
					throw new SizeException(
							format("size too big size:%s maxSize:%s", readLen,
									maxSize));
				}
			}

		} catch (ApiHttpClientExcpetion ex) {
			warn(format("processGZipInputStream url:%s message:%s",
					getHttpMethodURL(httpMethod), ex.getMessage()));
			throw ex;
		} catch (Exception ex) {
			warn(format("processGZipInputStream error! msg:%s", ex.getMessage()));
		}
		return readLen;
	}

	private int processNormalStream(HttpMethod httpMethod, OutputStream out,
			long beginTime) throws ApiHttpClientExcpetion {
		// 处理压缩过的配置信息的逻辑
		InputStream inputStream = null;
		int readLen = 0;
		try {
			inputStream = httpMethod.getResponseBodyAsStream();
			if (inputStream == null) {
				return readLen;
			}
			byte[] buffer = new byte[1024];
			int len;
			while ((len = inputStream.read(buffer)) != -1) {
				if (System.currentTimeMillis() - beginTime > this.socketTimeout) {
					throw new ReadTimeOutException(format(
							"read so timeout time:%s soTimeOut:%s",
							(System.currentTimeMillis() - beginTime),
							socketTimeout));
				}
				out.write(buffer, 0, len);
				readLen += len;
				if (readLen > maxSize) {
					throw new SizeException(
							format("size too big size:%s maxSize:%s", readLen,
									maxSize));
				}
			}
		} catch (ApiHttpClientExcpetion ex) {
			warn(format("processNormalStream url:%s message:%s",
					getHttpMethodURL(httpMethod), ex.getMessage()));
			throw ex;
		} catch (Exception ex) {
			warn(format("processNormalStream error! msg:%s", ex.getMessage()));
		}
		return readLen;
	}

	private HttpResponseMessage doExecuteMethod(HttpMethod httpMethod, String charset)
			throws ApiHttpClientExcpetion {
		long start = System.currentTimeMillis();
		try {
			addRemoteInvokerHeader(httpMethod);
			int status = client.executeMethod(httpMethod);
			if (System.currentTimeMillis() - start > this.socketTimeout) {
				throw new ReadTimeOutException(format(
						"executeMethod so timeout time:%s soTimeOut:%s",
						(System.currentTimeMillis() - start), socketTimeout));
			}
			HttpResponseMessage message = new DefaultHttpResponseMessage();
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			if (isZipContent(httpMethod)) {
				processGZipInputStream(httpMethod, out, start);
			} else {
				processNormalStream(httpMethod, out, start);
			}
			message.setContent(new String(out.toByteArray(), charset));
			message.setStatusCode(HttpStatusCode.parser(String.valueOf(status)));
			setHeaders(message, httpMethod.getResponseHeaders());
			return message;
		} catch (ApiHttpClientExcpetion ex) {
			warn(format("ApiHttpClientExcpetion url:%s message:%s",
					getHttpMethodURL(httpMethod), ex.getMessage()));
			throw ex;
		} catch (UnsupportedEncodingException e) {
			warn(format("ApacheHttpClient.executeMethod UnsupportedEncodingException charset:%s", charset), e);
			return null;
		} catch (Exception ex) {
			warn(format("ApacheHttpClient.doExecuteMethod error! msg:%s",
					ex.getMessage()));
		} finally {
			httpMethod.releaseConnection();
		}
		return null;
	}
	
	private void setHeaders(HttpResponseMessage message, Header[] headers) {
		if(ArrayUtils.isNotEmpty(headers)) {
			for(Header header: headers) {
				if(header != null) {
					message.addHeader(header.getName(), header.getValue());
				}
			}
		}
	}

	/**
	 * 查看是否为压缩的内容
	 * 
	 * @param httpMethod
	 * @return 
	 */
	boolean isZipContent(HttpMethod httpMethod) {
		if (null != httpMethod
				.getResponseHeader(VintageConstants.CONTENT_ENCODING)) {
			String acceptEncoding = httpMethod.getResponseHeader(
					VintageConstants.CONTENT_ENCODING).getValue();
			if (acceptEncoding.toLowerCase().indexOf("gzip") > -1) {
				return true;
			}
		}
		return false;
	}

	private String getHttpMethodURL(HttpMethod httpMethod) {
		try {
			return httpMethod.getURI().toString();
		} catch (URIException e) {
			return "";
		}
	}

	public int getStatusCodeAsync(final String url) throws Exception {
		return getStatusCodeAsync(url, this.socketTimeout);
	}

	public int getStatusCodeAsync(final String url, final long timeout)
			throws Exception {
		Future<Integer> future = httpPool.submit(new Callable<Integer>() {
			public Integer call() throws Exception {
				return getStatusCode(url, DEFAULT_CHARSET);
			}
		});
		try {
			return future.get(timeout, TimeUnit.MILLISECONDS);
		} catch (Exception e) {
			// debug log
			warn(new StringBuilder(128).append("getStatusCodeAsync url ")
					.append(url).append(" timeout ").append(timeout).toString(),
					e);
			throw e;
		}
	}

	public int getStatusCode(String url, String charset) throws Exception {
		if (HttpManager.isBlockResource(url)) {
			debug("getStatusCode blockResource url=" + url);
			return 0;
		}

		HttpMethod get = new GetMethod(url);
		HttpMethodParams params = new HttpMethodParams();
		params.setContentCharset(charset);
		params.setUriCharset(charset);
		get.setParams(params);
		return executeMethodForStatusCode(url, get, charset);
	}

	private int executeMethodForStatusCode(String url, HttpMethod httpMethod,
			String charset) throws Exception {
		int statusCode = 0;

		long start = System.currentTimeMillis();
		try {
			addRemoteInvokerHeader(httpMethod);
			statusCode = client.executeMethod(httpMethod);
			// TODO: the block may be never reached
			if (System.currentTimeMillis() - start > this.socketTimeout) {
				warn(format("executeMethod so timeout time:%s soTimeOut:%s",
						(System.currentTimeMillis() - start), socketTimeout));
			}
			return statusCode;
		} catch (Exception ex) {
			// debug log
			warn(new StringBuilder(128)
					.append("getStatusCodeAsync[executeMethodForStatusCode] url ")
					.append(url).toString(), ex);
			// throw the exception
			throw ex;
		} finally {
			httpMethod.releaseConnection();
		}

	}
	
	private String extractContent(HttpResponseMessage message) {
		return (message == null ? "" : message.getContent());
	}

	private void addRemoteInvokerHeader(HttpMethod httpMethod) {
		httpMethod.setRequestHeader("X-Remote-API-Invoker", "openapi");
	}

	private void accessLog(long time, String method, int status, int len,
			String uri, String queryString, String post, String ret) {
		String url = null;
		if (StringUtils.isEmpty(queryString)) {
			url = uri;
		} else if (uri.contains("?")) {
			url = uri.substring(0, uri.indexOf("?") + 1) + queryString;
		} else {
			url = uri + "?" + queryString;
		}
		if (time > this.socketTimeout) {
			VintageLogger.error("HTTP " + uri + " Error:" + time);
		}
		if (accessLog != null) {
			try {
				accessLog.accessLog(time, method, status, len, url, post, ret);
			} catch (Exception e) {
				warn("error accessLog", e);
			}
		}
	}

	public class ApiHttpClientExcpetion extends Exception {
		/**
		 * 
		 */
		private static final long serialVersionUID = 1109470750575403206L;

		public ApiHttpClientExcpetion(String msg) {
			super(msg);
		}
	}

	public class ReadTimeOutException extends ApiHttpClientExcpetion {
		/**
		 * 
		 */
		private static final long serialVersionUID = 2570957983622997586L;

		public ReadTimeOutException(String msg) {
			super(msg);
		}
	}

	public class SizeException extends ApiHttpClientExcpetion {
		/**
		 * 
		 */
		private static final long serialVersionUID = -1725830878783527306L;

		public SizeException(String msg) {
			super(msg);
		}
	}

	public interface AccessLog {

		void accessLog(long time, String method, int status, int len,
                       String url, String post, String ret);

		class DefaultHttpClientAceessLog implements AccessLog {
			private static final Logger access = LoggerFactory.getLogger("httpclientaccess");

			public void accessLog(long time, String method, int status,
					int len, String url, String post, String ret) {
				if (post != null && post.length() > 200) {
					post = post.substring(0, 200);
					post.replaceAll("\n", "");
					post = post + "...";
				}
				if (ret != null) {
					ret = ret.trim();
					ret = ret.replaceAll("\n", "");
				}

				access.info(format("%s %s %s %s %s %s %s", time, method,
						status, len, url, isEmpty(post) ? "-" : post,
						isEmpty(ret) ? "-" : ret));
			}
		}
	}
}
