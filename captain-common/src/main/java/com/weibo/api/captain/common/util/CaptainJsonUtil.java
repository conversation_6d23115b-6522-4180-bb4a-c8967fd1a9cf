package com.weibo.api.captain.common.util;

import java.util.*;

import com.weibo.api.captain.common.GlobalInstance;
import com.weibo.api.captain.common.exception.CaptainExcepFactor;
import com.weibo.api.captain.common.exception.CaptainException;
import com.weibo.api.captain.common.model.CacheAgentStat;

import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.JsonNode;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.JsonBuilder;

import com.weibo.vintage.model.EndpointAddress;
import com.weibo.vintage.model.NamingServiceCluster;
import com.weibo.vintage.model.NamingServiceNode;

/**  
 * Package: com.weibo.api.captain.core.util  
 *  
 * File: JsonUtil.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainJsonUtil {

	
	public static String JSON_RS_UNKNOW_ERROR = buildErrorJson(CaptainExcepFactor.CAPTAIN_EXCEP_UNKNOWN);
	public static String JSON_RS_MALFORMED_PARAM = buildErrorJson(CaptainExcepFactor.CAPTAIN_EXCEP_MALFORMED_PARAM);
	public static String JSON_RS_UNSUPPORT_PARAM_CONTENT = buildErrorJson(CaptainExcepFactor.CAPTAIN_EXCEP_UNSUPPORT_PARAM_CONTENT);
	public static String BACKEND_SERVER_ERR = buildErrorJson(CaptainExcepFactor.CAPTAIN_EXCEP_BACKEND_SERVER_ERR);
	public static String JSON_RS_EMPTY = "{}";
	public static String JSON_RS_TRUE = buildTrueResultJson();
	
	/**
	 * 修复Vintage种的JsonUtil.getJsonMultiValues中的bug，vintage中value的获取采用了
	 * values.next().toString()，会导致返回的字串多带双引号，此处改为values.next().getTextValue()。
	 * 已通知@洪兵，等vintage修改后切换，避免重复方法。 2015.7.28
	 * @param node
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static List<String> getJsonMultiValues(JsonNode node){
		if(node == null){
			return Collections.EMPTY_LIST;			
		}
		List<String> results = new ArrayList<String>();
		Iterator<JsonNode> values = node.getElements();		
		while(values.hasNext()){
			String value = values.next().getTextValue();
			if(value.trim().length() > 0){
				results.add(value.trim());
			}
		}
		return results;
	}
	
	/**
	 * 构建异常响应结果，如果excepFactor为null，返回默认的json result。
	 * @param excepFactor
	 * @return
	 */
	public static String buildErrorRsp(CaptainExcepFactor excepFactor) {
		if(excepFactor == null) {
			return JSON_RS_UNKNOW_ERROR.toString();
		}
		return new JsonBuilder().append("error_code", String.valueOf(excepFactor.getErrorCode()))
				.append("error", excepFactor.getErrorMsg()).flip().toString();
	}
	
	/**
	 * 构建agent的部分机器列表
	 * @param cluster
	 * @return
	 */
	public static String buildCacheAgentClusterNodes(NamingServiceCluster cluster) {
		JsonBuilder jb = buildClusterNodes(cluster);
		return jb != null ? jb.toString() : JSON_RS_EMPTY;
	}
	
	public static String buildCacheAgentServiceNodes(String serviceName, Collection<NamingServiceCluster> clusters) {
		if(StringUtils.isBlank(serviceName) || clusters == null || clusters.size() == 0) {
			ApiLogger.warn(String.format("buildAgentServiceNodes for malformed params, serviceName:%s, clusters.size:%s", serviceName, clusters == null ? 0 : clusters.size()));
			return null;
		}
		
		JsonBuilder jb = new JsonBuilder();
		jb.append("service_id", serviceName);
		
		List<JsonBuilder> clusterJbs = new ArrayList<JsonBuilder>();
		for(NamingServiceCluster c : clusters) {
			JsonBuilder cjb = buildClusterNodes(c);
			if(cjb != null) {
				clusterJbs.add(cjb);
			}
		}
		jb.appendJsonArr("clusters", clusterJbs);
		
		jb.flip();
		return jb.toString();
	}
	
	public static String buildCacheAgentConsistenceProps(String serviceName, Collection<NamingServiceCluster> clusters) {
		if(StringUtils.isBlank(serviceName) || clusters == null || clusters.size() == 0) {
			ApiLogger.warn(String.format("buildAgentServiceNodes for malformed params, serviceName:%s, clusters.size:%s", serviceName, clusters == null ? 0 : clusters.size()));
			return null;
		}
		
		JsonBuilder jb = new JsonBuilder();
		jb.append("service_id", serviceName);
		
		List<JsonBuilder> clusterJbs = new ArrayList<JsonBuilder>();
		for(NamingServiceCluster c : clusters) {
			JsonBuilder cjb = buildClusterNodeConsistanceProps(c);
			if(cjb != null) {
				clusterJbs.add(cjb);
			}
		}
		jb.appendJsonArr("clusters", clusterJbs);
		
		jb.flip();
		return jb.toString();
	}
	
	public static String buildErrorJson(CaptainExcepFactor factor) {
		return new JsonBuilder().append("code", String.valueOf(factor.getErrorCode()))
		.append("desc", factor.getErrorMsg()).flip().toString();
	}
	
	public static List<JsonBuilder> toJsonBuild4EndpointAddress(List<EndpointAddress> addrs) {
		List<JsonBuilder> serverJsons = new ArrayList<JsonBuilder>();
		for(EndpointAddress adr : addrs) {
			JsonBuilder nodeJb = new JsonBuilder();
			nodeJb.append("host", adr.getHost());
			nodeJb.append("port", adr.getPort());
			
			nodeJb.flip();
			serverJsons.add(nodeJb);
		}
		
		return serverJsons;
	}
	
	/**
	 * 如果参数异常或没有node节点，返回null。
	 * @param cluster
	 * @return
	 */
	private static JsonBuilder buildClusterNodes(NamingServiceCluster cluster) {
		if(cluster == null || cluster.getWorkingNodes() == null || cluster.getWorkingNodes().size() == 0) {
			return null;
		}
		JsonBuilder jb = new JsonBuilder();
		jb.append("service_id", cluster.getServiceId());
		jb.append("cluster_id", cluster.getClusterId());
		jb.append("sign", cluster.getSign());
		
		List<EndpointAddress> addrs = new ArrayList<EndpointAddress>();
		for(NamingServiceNode node : cluster.getWorkingNodes()) {
			addrs.add(node.getAddress());
		}
		
		List<JsonBuilder> addrJsons = toJsonBuild4EndpointAddress(addrs);
		jb.appendJsonArr("working", addrJsons);
		
		jb.flip();
		return jb;
	}
	
	private static JsonBuilder buildClusterNodeConsistanceProps(NamingServiceCluster cluster) {
		if(cluster == null || cluster.getWorkingNodes() == null || cluster.getWorkingNodes().size() == 0) {
			return null;
		}
		JsonBuilder jb = new JsonBuilder();
		jb.append("service_id", cluster.getServiceId());
		jb.append("cluster_id", cluster.getClusterId());
		
		List<JsonBuilder> nodes = new ArrayList<JsonBuilder>();
		for(NamingServiceNode node : cluster.getWorkingNodes()) {
			CacheAgentStat stat = GlobalInstance.cacheAgentStatLatest.get(node.getAddress());
			if(stat == null) {
				ApiLogger.warn(String.format("stat should not null for [%s], serviceId = %s, clusterId = %s", node.getAddress(), cluster.getServiceId(), cluster.getClusterId()));
				continue;
			}
			nodes.add(stat.toJsonBuilderForConsistence());
		}
		jb.appendJsonArr("working", nodes);
		
		jb.flip();
		return jb;
	}
	
	private static String buildTrueResultJson() {
		return new JsonBuilder().append("code", "0")
				.append("desc", "true").flip().toString();
	}
	
	public static String buildErrorJson(CaptainException exception) {
        return new JsonBuilder().append("code", String.valueOf(exception.getFactor().getErrorCode()))
        .append("desc", exception.getMessage()).flip().toString();
    }

	public static JsonBuilder buildErrorBuilder(CaptainException exception) {
		return new JsonBuilder().append("code", String.valueOf(exception.getFactor().getErrorCode()))
				.append("desc", exception.getMessage());
	}
}
