package com.weibo.api.captain.common.util;

import com.weibo.platform.commons.switcher.SwitcherManager;
import com.weibo.platform.commons.switcher.SwitcherManagerFactoryLoader;
import net.sf.json.JSONException;
import net.sf.json.JSONObject;

/**
 * Package: com.weibo.api.captain.core.util  
 *  
 * File: CaptainUtil.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainUtil {
	public static final String SWITCHER_IGNORE_NEW_FIELD = "feature.clustermanager.biz.ignorefield";
	public static final String SWITCHER_IGNORE_FALSE_FIELD = "feature.clustermanager.biz.ignorefalsefield";
	public static final String SWITCHER_IGNORE_TRUE_FIELD = "feature.clustermanager.biz.ignoretruefield";
	public static final String SWITCHER_IGNORE_LOCATION_FALSE_FIELD = "feature.clustermanager.biz.ignorelocationfalsefield";
	public static final String SWITCHER_IGNORE_FLAG_ZERO_FIELD = "feature.clustermanager.biz.ignoreflagzerofield";
	public static long roundToPositiveDivisor(long divisor) {
		return divisor > 0 ? divisor : 1;
	}
	private static final SwitcherManager SWITCH_MANAGER = SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager();

	public static boolean ignoreNewField(){
		return SWITCH_MANAGER.getSwitcher(SWITCHER_IGNORE_NEW_FIELD).isOpen();
	}
	public static boolean ignoreFalseField(){
		return SWITCH_MANAGER.getSwitcher(SWITCHER_IGNORE_FALSE_FIELD).isOpen();
	}
	public static boolean ignoreTrueField(){
		return SWITCH_MANAGER.getSwitcher(SWITCHER_IGNORE_TRUE_FIELD).isOpen();
	}
	public static boolean ignoreLocationFalseField(){
		return SWITCH_MANAGER.getSwitcher(SWITCHER_IGNORE_LOCATION_FALSE_FIELD).isOpen();
	}
	public static boolean ignoreFlagZeroField(){
		return SWITCH_MANAGER.getSwitcher(SWITCHER_IGNORE_FLAG_ZERO_FIELD).isOpen();
	}

	public static int getForceWriteAll(JSONObject resource){
		int forceWriteAll=0;
		try {
			forceWriteAll = resource.getInt("forceWriteAll");
		}catch (JSONException e){
			//ignore
		}
		return forceWriteAll;
	}

	public static int getUpdateSlaveL1(JSONObject resource){
		int updateSlaveL1=1;
		try {
			updateSlaveL1 = resource.getInt("updateSlaveL1");
		}catch (JSONException e){
			//ignore
		}
		return updateSlaveL1;
	}

	public static int getlocalAffinity(JSONObject resource){
		int localAffinity=0;
		try {
			localAffinity = resource.getInt("localAffinity");
		}catch (JSONException e){
			//ignore
		}
		return localAffinity;
	}

	public static int getFlag(JSONObject resource){
		int flag=0;
		try {
			flag = resource.getInt("flag");
		}catch (JSONException e){
			//ignore
		}
		return flag;
	}

}
