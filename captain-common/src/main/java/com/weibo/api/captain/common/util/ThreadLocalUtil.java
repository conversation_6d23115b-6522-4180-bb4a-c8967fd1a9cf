package com.weibo.api.captain.common.util;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.JsonUtil;

import java.util.HashMap;
import java.util.Map;

public class ThreadLocalUtil {
    public static final String MasterSeerverHeader = "X-Upstream-Server";
    private static final ThreadLocal<Map<String,String>> vintageServerHolder = new ThreadLocal<Map<String,String>>();

    public static void setServer(String configAdd,String server) {
        // 获取当前线程的 Map
        Map<String, String> map = vintageServerHolder.get();
        // 修改 Map 中的值
        if (map == null) {
            ApiLogger.info("read master init thread local for server:"+configAdd+" with server:"+server);
            map = new HashMap<String, String>();
            vintageServerHolder.set(map);
        }
        if (map.containsKey(configAdd)) {
            return;
        }
        //configadd前缀有http://或者https://，如果server没有对应的前缀，则加上
        if (configAdd.startsWith("http") && !server.startsWith("http")) {
            server = "http://"+server;
            ApiLogger.info("read master set server to thread local with http, configAdd="+configAdd+",server="+server);
        }
        map.put(configAdd, server);
        ApiLogger.info("read master set server to thread local, current map"+ map);
        vintageServerHolder.set(map);
    }

    public static String getServer(String configAdd) {
        Map<String,String> map=vintageServerHolder.get();
        if (map == null || !map.containsKey(configAdd)) {
            ApiLogger.info("read master get server from thread local contains no configAdd = "+configAdd);
            return configAdd;
        }

        String result= map.get(configAdd);
        ApiLogger.info("read master get server from thread local, configAdd="+configAdd+",actual server="+result);
        return result;
    }

    public static void clear() {
        vintageServerHolder.remove();
    }

    public static boolean isInitialized() {
        return vintageServerHolder.get() != null;
    }
}
