package com.weibo.api.captain.common.util;

public class TraceIdContext {
    // 使用 ThreadLocal 存储 TraceId
    private static final ThreadLocal<String> traceIdHolder = new ThreadLocal<String>();

    // 设置 TraceId
    public static void setTraceId(String traceId) {
        traceIdHolder.set(traceId);
    }

    // 获取 TraceId
    public static String getTraceId() {
        return traceIdHolder.get();
    }

    // 清除 TraceId
    public static void clear() {
        traceIdHolder.remove();
    }
}
