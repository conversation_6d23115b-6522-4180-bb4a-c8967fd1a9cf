<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:property-placeholder location="classpath:captain.properties"/>

    <bean id="httpClient" class="com.weibo.api.captain.common.util.ApacheHttpClient">
        <constructor-arg index="0" value="300"/>
        <constructor-arg index="1" value="2000"/>
        <constructor-arg index="2" value="2000"/>
        <constructor-arg index="3" value="1048576"/>
    </bean>
    <bean id="staticConfigBiz" class="com.weibo.api.captain.common.service.StaticConfigBizImpl" scope="prototype">
        <property name="httpClient" ref="httpClient"></property>
        <property name="baseUrl" value=""></property>
    </bean>
    <bean id="namingServiceBiz" class="com.weibo.api.captain.common.service.NamingServiceBizImpl">
        <property name="httpClient" ref="httpClient"></property>
        <property name="baseUrl" value=""></property>
    </bean>
</beans>
