package com.weibo.api.captain.core;


import com.weibo.api.captain.core.model.CacheAgentSLAThreshold;
import com.weibo.api.captain.core.model.MemcacheSlaThreshold;

/**  
 * Package: com.weibo.api.captain.core.common  
 *  
 * File: CaptainConstants.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainConstants {

	
	public static String COMMA = ",";
	
	public static String COLON = ":";
	
	public static String DEFAULT_STATIC_CONFIG_KEY = "all";
	
	public static CacheAgentSLAThreshold DEFAULT_CACHE_AGENT_SLA_THRESHOLD = new CacheAgentSLAThreshold();
	
	public static MemcacheSlaThreshold DEFAULT_MEMCACHE_SLA_THRESHOLD = new MemcacheSlaThreshold(); 
	
	public static int HASH_FACTOR = 31;
}
