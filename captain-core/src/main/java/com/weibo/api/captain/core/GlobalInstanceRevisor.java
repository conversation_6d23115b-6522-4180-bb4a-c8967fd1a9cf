package com.weibo.api.captain.core;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.core.biz.CacheAgentBiz;
import com.weibo.api.captain.core.biz.MemcachedBiz;
import com.weibo.api.captain.core.biz.NamingServiceBiz;
import com.weibo.api.captain.core.biz.StaticConfigBiz;
import com.weibo.api.captain.core.model.CacheAgentBizStat;
import com.weibo.api.captain.core.model.CacheAgentConfig;
import com.weibo.api.captain.core.model.CacheAgentGroupConfig;
import com.weibo.api.captain.core.model.CacheAgentSLAThreshold;
import com.weibo.api.captain.core.model.CacheAgentStat;
import com.weibo.api.captain.core.model.MemcacheNodeStat;
import com.weibo.api.captain.core.model.MemcacheSlaThreshold;
import com.weibo.api.captain.core.task.CacheAgentDeployRefreshTask;
import com.weibo.api.captain.core.task.CacheAgentStatProbeTask;
import com.weibo.api.captain.core.task.CacheAgentStaticConfigRefreshTask;
import com.weibo.api.captain.core.task.McStatProbeTask;
import com.weibo.vintage.model.EndpointAddress;
import com.weibo.vintage.model.NamingServiceCluster;
import com.weibo.vintage.model.NamingServiceNode;

/**  
 * Package: com.weibo.api.captain.core  
 *  
 * File: GlobalInstanceRevisor.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class GlobalInstanceRevisor {

	@Resource(name="cacheAgentClusterSlas")
	private Map<String, CacheAgentSLAThreshold> cacheAgentClusterSlas;
	
	@Resource(name="mcSlas")
	private Map<String, MemcacheSlaThreshold> mcSlas;
	
	@Resource(name="serviceIds")
	private List<String> serviceIds;
	
	@Resource(name="staticConfigGroupIds")
	private List<String> staticConfigGroupIds;
	
	@Resource(name="namingServiceBiz")
	private NamingServiceBiz namingServiceBiz;
	
	@Resource(name="staticConfigBiz")
	private StaticConfigBiz staticConfigBiz;
	
	@Resource(name="cacheAgentBiz")
	private CacheAgentBiz cacheAgentBiz;
	
	@Resource(name="memcachedBiz")
	private MemcachedBiz memcachedBiz;
	
	private long taskScheduledDelaySeconds = 60 * 5;
	
	private AtomicBoolean inited = new AtomicBoolean(false);
	
	private AtomicBoolean scheduled = new AtomicBoolean(false);
	
	private TaskExecutor taskExecutor = TaskExecutor.getInstance();
	
	private ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(1);
	
	public synchronized void initGlobalInstanceCache(){
		if(inited.get()) {
			return;
		}
		
		initServiceAndGroupIds();
		
		initSlaThreshold();
		
		reviseTheGlobalInstanceCache();
		
		inited.set(true);
	}
	
	public synchronized void scheduleUpdateGlobalInstaceCache(){
		if(scheduled.get()) {
			return;
		}
		
		scheduledExecutor.scheduleWithFixedDelay(new Runnable() {
			
			@Override
			public void run() {
				try {
					reviseTheGlobalInstanceCache();
				} catch (Exception e) {
					ApiLogger.warn("reviseTheGlobalInstanceCache false", e);
				}
				
			}
		}, taskScheduledDelaySeconds, taskScheduledDelaySeconds, TimeUnit.SECONDS);
		
		scheduled.set(true);
	}
	
	public boolean isInit() {
		return inited.get();
	}
	
	private void reviseTheGlobalInstanceCache(){
		ApiLogger.info("Revise globalInstance cache start...");
		
		/** refresh和stat的顺序不能颠倒，因为stat依赖于refresh获取的数据 */
		try {
			refreshCacheAgentDeploy();
			refreshStaticConfig();
			probeCacheAgentStat();
			probeMcStat();
		} catch (Exception e) {
			ApiLogger.error("found error in reviseTheGlobalInstanceCache", e);
		}
		
		ApiLogger.info("Revise globalInstance cache completed!");
	}
	
	private void initServiceAndGroupIds() {
		// init service & groupIds
		if(this.serviceIds != null && this.serviceIds.size() > 0) {
			GlobalInstance.serviceIds.addAll(this.serviceIds);
		}
				
		if(this.staticConfigGroupIds != null && this.staticConfigGroupIds.size() > 0) {
			GlobalInstance.groupIds.addAll(this.staticConfigGroupIds);
		}
	}
	
	private void initSlaThreshold(){
		if(cacheAgentClusterSlas != null && cacheAgentClusterSlas.size() > 0) {
			GlobalInstance.cacheAgentClusterSlaThreshold.putAll(this.cacheAgentClusterSlas);
		}
		
		if(mcSlas != null && mcSlas.size() > 0) {
			GlobalInstance.mcSlaThreshold.putAll(mcSlas);
		}
	}
	
	private void refreshCacheAgentDeploy() {
		if(serviceIds != null && serviceIds.size() == 0) {
			ApiLogger.info("Donnot refresh cacheAgent deploy, for there is no serviceIds");
			return;
		}
		
		ApiLogger.info(String.format("Start refresh cacheAgent deploy, serviceIds:%s ...", serviceIds));
		
		CacheAgentDeployRefreshTask deployRefreshTask = new CacheAgentDeployRefreshTask(namingServiceBiz, serviceIds);
		
		//first refresh， then update the global cache
		taskExecutor.submit(deployRefreshTask);
		updateCacheAgentDeployLocalCache();
		
		ApiLogger.info(String.format("Refresh cacheAgent completed! serviceIds:%s", serviceIds));
	}
	
	private synchronized void updateCacheAgentDeployLocalCache() {
		taskExecutor.waitAllTaskCompleted();
		GlobalInstance.cacheAgentServiceClusterLatest = GlobalInstance.cacheAgentServiceClusterProbing;
		GlobalInstance.cacheAgentServiceClusterProbing = new ConcurrentHashMap<String, Map<String,NamingServiceCluster>>();
		ApiLogger.debug("GlobalInstance.cacheAgentServiceClusterLatest:" + GlobalInstance.cacheAgentServiceClusterLatest);
	}
	
	private void refreshStaticConfig(){
		if(staticConfigGroupIds == null || staticConfigGroupIds.size() == 0) {
			ApiLogger.info("Donnot refresh static config of cacheAgent, for there is no groupids");
			return;
		}
		
		ApiLogger.info(String.format("Start refresh the static config of cacheAgent, groupIds:%s", staticConfigGroupIds));
		
		for(String gid : staticConfigGroupIds) {
			CacheAgentStaticConfigRefreshTask configRefreshTask = new CacheAgentStaticConfigRefreshTask(staticConfigBiz, gid);
			taskExecutor.submit(configRefreshTask);
		}
		
		updateStaticConfigLocalCache();
		updateMcAddressLocalCache();
		ApiLogger.info(String.format("Refresh cacheAgent completed! serviceIds:%s", serviceIds));
		
	}
	
	private synchronized void updateStaticConfigLocalCache() {
		taskExecutor.waitAllTaskCompleted();
		GlobalInstance.cacheAgentConfigsLatest = GlobalInstance.cacheAgentConfigsProbing;
		GlobalInstance.cacheAgentConfigsProbing = new ConcurrentHashMap<String, Map<String,CacheAgentConfig>>();
		ApiLogger.debug("GlobalInstance.cacheAgentConfigsLatest = " + GlobalInstance.cacheAgentConfigsLatest);
	}
	
	private synchronized void updateMcAddressLocalCache() {
		taskExecutor.waitAllTaskCompleted();
		
		String serviceId;
		Map<String, CacheAgentConfig> configMap;
		List<EndpointAddress> addrs = null;
		for(Map.Entry<String, Map<String, CacheAgentConfig>> entry: GlobalInstance.cacheAgentConfigsLatest.entrySet()) {
			serviceId = entry.getKey();
			configMap = entry.getValue();
			for(CacheAgentConfig config : configMap.values()) {
				if(config.getGroupConfs() == null || config.getGroupConfs().size() == 0) {
					continue;
				}
				for(CacheAgentGroupConfig gcfg : config.getGroupConfs()) {
					addrs = gcfg.extractAllMcNodes();
					for(EndpointAddress addr : addrs) {
						GlobalInstance.mcAddressGroupIdsProbing.put(addr, serviceId);
					}
				}
			}
		}
		
		GlobalInstance.mcAddressGroupIdsLatest = GlobalInstance.mcAddressGroupIdsProbing;
		GlobalInstance.mcAddressGroupIdsProbing = new ConcurrentHashMap<EndpointAddress, String>();
		
		ApiLogger.debug("GlobalInstance.mcAddressGroupIdsLatest = " + GlobalInstance.mcAddressGroupIdsLatest);
	}
	
	
	private void probeCacheAgentStat(){
		if(GlobalInstance.cacheAgentServiceClusterLatest.size() == 0) {
			ApiLogger.info("Donnot probe cacheAgent stats for the GlobalInstance.cacheAgentServiceClusterLatest.size() is 0");
			return;
		}
		
		ApiLogger.info(String.format("Start probe cacheAgent stat, cluster size:%s", GlobalInstance.cacheAgentServiceClusterLatest.size()));
		
		int taskCount = 0;
		for(Map<String, NamingServiceCluster> entry : GlobalInstance.cacheAgentServiceClusterLatest.values()) {
			for(NamingServiceCluster cluster : entry.values()) {
				for(NamingServiceNode node : cluster.getWorkingNodes()) {
					CacheAgentStatProbeTask task = new CacheAgentStatProbeTask(cacheAgentBiz, node.getAddress());
					taskExecutor.submit(task);
					taskCount++;
				}
			}
		}
		
		taskExecutor.waitAllTaskCompleted();
		ApiLogger.info(String.format("Probe cacheAgent stat completed and will continue to probe biz stat later. taskCount: %s, ", taskCount));
		
		continueProbeCacheAgentBizStat();
	}
	
	
	
	private void continueProbeCacheAgentBizStat() {
		EndpointAddress addr = null;
		CacheAgentStat stat = null;
		int taskCount = 0;
		
		ApiLogger.info(String.format("Start probe cacheAgent biz stat, cacheAgentStatProbing.size:%s start...", GlobalInstance.cacheAgentStatProbing.size()));
		
		for(Map.Entry<EndpointAddress, CacheAgentStat> entry: GlobalInstance.cacheAgentStatProbing.entrySet()) {
			addr = entry.getKey();
			stat = entry.getValue();
			if(StringUtils.isBlank(stat.getConfigGroup()) || StringUtils.isBlank(stat.getConfigKey())) {
				ApiLogger.warn(String.format("stat miss config group and key, for cacheservice [%s]", addr));
				continue;
			}
			
			Map<String, CacheAgentConfig> keyConfigs = GlobalInstance.cacheAgentConfigsLatest.get(stat.getConfigGroup());
			if(keyConfigs == null || !keyConfigs.containsKey(stat.getConfigKey())) {
				ApiLogger.warn(String.format("found unknow groupId for CacheAgentConfig, groupId = %s, addr = %s", stat.getConfigGroup(), stat.getAddr()));
				continue;
			}
			CacheAgentConfig config = keyConfigs.get(stat.getConfigKey());
			for(CacheAgentGroupConfig gcfg : config.getGroupConfs()) {
				CacheAgentStatProbeTask clusterTask = new CacheAgentStatProbeTask(cacheAgentBiz, stat, gcfg.getName());
				taskExecutor.submit(clusterTask);
				taskCount++;
			}
			
		}
		
		updateCacheAgentStatLocalCache();
		ApiLogger.info(String.format("Start probe cacheAgent biz stat completed! cacheservice ount:%s, taskcount:%s ", GlobalInstance.cacheAgentStatLatest.size(), taskCount));
		
	}
	
	private void updateCacheAgentStatLocalCache() {
		taskExecutor.waitAllTaskCompleted();
		
		//first correct sla and bizSla for cacheAgent
		CacheAgentStat latestStat = null;
		CacheAgentBizStat latestBizStat = null;
		for(CacheAgentStat stat : GlobalInstance.cacheAgentStatProbing.values()) {
			//correct sla
			latestStat = GlobalInstance.cacheAgentStatLatest.get(stat.getAddr());
			if(latestStat == null) {
				if(inited.get()) {
					ApiLogger.warn("found new cacheAgent with stat :" + stat);
				}
				continue;
			}
			stat.correctSla(latestStat);
			
			//correct biz sla
			for(Map.Entry<String, CacheAgentBizStat> bizStatEntry : stat.getBizStats().entrySet()) {
				latestBizStat = latestStat.getBizStats().get(bizStatEntry.getKey());
				if(latestBizStat == null) {
					if(inited.get()) {
						ApiLogger.warn(String.format("found new biz [%s] in cacheAgent with addr :", bizStatEntry.getKey(), stat.getAddr()));
					}
					continue;
				}
				bizStatEntry.getValue().correctSla(latestBizStat);
			}
		}
		GlobalInstance.cacheAgentStatLatest = GlobalInstance.cacheAgentStatProbing;
		GlobalInstance.cacheAgentStatProbing = new ConcurrentHashMap<EndpointAddress, CacheAgentStat>();
		ApiLogger.debug("GlobalInstance.cacheAgentStatLatest = " + GlobalInstance.cacheAgentStatLatest);
	}
	
	private void probeMcStat() {
		if(GlobalInstance.cacheAgentConfigsLatest.size() == 0) {
			ApiLogger.info("Donnot probe mc stat for GlobalInstance.cacheAgentConfigs.size() is 0 ");
			return;
		}
		
		ApiLogger.info(String.format("Start probing mc stat, mcNode.size:%s", GlobalInstance.mcAddressGroupIdsLatest.size()));
		
		for(EndpointAddress ep : GlobalInstance.mcAddressGroupIdsLatest.keySet()) {
			McStatProbeTask task = new McStatProbeTask(memcachedBiz, ep);
			taskExecutor.submit(task);
		}
		
		updateMcStatLocalCache();
	}
	
	private void updateMcStatLocalCache(){
		taskExecutor.waitAllTaskCompleted();
		
		// correct mc stat
		MemcacheNodeStat lastStat = null;
		for(MemcacheNodeStat stat : GlobalInstance.mcStatProbing.values()) {
			lastStat = GlobalInstance.mcStatLatest.get(stat.getAddress());
			if(lastStat == null) {
				if(inited.get()) {
					ApiLogger.warn("found new mc node, addr = %s" + stat.getAddress());
				}
				continue;
			}
			stat.correctSla(lastStat);
		}
		GlobalInstance.mcStatLatest = GlobalInstance.mcStatProbing;
		GlobalInstance.mcStatProbing = new ConcurrentHashMap<EndpointAddress, MemcacheNodeStat>();
		ApiLogger.debug("GlobalInstance.mcStatLatest = " + GlobalInstance.mcStatLatest);
	}
	
	/** ====================== getters & setters ====================== */
	public List<String> getServiceIds() {
		return serviceIds;
	}

	public List<String> getStaticConfigGroupIds() {
		return staticConfigGroupIds;
	}

	public long getTaskScheduledDelaySeconds() {
		return taskScheduledDelaySeconds;
	}

	public void setTaskScheduledDelaySeconds(long taskScheduledDelaySeconds) {
		this.taskScheduledDelaySeconds = taskScheduledDelaySeconds;
	}
	
	public Map<String, CacheAgentSLAThreshold> getCacheAgentClusterSlas() {
		return cacheAgentClusterSlas;
	}

	public void setCacheAgentClusterSlas(
			Map<String, CacheAgentSLAThreshold> cacheAgentClusterSlas) {
		this.cacheAgentClusterSlas = cacheAgentClusterSlas;
	}

	public Map<String, MemcacheSlaThreshold> getMcSlas() {
		return mcSlas;
	}

	public void setMcSlas(Map<String, MemcacheSlaThreshold> mcSlas) {
		this.mcSlas = mcSlas;
	}

	public void setServiceIds(List<String> serviceIds) {
		this.serviceIds = serviceIds;
	}

	public void setStaticConfigGroupIds(List<String> staticConfigGroupIds) {
		this.staticConfigGroupIds = staticConfigGroupIds;
	}
	
	public NamingServiceBiz getNamingServiceBiz() {
		return namingServiceBiz;
	}

	public void setNamingServiceBiz(NamingServiceBiz namingServiceBiz) {
		this.namingServiceBiz = namingServiceBiz;
	}

	public StaticConfigBiz getStaticConfigBiz() {
		return staticConfigBiz;
	}

	public void setStaticConfigBiz(StaticConfigBiz staticConfigBiz) {
		this.staticConfigBiz = staticConfigBiz;
	}

	public CacheAgentBiz getCacheAgentBiz() {
		return cacheAgentBiz;
	}

	public void setCacheAgentBiz(CacheAgentBiz cacheAgentBiz) {
		this.cacheAgentBiz = cacheAgentBiz;
	}

	public MemcachedBiz getMemcachedBiz() {
		return memcachedBiz;
	}

	public void setMemcachedBiz(MemcachedBiz memcachedBiz) {
		this.memcachedBiz = memcachedBiz;
	}
	
}
