package com.weibo.api.captain.core;

import javax.annotation.Resource;

import cn.sina.api.commons.util.ApiLogger;


/**  
 * Package: com.weibo.api.captain.core.model  
 *  
 * File: Initialization.java   
 *  
 * 加载SlaThreashold，初始化线程池。  
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class Initialization {

	@Resource(name="globalInstanceRevisor")
	private GlobalInstanceRevisor globalInstanceRevisor;
	
	
	public void init(){
		ApiLogger.info("Init start...");
		globalInstanceRevisor.initGlobalInstanceCache();
		globalInstanceRevisor.scheduleUpdateGlobalInstaceCache();
		
		ApiLogger.info("Init completed!");
	}


	public GlobalInstanceRevisor getGlobalInstanceRevisor() {
		return globalInstanceRevisor;
	}


	public void setGlobalInstanceRevisor(GlobalInstanceRevisor globalInstanceRevisor) {
		this.globalInstanceRevisor = globalInstanceRevisor;
	}

	
	
	
	
}
