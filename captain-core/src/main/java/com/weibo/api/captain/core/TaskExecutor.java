package com.weibo.api.captain.core;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.core.task.AbstractTask;

/**  
 * Package: com.weibo.api.captain.core  
 *  
 * File: TaskExecutor.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class TaskExecutor {
	
	private static final int EXECUTOR_THREADS = 20;
	
	private static int TIMEOUT_MILLS = 1000;
	
	private ThreadPoolExecutor taskExecutor = (ThreadPoolExecutor)Executors.newFixedThreadPool(EXECUTOR_THREADS);
	
	private Map<Future<Boolean>, String> futureNameMap = new ConcurrentHashMap<Future<Boolean>, String>();
	
	private static TaskExecutor instance = new TaskExecutor();
	
	private TaskExecutor() {
		
	}
	
	public static TaskExecutor getInstance() {
		return instance;
	}
	
	public boolean submit(AbstractTask task) {
		return submit(task, false);
	}
	
	public boolean submit(AbstractTask task, boolean waitCompleted){
		
		Future<Boolean> future = taskExecutor.submit(task);
		
		if(!waitCompleted) {
			futureNameMap.put(future, task.toString());
			return false;
		}
		
		boolean rs = false;
		try {
			rs = future.get(TIMEOUT_MILLS, TimeUnit.MILLISECONDS);
		} catch (Exception e) {
			ApiLogger.warn(String.format("task call failure, name = %s", task.getTaskName()), e);
		}
		
		ApiLogger.info(String.format("task call completed, name = %s, rs = %s", task.getTaskName(), rs));
		return rs;
	}
	
	public synchronized void waitAllTaskCompleted(){
		if(futureNameMap.size() == 0) {
			return;
		}
		
		ApiLogger.info(String.format("waitAllTaskCompleted - wait %s futures start...",futureNameMap.size()));
		
		int count = 0;
		long t1 = System.currentTimeMillis();
		List<Future<Boolean>> futures = new ArrayList<Future<Boolean>>(futureNameMap.keySet());
		for(Future<Boolean> future : futures) {
			try {
				future.get(TIMEOUT_MILLS, TimeUnit.MILLISECONDS);
			} catch (Exception e) {
				ApiLogger.warn(String.format("waitAllTaskCompleted - get task [%s] result false in waitAllTaskCompleted()", futureNameMap.get(future)), e);
			}
			count++;
			futureNameMap.remove(future);
		}
		
		ApiLogger.info(String.format("waitAllTaskCompleted - wait %s futures run completed in %s mills, now futrues count: %s", count, (System.currentTimeMillis() - t1), futureNameMap.size()));
	}
	

}
