package com.weibo.api.captain.core.biz;


import com.weibo.api.captain.core.model.CacheAgentStat;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.agent.service  
 *  
 * File: CacheAgentService.java  
 * <pre>
 * 对cacheAgent进行访问，获取sign、统计、sla等。
 * </pre>
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public interface CacheAgentBiz {


	/**
	 * 获取cache agent的sla
	 * @param ip
	 * @param port
	 * @return
	 */
	CacheAgentStat getCacheAgentStat(EndpointAddress address);
	
	/**
	 * 获取cacheAgent某个cluster的统计信息
	 * @param stat
	 * @param clusterName
	 * @return
	 */
	void fillCacheAgentBizStat(CacheAgentStat stat, String bizName);
}
