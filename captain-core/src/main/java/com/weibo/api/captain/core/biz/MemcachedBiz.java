package com.weibo.api.captain.core.biz;


import com.weibo.api.captain.core.model.MemcacheNodeStat;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.mc.service  
 *  
 * File: MCService.java   
 * <pre>
 * 对memcached进行访问，用于获取各种 统计、sla。
 * </pre>
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public interface MemcachedBiz {

	/**
	 * 获取某个mc节点的统计信息
	 * @param ip
	 * @param port
	 * @return
	 */
	public MemcacheNodeStat getMcNodeStat(EndpointAddress address);
}
