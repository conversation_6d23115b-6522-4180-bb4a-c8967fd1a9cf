package com.weibo.api.captain.core.biz;

import java.util.Map;

import com.weibo.api.captain.core.model.CacheAgentConfig;


/**  
 * Package: com.weibo.api.captain.core.config.service  
 *  
 * File: configService.java  
 * 
 *  <pre>
 *  访问configServer，从而进行静态配置更新、获取等底层业务操作。
 *  mc配置是静态配置服务，层次为：groupId-->key-->configVal。目前用的key都是“all”。
 *  
 *  
 *  </pre>
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public interface StaticConfigBiz {

	
	/**
	 * 更新groupId、key对应的静态配置
	 * @param serverCluster
	 * @return
	 */
	boolean register(String groupId, String key, String configStr);
	
	/**
	 * 取消注册
	 * @param groupId
	 * @param key
	 * @return
	 */
	boolean unregister(String groupId, String key);
	
	/**
	 * 获取groupId、key对应的configVal
	 * @param groupId
	 * @return
	 */
	CacheAgentConfig lookup(String groupId, String key);
	
	/**
	 *获取groupId下所有的key及配置
	 * @param groupId
	 * @return
	 */
	Map<String, CacheAgentConfig> lookup(String groupId);
	
}
