package com.weibo.api.captain.core.biz.spi;

import java.net.InetSocketAddress;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.common.memcache.CaptainMemcacheClientUtil;
import com.weibo.api.captain.core.biz.CacheAgentBiz;
import com.weibo.api.captain.core.model.CacheAgentStat;
import com.weibo.api.captain.core.protocol.CacheAgentProtocol;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.agent.service.impl  
 *  
 * File: CacheAgentServiceImpl.java  
 * <pre>
 * 对cacheAgent进行访问，获取sign、统计、sla等。
 * </pre> 
 *  
 * Author: fishermen  
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentBizImpl implements CacheAgentBiz {

	
	@Override
	public CacheAgentStat getCacheAgentStat(EndpointAddress address) {
		if(address == null 
				|| StringUtils.isBlank(address.getHost()) 
				|| address.getPort() < 1) {
			ApiLogger.warn(String.format("getCacheAgentStat false, for address is malformed: %s", address));
			return null;
		}
		
		InetSocketAddress inetAddress = new InetSocketAddress(address.getHost(), address.getPort());
		Map<String, String> statsRs = CaptainMemcacheClientUtil.stats(inetAddress);
		
		CacheAgentStat stat = new CacheAgentStat(address);
		CacheAgentProtocol.parseAgentStat(stat, statsRs);
		
		return stat;
	}
	
	@Override
	public void fillCacheAgentBizStat(CacheAgentStat stat, String bizName) {
		if(stat == null 
				|| StringUtils.isBlank(bizName)){
					ApiLogger.warn(String.format("getCacheAgentStat false for malformed params, stat=%s, bizName=%s", stat, bizName));
					return;
		}
		
		InetSocketAddress sockAddr = new InetSocketAddress(stat.getAddr().getHost(), stat.getAddr().getPort());
		Map<String, String> statsRs = CaptainMemcacheClientUtil.stats(sockAddr, bizName);
		
		CacheAgentProtocol.parseAgentClusterStat(stat, bizName, statsRs);
	}
}
