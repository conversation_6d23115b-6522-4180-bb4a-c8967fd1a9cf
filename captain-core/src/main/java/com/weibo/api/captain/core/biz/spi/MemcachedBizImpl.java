package com.weibo.api.captain.core.biz.spi;

import java.net.InetSocketAddress;
import java.util.Map;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.common.memcache.CaptainMemcacheClientUtil;
import com.weibo.api.captain.core.biz.MemcachedBiz;
import com.weibo.api.captain.core.model.MemcacheNodeStat;
import com.weibo.api.captain.core.protocol.MemcachedProtocol;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.mc.service.impl  
 *  
 * File: MCServiceImpl.java
 * 
 * <pre>
 * 对memcached进行访问，用于获取各种 统计、sla。
 * </pre>
 *  
 * Author: fishermen    
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class MemcachedBizImpl implements MemcachedBiz{

	@Override
	public MemcacheNodeStat getMcNodeStat(EndpointAddress address) {
		
		if(address == null) {
			ApiLogger.info("getMcNodeStat false, for malformed addr: %s", address);
			return null;
		}
		
		InetSocketAddress sockAddress = new InetSocketAddress(address.getHost(), address.getPort());
		Map<String, String> statsRs = CaptainMemcacheClientUtil.stats(sockAddress); 
		
		MemcacheNodeStat stat = new MemcacheNodeStat();
		stat.setAddress(address);
		stat = MemcachedProtocol.parseMcStat(stat, statsRs);
		
		return stat;
	}
	
	
}
