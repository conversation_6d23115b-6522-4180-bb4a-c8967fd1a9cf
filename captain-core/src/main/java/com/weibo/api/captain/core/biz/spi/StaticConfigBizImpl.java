package com.weibo.api.captain.core.biz.spi;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.weibo.api.captain.common.util.ApacheHttpClient;
import org.apache.commons.lang.StringUtils;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.core.biz.StaticConfigBiz;
import com.weibo.api.captain.core.model.CacheAgentConfig;
import com.weibo.api.captain.core.util.CaptainHttpUtil;
import com.weibo.vintage.json.JsonUtil;
import com.weibo.vintage.model.ResponsePacket;
import com.weibo.vintage.model.StaticsConfigMap;
import com.weibo.vintage.utils.VintageConstants;

/**
 * Package: com.weibo.api.captain.core.config.service.impl
 * 
 * File: ConfigServiceImpl.java
 * 
 * <pre>
 * 访问configServer，从而进行静态配置更新、获取等底层业务操作。
 * mc配置是静态配置服务，层次为：groupId-->key-->configVal。目前用的key都是“all”。
 * 
 * </pre>
 * 
 * Author: fishermen
 * 
 * Copyright @ 2015 Corpration Name
 * 
 */
public class StaticConfigBizImpl implements StaticConfigBiz {

	private static String STATIC_CONFIG_URI = "/1/config/service";

	private String baseUrl;

	/** 访问vintage资源，所以这里保持和vintage使用相同的httpClient */
	@Resource(name = "httpClient")
	private ApacheHttpClient httpClient;

	@Override
	public boolean register(String groupId, String key, String configStr) {
		if(StringUtils.isBlank(groupId)
				|| StringUtils.isBlank(key)
				|| StringUtils.isBlank(configStr)) {
			ApiLogger.warn(String.format("register config false, [groupId=%s], [key=%s], [configStr=%s]",
					groupId, key, configStr));
			return false;
		}
		
		if (!CacheAgentConfig.validateStr(configStr)) {
			ApiLogger.warn(String.format(
					"update config false, groupId:%s, key:%s, configStr:%s",
					groupId, key, configStr));
			return false;
		}
		
		// build name-value pairs
		Map<String, String> nameValuePairs = new HashMap<String, String>();
		nameValuePairs.put("action", VintageConstants.CONFIG_SERVICE_REGISTER);
		nameValuePairs.put("group", CaptainHttpUtil.encoding(groupId));
		nameValuePairs.put("key", CaptainHttpUtil.encoding(key));
		nameValuePairs.put("value", CaptainHttpUtil.encoding(configStr));

		String url = getStaticConfigUrl();
		
		String responseData = CaptainHttpUtil.doHttpPost(getBaseUrl(),httpClient, url, nameValuePairs);
		ResponsePacket packet = CaptainHttpUtil.parseResponse(responseData, true);
		boolean registerSuccess = false;
		if (CaptainHttpUtil.validateResponse(packet,
				VintageConstants.CONFIG_SERVICE_REGISTER)) {
			registerSuccess = packet.getJsonNodeBody() != null
					&& packet.getJsonNodeBody().get("success") != null
					&& JsonUtil.getJsonBooleanValue(packet.getJsonNodeBody()
							.get("success"), false);

			ApiLogger.info(String.format("static config register 3, url = %s, params = %s, rs = %s, rsMsg = %s",
					url, nameValuePairs, registerSuccess, packet));

			return registerSuccess;
		}
		ApiLogger.info(String.format("static config register false for rsp is invalid, url:%s, params:%s, rs:%s. rsMsg = %s", 
				url, nameValuePairs, registerSuccess, packet));
		return registerSuccess;

	}
	
	public boolean unregister(String groupId, String key) {
		if(StringUtils.isBlank(groupId) || StringUtils.isBlank(key)) {
			ApiLogger.warn(String.format("static config unregister false for malformed params, groupId = %s, key = %s ", 
					groupId, key));
			return false;
		}
		
		// build name-value pairs
		Map<String, String> nameValuePairs = new HashMap<String, String>();
		nameValuePairs.put("action", VintageConstants.CONFIG_SERVICE_UNREGISTER);
		nameValuePairs.put("group", CaptainHttpUtil.encoding(groupId));
		nameValuePairs.put("key", CaptainHttpUtil.encoding(key));

		String url = getStaticConfigUrl();
		
		String responseData = CaptainHttpUtil.doHttpPost(getBaseUrl(),httpClient, url, nameValuePairs);
		ResponsePacket packet = CaptainHttpUtil.parseResponse(responseData, true);
		boolean result = false;
		if (CaptainHttpUtil.validateResponse(packet,
				VintageConstants.CONFIG_SERVICE_UNREGISTER)) {
			result = packet.getJsonNodeBody() != null
					&& packet.getJsonNodeBody().get("success") != null
					&& JsonUtil.getJsonBooleanValue(packet.getJsonNodeBody()
							.get("success"), false);
		}
		
		ApiLogger.info(String.format("static config register %s, url=%s, params=%s, packet=%s",  result,
				url, nameValuePairs, packet));
		return result;
	}

	/**
	 * 获取groupId、key对应的configVal
	 * @param groupId
	 * @return
	 */
	public CacheAgentConfig lookup(String groupId, String key) {
		if(StringUtils.isBlank(groupId) || StringUtils.isBlank(key)) {
			ApiLogger.warn(String.format("static-config lookup false, for malformed params, groupId=%s, key=%s", groupId, key));
			return null;
		}
		
		StringBuilder urlBuilder = new StringBuilder();
		urlBuilder.append(getStaticConfigUrl()).append("?action=").append(VintageConstants.CONFIG_SERVICE_LOOKUP)
				.append("&group=").append(CaptainHttpUtil.encoding(groupId))
				.append("&key=").append(CaptainHttpUtil.encoding(key));
		
		
		String responseData = CaptainHttpUtil.doHttpGetContent(httpClient, urlBuilder.toString(), true);
		
		Map<String, CacheAgentConfig> configMap = parseRspForCacheAgentConfig(responseData);
		CacheAgentConfig config = configMap != null ? configMap.get(key) : null;
			
		ApiLogger.info(String.format("static-config lookup by group-key, url=[%s], config=[%s]", urlBuilder, config));
		
		return config;
	}
	
	@Override
	public Map<String, CacheAgentConfig> lookup(String groupId) {
		if(StringUtils.isBlank(groupId)) {
			ApiLogger.warn(String.format("static-config lookup false, for groupId = %s", groupId));
			return null;
		}
		
		String url = getStaticConfigUrl();
		StringBuilder urlBuilder = new StringBuilder();
		urlBuilder.append(url).append("?action=").append(VintageConstants.CONFIG_SERVICE_LOOKUP)
				.append("&group=").append(CaptainHttpUtil.encoding(groupId));
		
		// call the statics configuration server's URL and accept the GZIP stream
		String responseData = CaptainHttpUtil.doHttpGetContent(httpClient, urlBuilder.toString(), true);
		Map<String, CacheAgentConfig> result = parseRspForCacheAgentConfig(responseData);
		
		ApiLogger.info(String.format("static-config lookup by group, url=[%s], lookupRs=[%s]", urlBuilder, result));
		return result;
	}
	
	private Map<String, CacheAgentConfig> parseRspForCacheAgentConfig(String responseData) {
		// parser the data that receiving from remote server
		ResponsePacket responsePacket = CaptainHttpUtil.parseResponse(responseData, true);

		if (CaptainHttpUtil.validateResponse(responsePacket, VintageConstants.CONFIG_SERVICE_LOOKUP)) {
			String jsonData = responsePacket.getJsonNodeBody().toString();
			StaticsConfigMap staticConfig = StaticsConfigMap.parser(jsonData);
			if (staticConfig != null) {
				return CacheAgentConfig.parseVintageStaticConfigMap(staticConfig);
			}
		}
		return null;
	}

	public String getBaseUrl() {
		return baseUrl;
	}

	public void setBaseUrl(String baseUrl) {
		this.baseUrl = baseUrl;
	}

	public ApacheHttpClient getHttpClient() {
		return httpClient;
	}

	public void setHttpClient(ApacheHttpClient httpClient) {
		this.httpClient = httpClient;
	}
	
	private String getStaticConfigUrl() {
		return this.baseUrl + STATIC_CONFIG_URI;
	}
	

}
