package com.weibo.api.captain.core.exception;


import com.weibo.vintage.exception.ExcepFactor;
import com.weibo.vintage.model.HttpStatus;

/**  
 * Package: com.weibo.api.captain.core.exception  
 *  
 * File: CaptainExcepFactor.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainExcepFactor extends ExcepFactor{

	private static final long serialVersionUID = 4290557563399666747L;
	
	public static final CaptainExcepFactor CAPTAIN_EXCEP_UNKNOWN = new CaptainExcepFactor(HttpStatus.INTERNAL_SERVER_ERROR, 1, "unknown internal error", "未知服务异常");
	
	public static final CaptainExcepFactor CAPTAIN_EXCEP_MALFORMED_PARAM = new CaptainExcepFactor(HttpStatus.BAD_REQUEST, 2, "malformed param", "非法参数");
	
	public static final CaptainExcepFactor CAPTAIN_EXCEP_UNSUPPORT_PARAM_CONTENT = new CaptainExcepFactor(HttpStatus.BAD_REQUEST, 3, "unsupport param content", "不支持提供的参数数值");
	
	public static final CaptainExcepFactor CAPTAIN_EXCEP_BACKEND_SERVER_ERR = new CaptainExcepFactor(HttpStatus.INTERNAL_SERVER_ERROR, 9, "backend server error", "后端server请求失败");
	
	protected CaptainExcepFactor(HttpStatus httpStatus,
		    int errorCode, String errorMsg, String errorMsgCn) {
	    	super(9, httpStatus, errorCode, errorMsg, errorMsgCn);
	 }
}
