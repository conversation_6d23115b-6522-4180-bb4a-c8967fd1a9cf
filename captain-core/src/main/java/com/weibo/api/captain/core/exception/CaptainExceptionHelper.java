package com.weibo.api.captain.core.exception;

import com.weibo.vintage.exception.ExcepFactor;


/**  
 * Package: com.weibo.api.captain.core.exception  
 *  
 * File: CaptainExceptionHelper.java   
 *  
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainExceptionHelper {

	public static CaptainException newMatrixException(ExcepFactor factor) {
		return new CaptainException(factor);
	}

	public static CaptainException newMatrixException(ExcepFactor factor, Object message) {
		return new CaptainException(factor, message);
	}
	
	public static CaptainException newMatrixException(ExcepFactor factor, Object[] args) {
		return new CaptainException(factor, args);
	}
}
