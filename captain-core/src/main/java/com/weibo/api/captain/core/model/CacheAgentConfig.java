package com.weibo.api.captain.core.model;

import java.io.File;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.weibo.api.captain.core.util.CaptainUtil;
import org.apache.commons.lang.StringUtils;
import org.yaml.snakeyaml.Yaml;

import com.weibo.vintage.model.StaticsConfigMap;

import cn.sina.api.commons.util.ApiLogger;


/**
 * Package: com.weibo.api.captain.core.model
 *
 * File: CacheAgentConfig.java
 *
 * <pre>
 *     对应cacheAgent的config配置pool中的各个元素，为保持统一，
 * </pre>
 *
 * Author: fishermen
 *
 * Copyright @ 2015 Corpration Name
 *
 */
public class CacheAgentConfig {

	private static final String SEMICOLON = ":";
	private static final String SEMICOLON_FOR_VAL = ": ";
	private static final String NEW_LINE = "\n";
	private static final String INDENTATION_SUB_ITEM = "  ";
	private static final String INDENTATION_SUB_EMPTY_LIST = " -";
	private static final String INDENTATION_SUB_LIST = " - ";

	private String configGroupId;
	private String configKey;

	private CacheAgentGlobalConfig globalConfig;
	private List<CacheAgentGroupConfig> groupConfs = new ArrayList<CacheAgentGroupConfig>();


	public CacheAgentGlobalConfig getGlobalConfig() {
		return globalConfig;
	}

	public void setGlobalConfig(CacheAgentGlobalConfig globalConfig) {
		this.globalConfig = globalConfig;
	}

	public List<CacheAgentGroupConfig> getGroupConfs() {
		return groupConfs;
	}

	public void setGroupConfs(List<CacheAgentGroupConfig> groupConfs) {
		this.groupConfs = groupConfs;
	}

	/**
	 * return null if parse failure
	 * @param configStr
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static CacheAgentConfig parseStr(String configStr) {
		if(StringUtils.isBlank(configStr)) {
			ApiLogger.warn(String.format("parseStr false for configStr is blank : %s", configStr));
			return null;
		}
		Yaml yaml = new Yaml();

		Map<String, Map<String, Object>> props = null;

		try {
			props = (Map<String, Map<String, Object>>)yaml.load(configStr);
		} catch (Exception e) {
			ApiLogger.warn(String.format("parseStr false when yaml.load configStr [%s]", configStr), e);
			return null;
		}

		CacheAgentConfig conf = new CacheAgentConfig();
		for(Map.Entry<String, Map<String, Object>> entry :props.entrySet()) {
			if("global".equals(entry.getKey())) {
				Map<String, Object> globalPros = entry.getValue();
				conf.globalConfig = new CacheAgentGlobalConfig();
				conf.globalConfig.setLruMaxMemory(extractLongVal(globalPros, CacheAgentConfigElement.lru_max_memory));
				conf.globalConfig.setGetSignTime(extractIntegerVal(globalPros, CacheAgentConfigElement.get_sign_time));
				conf.globalConfig.setHeartBeatTime(extractIntegerVal(globalPros, CacheAgentConfigElement.heart_beat_time));
				conf.globalConfig.setSign(extractStringVal(globalPros, CacheAgentConfigElement.sign));

			} else {
				Map<String, Object> groupPros = entry.getValue();
				List<CacheAgentGroupConfig> groupConfs = conf.getGroupConfs();

				/** hash_tag 绝对不能为null */
				String groupName = (String)groupPros.get(CacheAgentConfigElement.hash_tag.name());
				if(groupName == null){
					ApiLogger.warn(String.format("malformed config for missing hash_tag, config:%s", configStr));
					return null;
				}
				CacheAgentGroupConfig gconf = new CacheAgentGroupConfig();
				groupConfs.add(gconf);

				gconf.setName(groupName);
				gconf.setHash(extractStringVal(groupPros, CacheAgentConfigElement.hash));
				gconf.setDistribution(extractStringVal(groupPros, CacheAgentConfigElement.distribution));
				gconf.setHashTag(groupName);
				gconf.setSaveTag(extractBooleanVal(groupPros, CacheAgentConfigElement.save_tag));
				gconf.setAutoEjecThosts(extractBooleanVal(groupPros, CacheAgentConfigElement.auto_eject_hosts));
				gconf.setTimeout(extractLongVal(groupPros, CacheAgentConfigElement.timeout));
				gconf.setLruTimeout(extractLongVal(groupPros, CacheAgentConfigElement.lru_timeout));
				gconf.setRedis(extractBooleanVal(groupPros, CacheAgentConfigElement.redis));
				gconf.setServerRetryTimeout(extractLongVal(groupPros, CacheAgentConfigElement.server_retry_timeout));
				gconf.setServerFailureLimit(extractIntegerVal(groupPros, CacheAgentConfigElement.server_failure_limit));
				gconf.setPartialReply(extractBooleanVal(groupPros, CacheAgentConfigElement.partial_reply));
				gconf.setExptime(extractLongVal(groupPros, CacheAgentConfigElement.exptime));
                Boolean forceWriteAll = extractBooleanVal(groupPros, CacheAgentConfigElement.forceWriteAll);
				Boolean updateSlaveL1 = extractBooleanVal(groupPros, CacheAgentConfigElement.updateSlaveL1);
				Boolean localAffinity = extractBooleanVal(groupPros, CacheAgentConfigElement.localAffinity);
				Integer flag = extractIntegerVal(groupPros, CacheAgentConfigElement.flag);
                if(!CaptainUtil.ignoreNewField()) {
					boolean forceFlag = forceWriteAll == null ? false : forceWriteAll;
					boolean slaveFlag = updateSlaveL1 == null ? true : updateSlaveL1;
					boolean locationFlag = localAffinity == null ? false : localAffinity;
					flag = flag == null ? 0 : flag;
					if(!CaptainUtil.ignoreFalseField()){
						gconf.setForceWriteAll(forceFlag);
					}else{
						if(forceFlag){
							gconf.setForceWriteAll(forceFlag);
						}
					}
					if(!CaptainUtil.ignoreTrueField()){
						gconf.setUpdateSlaveL1(slaveFlag);
					}else{
						if (!slaveFlag) {
							gconf.setUpdateSlaveL1(slaveFlag);
						}
					}
					if(!CaptainUtil.ignoreLocationFalseField()){
						gconf.setlocalAffinity(localAffinity);
					}else{
						if (locationFlag) {
							gconf.setlocalAffinity(localAffinity);
						}
					}
					if(!CaptainUtil.ignoreFlagZeroField()){
						gconf.setFlag(flag);
					}else{
						if (flag > 0) {
							gconf.setFlag(flag);
						}
					}
                }
				gconf.setMaster(extractListOfStr(groupPros, CacheAgentConfigElement.master));
				gconf.setMasterL1(extractListOfListStr(groupPros, CacheAgentConfigElement.master_l1));
				gconf.setSlave(extractListOfStr(groupPros, CacheAgentConfigElement.slave));
				gconf.setSlaveL1(extractListOfListStr(groupPros, CacheAgentConfigElement.slave_l1));
				gconf.setSsdcache(extractListOfListStr(groupPros, CacheAgentConfigElement.ssdcache));

			}
		}

		return conf;
	}

	/**
	 * validate configstr, return false if parse or check the configStr faise;
	 * @param configStr
	 * @return
	 */
	public static boolean validateStr(String configStr) {
		CacheAgentConfig config = parseStr(configStr);
		if(config == null || config.getGroupConfs().size() < 1) {
			ApiLogger.warn(String.format("Found invalid config for parse false, configStr: %s", configStr));
			return false;
		}
		// check groups: 1 必须要有master; 2 如果 master_effective_time 大于当前时间，则必须要有master_elapse
		for(CacheAgentGroupConfig gcfg : config.getGroupConfs()) {

			if(gcfg.getMaster() == null || gcfg.getMaster().size() < 1) {
				ApiLogger.warn(String.format("Found invalid config for has no master, configStr: %s", configStr));
				return false;
			}

			boolean missMaster = (gcfg.getMasterEffectiveTime() == null || gcfg.getMasterEffectiveTime() <= System.currentTimeMillis())
					&& (gcfg.getMaster() == null || gcfg.getMaster().size() == 0);
			boolean missMasterElaspse = (gcfg.getMasterEffectiveTime() != null && gcfg.getMasterEffectiveTime() > System.currentTimeMillis())
					&& (gcfg.getMasterElapse() == null || gcfg.getMasterElapse().size() == 0);
			if(missMaster || missMasterElaspse) {
				return false;
			}
		}

		//check toYaml
		String yamlStr = null;
		try {
			yamlStr = config.toYamlStr();
		} catch (Exception e) {
			ApiLogger.warn("validate configStr false for config:" + configStr, e);
			return false;
		}

		return yamlStr != null;
	}

	/**
	 * 构建yaml配置，因为group name不定，所以此处手动构建，跟进snakeyaml新版能否支持这种场景？
	 * @return
	 */
	public String toYamlStr() {
		StringBuilder sb = new StringBuilder(256);
		sb.append(CacheAgentConfigElement.global.name()).append(SEMICOLON).append(NEW_LINE);
		appendSubItem(sb, CacheAgentConfigElement.lru_max_memory, globalConfig.getLruMaxMemory());
		appendSubItem(sb, CacheAgentConfigElement.get_sign_time, globalConfig.getGetSignTime());
		appendSubItem(sb, CacheAgentConfigElement.heart_beat_time, globalConfig.getHeartBeatTime());
		appendSubItem(sb, CacheAgentConfigElement.sign, globalConfig.getSign());

		if(groupConfs == null || groupConfs.size() == 0){
			return sb.toString();
		}

		for(CacheAgentGroupConfig gconf : groupConfs) {
			sb.append(NEW_LINE);
			sb.append(gconf.getName()).append(SEMICOLON).append(NEW_LINE);
			appendSubItem(sb, CacheAgentConfigElement.hash, gconf.getHash());
			appendSubItem(sb, CacheAgentConfigElement.distribution, gconf.getDistribution());
			appendSubItem(sb, CacheAgentConfigElement.hash_tag, gconf.getHashTag());
			appendSubItem(sb, CacheAgentConfigElement.save_tag, gconf.getSaveTag());
			appendSubItem(sb, CacheAgentConfigElement.auto_eject_hosts, gconf.getAutoEjecThosts());
			appendSubItem(sb, CacheAgentConfigElement.timeout, gconf.getTimeout());
			appendSubItem(sb, CacheAgentConfigElement.master_effective_time, gconf.getMasterEffectiveTime());
			appendSubItem(sb, CacheAgentConfigElement.lru_timeout, gconf.getLruTimeout());
			appendSubItem(sb, CacheAgentConfigElement.redis, gconf.getRedis());
			appendSubItem(sb, CacheAgentConfigElement.server_retry_timeout, gconf.getServerRetryTimeout());
			appendSubItem(sb, CacheAgentConfigElement.server_failure_limit, gconf.getServerFailureLimit());
			appendSubItem(sb, CacheAgentConfigElement.partial_reply, gconf.getPartialReply());
			appendSubItem(sb, CacheAgentConfigElement.exptime, gconf.getExptime());

			if(gconf.getMaster() != null && gconf.getMaster().size() > 0) {
				sb.append(INDENTATION_SUB_ITEM).append(CacheAgentConfigElement.master).append(SEMICOLON).append(NEW_LINE);
				appendListStr(sb, 2, gconf.getMaster());
			}
			if(gconf.getMasterElapse() != null && gconf.getMasterElapse().size() > 0){
				sb.append(INDENTATION_SUB_ITEM).append(CacheAgentConfigElement.master_elapse).append(SEMICOLON).append(NEW_LINE);
				appendListStr(sb, 2, gconf.getMasterElapse());
			}
			if(gconf.getMasterL1() != null && gconf.getMasterL1().size() > 0){
				sb.append(INDENTATION_SUB_ITEM).append(CacheAgentConfigElement.master_l1).append(SEMICOLON).append(NEW_LINE);
				appendListOfListStr(sb, 3, gconf.getMasterL1());
			}
			if(gconf.getSlave() != null && gconf.getSlave().size() > 0) {
				sb.append(INDENTATION_SUB_ITEM).append(CacheAgentConfigElement.slave).append(SEMICOLON).append(NEW_LINE);
				appendListStr(sb, 2, gconf.getSlave());
			}
			if(gconf.getSlaveL1() != null && gconf.getSlaveL1().size() > 0) {
				sb.append(INDENTATION_SUB_ITEM).append(CacheAgentConfigElement.slave_l1).append(SEMICOLON).append(NEW_LINE);
				appendListOfListStr(sb, 3, gconf.getSlaveL1());
			}
			if(gconf.getSsdcache() != null && gconf.getSsdcache().size() > 0) {
				sb.append(INDENTATION_SUB_ITEM).append(CacheAgentConfigElement.ssdcache).append(SEMICOLON).append(NEW_LINE);
				appendListOfListStr(sb, 3, gconf.getSsdcache());
			}

		}

		sb.append(NEW_LINE);
		return sb.toString();

	}

	/**
	 * return Map&ltgroupKey, CacheAgentConfig&gt
	 * @param sconfigMap
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, CacheAgentConfig> parseVintageStaticConfigMap(StaticsConfigMap sconfigMap) {
		if(sconfigMap == null || sconfigMap.getMaps() == null || sconfigMap.getMaps().size() == 0) {
			return Collections.EMPTY_MAP;
		}

		Map<String, CacheAgentConfig> configs = new HashMap<String, CacheAgentConfig>();
		for(Map.Entry<String, String> configEntry : sconfigMap.getMaps().entrySet()) {
			CacheAgentConfig config = CacheAgentConfig.parseStr(configEntry.getValue());
			if(config == null) {
				ApiLogger.warn(String.format("parse vintage staticConfigMap false for key:%s, staticConfigMap:%s", configEntry.getKey(), sconfigMap));
				continue;
			}
			config.setConfigGroupId(sconfigMap.getGroupId());
			config.setConfigKey(configEntry.getKey());

			configs.put(configEntry.getKey(), config);
		}
		return configs;
	}

	@Override
	public int hashCode() {
		return toYamlStr().hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		if(obj == null || !(obj instanceof CacheAgentConfig)) {
			return false;
		}

		return StringUtils.equals(this.toYamlStr(), ((CacheAgentConfig)obj).toYamlStr());
	}

	@Override
	public String toString() {
		return toYamlStr();
	}

	public String getConfigGroupId() {
		return configGroupId;
	}

	public void setConfigGroupId(String configGroupId) {
		this.configGroupId = configGroupId;
	}

	public String getConfigKey() {
		return configKey;
	}

	public void setConfigKey(String configKey) {
		this.configKey = configKey;
	}

	private void appendSubItem(StringBuilder sb, CacheAgentConfigElement cname, Object val) {
		if(val == null) {
			return;
		}
		sb.append(INDENTATION_SUB_ITEM)
		.append(cname.name()).append(SEMICOLON_FOR_VAL).append(val)
		.append(NEW_LINE);
	}

	private void appendListStr(StringBuilder sb, int level, List<String> list) {
		if(list == null || list.size() == 0){
			return;
		}
		for(String val : list) {
			for(int i = 1; i < level; i++){
				sb.append(INDENTATION_SUB_ITEM);
			}
			sb.append(INDENTATION_SUB_LIST).append(val).append(NEW_LINE);
		}
	}

	private void appendListOfListStr(StringBuilder sb, int level, List<List<String>> list) {
		if(list == null || list.size() == 0){
			return;
		}
		for(List<String> l : list) {
			if(l == null || l.size() == 0) {
				continue;
			}
			for(int i = 1; i < (level - 1); i++){
				sb.append(INDENTATION_SUB_ITEM);
			}
			sb.append(INDENTATION_SUB_EMPTY_LIST).append(NEW_LINE);
			appendListStr(sb, level, l);
		}
	}

	/**
	 * yaml中的obj可能是Intger、Long、null，所以此处单独判断
	 * @param obj
	 * @return
	 */
	private static Long parseYamlLong(Object obj) {
		if(obj == null){
			return null;

		} else if(obj instanceof Integer){
			return ((Integer)obj).longValue();

		} else if(obj instanceof Long){
			return ((Long)obj).longValue();

		} else{
			throw new IllegalArgumentException("malformed param, obj:" + obj);
		}
	}

	private static String extractStringVal(Map<String, Object> map, CacheAgentConfigElement gname) {
		return (String)map.get(gname.name());
	}

	private static Boolean extractBooleanVal(Map<String, Object> map, CacheAgentConfigElement gname) {
		Object obj = map.get(gname.name());
		if(obj != null && obj instanceof Boolean) {
			return (Boolean)obj;
		}
		return null;
	}

	private static Long extractLongVal(Map<String, Object> map, CacheAgentConfigElement gname) {
		return parseYamlLong(map.get(gname.name()));
	}

	private static Integer extractIntegerVal(Map<String, Object> map, CacheAgentConfigElement gname){
		return (Integer)map.get(gname.name());
	}

	@SuppressWarnings("unchecked")
	private static List<String> extractListOfStr(Map<String, Object> map, CacheAgentConfigElement gname) {
		return (List<String>)map.get(gname.name());
	}

	@SuppressWarnings("unchecked")
	private static List<List<String>> extractListOfListStr(Map<String, Object> map, CacheAgentConfigElement gname) {
		return (List<List<String>>)map.get(gname.name());
	}

	public static void main(String[] args) throws Exception{
		CacheAgentConfig conf = parseStr("/Users/<USER>/Desktop/nutcracker.yml");
		System.out.println("====" + conf);
		System.out.println(conf.toYamlStr());
		File file = new File("/Users/<USER>/Desktop/nutcracker-new.yml");
		FileWriter writer = new FileWriter(file);
		writer.write(conf.toYamlStr());
		writer.flush();
	}






}
