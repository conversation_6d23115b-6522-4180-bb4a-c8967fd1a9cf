package com.weibo.api.captain.core.model;
/**  
 * Package: com.weibo.api.captain.core.model  
 *  
 * File: CacheAgentConfigName.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public enum CacheAgentConfigElement {
	global,
	
	lru_max_memory,
	get_sign_time,
	heart_beat_time,
	sign,
	
	hash, 
	distribution, 
	hash_tag, save_tag, 
	auto_eject_hosts, 
	timeout,
	lru_timeout,
	redis, 
	server_retry_timeout, 
	server_failure_limit, 
	partial_reply, 
	exptime,
	forceWriteAll,
	updateSlaveL1,
	localAffinity,
	flag,
	master_effective_time, 
	master, 
	master_elapse, 
	master_l1, 
	slave, 
	slave_l1, 
	ssdcache;
}
