package com.weibo.api.captain.core.model;

import java.util.List;

import cn.sina.api.commons.util.JsonBuilder;

import com.weibo.api.captain.core.CaptainConstants;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.model  
 *  
 * File: SLAValue.java   
 *  
 * Author: fishermen    
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentSla implements JsonAble{
	
	private long tps;
	private long ctps;
	private long timeoutsPerSecond;
	private long avgTimeInMills;
	/** 命中率， 基数是100*/
	private int hitRate = 100;

	
	public long getTps() {
		return tps;
	}

	public void setTps(long tps) {
		this.tps = tps;
	}

	public long getCtps() {
		return ctps;
	}

	public void setCtps(long ctps) {
		this.ctps = ctps;
	}
	
	public long getTimeoutsPerSecond() {
		return timeoutsPerSecond;
	}

	public void setTimeoutsPerSecond(long timeoutsPerSecond) {
		this.timeoutsPerSecond = timeoutsPerSecond;
	}

	public long getAvgTimeInMills() {
		return avgTimeInMills;
	}

	public void setAvgTimeInMills(long avgTimeInMills) {
		this.avgTimeInMills = avgTimeInMills;
	}

	public int getHitRate() {
		return hitRate;
	}

	public void setHitRate(int hitRate) {
		this.hitRate = hitRate;
	}
	
	public static CacheAgentSla aggregateSla(List<CacheAgentSla> slas) {
		CacheAgentSla totalSla = new CacheAgentSla();
		if(slas == null || slas.size() == 0) {
			return totalSla;
		}
		
		for(CacheAgentSla sla : slas) {
			totalSla.tps += sla.getTps();
			totalSla.ctps += sla.getCtps();
			totalSla.avgTimeInMills += (sla.getAvgTimeInMills() / slas.size());
			totalSla.timeoutsPerSecond += sla.getTimeoutsPerSecond();
		}
		
		long totalHits = 0; 
		for(CacheAgentSla sla : slas) {
			totalHits += sla.getTps() * sla.getHitRate();
		}
		
		if(totalSla.tps > 0) {
			totalSla.hitRate = (int)(totalHits / totalSla.tps);
		} else {
			totalSla.hitRate = 100;
		}
		
		return totalSla;
	}
	
	@Override
	public int hashCode() {
		int hash = 0;
		
		hash += hash * CaptainConstants.HASH_FACTOR + tps;
		hash += hash * CaptainConstants.HASH_FACTOR + ctps;
		hash += hash * CaptainConstants.HASH_FACTOR + timeoutsPerSecond;
		hash += hash * CaptainConstants.HASH_FACTOR + avgTimeInMills;
		hash += hash * CaptainConstants.HASH_FACTOR + hitRate;
		
		return hash;
	}
	
	@Override
	public boolean equals(Object obj) {
		if(!(obj instanceof CacheAgentSla)) {
			return false;
		}
		
		CacheAgentSla slaObj = (CacheAgentSla)obj;
		return this.tps == slaObj.tps
				&& this.ctps == slaObj.ctps
				&& this.timeoutsPerSecond == slaObj.timeoutsPerSecond
				&& this.avgTimeInMills == slaObj.avgTimeInMills
				&& this.hitRate == slaObj.hitRate;
		
	}
	
	@Override
	public String toString(){
		return toJson();
	}
	
	@Override
	public JsonBuilder toJsonBuilder() {
		return toJsonBuilder(null);
	}
	
	public JsonBuilder toJsonBuilder(EndpointAddress addr) {
		JsonBuilder jb = new JsonBuilder();
		if(addr != null) {
			jb.append("host", addr.getHost());
			jb.append("port", addr.getPort());
		}
		jb.append("tps",tps);
		jb.append("ctps", ctps);
		jb.append("timeouts_per_second", timeoutsPerSecond);
		jb.append("average_time", avgTimeInMills);
		jb.append("hit_rate", hitRate);
		
		jb.flip();
		return jb;
	}
	
	@Override
	public String toJson() {
		return toJsonBuilder().toString();
	}
	
}
