package com.weibo.api.captain.core.model;

import java.util.List;

import cn.sina.api.commons.util.JsonBuilder;

import com.weibo.api.captain.core.CaptainConstants;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.model  
 *  
 * File: SLAValue.java   
 *  
 * Author: fishermen    
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class MemcacheSla implements JsonAble{
	
	private long tps;
	private int hitRate = 100;
	private long inFlow;
	private long outFlow;
	private long evictionPerSecond;
	
	public long getTps() {
		return tps;
	}

	public void setTps(long tps) {
		this.tps = tps;
	}

	public int getHitRate() {
		return hitRate;
	}

	public void setHitRate(int hitRate) {
		this.hitRate = hitRate;
	}

	public long getInFlow() {
		return inFlow;
	}

	public void setInFlow(long inFlow) {
		this.inFlow = inFlow;
	}

	public long getOutFlow() {
		return outFlow;
	}

	public void setOutFlow(long outFlow) {
		this.outFlow = outFlow;
	}

	public long getEvictionPerSecond() {
		return evictionPerSecond;
	}

	public void setEvictionPerSecond(long evictionPerSecond) {
		this.evictionPerSecond = evictionPerSecond;
	}

	public static MemcacheSla aggregateSla(List<MemcacheSla> slas) {
		MemcacheSla totalSla = new MemcacheSla();
		if(slas == null || slas.size() == 0) {
			return totalSla;
		}
		
		for(MemcacheSla sla : slas) {
			totalSla.tps += sla.getTps();
			totalSla.inFlow += sla.inFlow;
			totalSla.outFlow += sla.outFlow;
			totalSla.evictionPerSecond += sla.evictionPerSecond;
		}
		
		long totalHits = 0;
		for(MemcacheSla sla : slas) {
			totalHits += sla.getTps() * sla.getHitRate();
		}
		
		if(totalSla.tps > 0) {
			totalSla.hitRate = (int)(totalHits / totalSla.tps);
		} else {
			totalSla.hitRate = 100;
		}
		
		return totalSla;
	}
	
	@Override
	public int hashCode() {
		int hash = 0;
		
		hash += hash * CaptainConstants.HASH_FACTOR + tps;
		hash += hash * CaptainConstants.HASH_FACTOR + hitRate;
		hash += hash * CaptainConstants.HASH_FACTOR + inFlow;
		hash += hash * CaptainConstants.HASH_FACTOR + outFlow;
		hash += hash * CaptainConstants.HASH_FACTOR + evictionPerSecond;
		
		return hash;
	}
	
	@Override
	public boolean equals(Object obj) {
		if(!(obj instanceof MemcacheSla)) {
			return false;
		}
		
		MemcacheSla slaObj = (MemcacheSla)obj;
		return this.tps == slaObj.tps
				&& this.hitRate == slaObj.hitRate
				&& this.inFlow == slaObj.inFlow
				&& this.outFlow == slaObj.outFlow
				&& this.evictionPerSecond == slaObj.evictionPerSecond;
		
	}
	
	@Override
	public String toString(){
		return toJson();
	}
	
	@Override
	public JsonBuilder toJsonBuilder() {
		return toJsonBuilder(null);
	}
	
	public JsonBuilder toJsonBuilder(EndpointAddress addr) {
		JsonBuilder jb = new JsonBuilder();
		if(addr != null) {
			jb.append("host", addr.getHost());
			jb.append("port", addr.getPort());
		}
		jb.append("tps",tps);
		jb.append("hit_rate", hitRate);
		jb.append("in_flow", inFlow);
		jb.append("out_flow", outFlow);
		jb.append("eviction_per_second", evictionPerSecond);
		
		jb.flip();
		return jb;
	}
	
	@Override
	public String toJson() {
		return toJsonBuilder().toString();
	}
	
}
