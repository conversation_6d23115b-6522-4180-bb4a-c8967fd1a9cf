package com.weibo.api.captain.core.model;

import cn.sina.api.commons.util.JsonBuilder;

/**
 * Package: com.weibo.api.captain.core
 * 
 * File: MemcacheSlaThreshold.java
 * 
 * Author: fishermen 
 * 
 * Copyright @ 2015 Corpration Name
 * 
 */
public class MemcacheSlaThreshold implements JsonAble{

	/** tps默认上限10w */
	private int tps = 80000;
	
	/** 命中率 */
	private int hitRate = 80;
	
	/** mc读取的io流，单位byte */
	private long inFlow = 100*1024*1024;
	
	/** mc写出的io流，单位byte */
	private long outFlow = 1024*1024*1024;
	
	/** 每秒剔除数 */
	private long evictionPerSecond = 1000;
	
	public int getTps() {
		return tps;
	}

	public void setTps(int tps) {
		this.tps = tps;
	}

	public int getHitRate() {
		return hitRate;
	}

	public void setHitRate(int hitRate) {
		this.hitRate = hitRate;
	}

	public long getInFlow() {
		return inFlow;
	}

	public void setInFlow(long inFlow) {
		this.inFlow = inFlow;
	}

	public long getOutFlow() {
		return outFlow;
	}

	public void setOutFlow(long outFlow) {
		this.outFlow = outFlow;
	}

	public long getEvictionPerSecond() {
		return evictionPerSecond;
	}

	public void setEvictionPerSecond(long evictionPerSecond) {
		this.evictionPerSecond = evictionPerSecond;
	}

	@Override
	public int hashCode() {
		int factor = 31;
		int hash = 0;
		
		hash += hash * factor + tps;
		hash += hash * 31 + hitRate;
		hash += hash * 31 + inFlow;
		hash += hash * 31 + outFlow;
		hash += hash * 31 + evictionPerSecond;
		
		return hash;
	}
	
	@Override
	public boolean equals(Object obj) {
		if(!(obj instanceof MemcacheSlaThreshold)) {
			return false;
		}
		
		MemcacheSlaThreshold mcObj = (MemcacheSlaThreshold) obj;
		return this.tps == mcObj.tps
				&& this.hitRate == mcObj.hitRate
				&& this.tps == mcObj.tps
				&& this.inFlow == mcObj.inFlow
				&& this.outFlow == mcObj.outFlow
				&& this.evictionPerSecond == mcObj.evictionPerSecond;
	}
	
	@Override
	public JsonBuilder toJsonBuilder() {
		JsonBuilder jb = new JsonBuilder();
		jb.append("tps", tps);
		jb.append("hit_rate", hitRate);
		jb.append("in_flow", inFlow);
		jb.append("out_flow", outFlow);
		jb.append("eviction_per_second", evictionPerSecond);
		
		jb.flip();
		return jb;
	}
	
	@Override
	public String toJson() {
		return toJsonBuilder().toString();
	}
	
	@Override
	public String toString() {
		return toJson();
	}
}
