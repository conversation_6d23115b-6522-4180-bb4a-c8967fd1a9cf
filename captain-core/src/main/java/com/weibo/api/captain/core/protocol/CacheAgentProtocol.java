package com.weibo.api.captain.core.protocol;

import java.util.Map;

import org.apache.commons.lang.StringUtils;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.Util;

import com.weibo.api.captain.core.model.CacheAgentBizStat;
import com.weibo.api.captain.core.model.CacheAgentStat;


/**
 * Package: com.weibo.api.captain.core.common.proto
 * 
 * File: CacheAgentParser.java
 * 
 * <pre>
 * 根据协议文档解析cacheAgent个各种命令的响应结果
 * </pre>
 * 
 * Author: fishermen 
 * 
 * Copyright @ 2015 Corpration Name
 * 
 */
public class CacheAgentProtocol {

	/** ================ cacheService stat ================ */
	private static final String STAT_TAG_VERSION = "version";
	private static final String STAT_TAG_CONFIG_GROUP = "conf_group";
	private static final String STAT_TAG_CONFIG_KEY = "conf_key";
	private static final String STAT_TAG_SIGN = "sign";
	
	private static final String STAT_TAG_UPTIME = "uptime_in_seconds";
	private static final String STAT_TAG_CONNECTIONS = "connected_clients";
	private static final String STAT_TAG_CREQUEST_TOTAL = "crequest_total";
	
	private static final String STAT_TAG_CMD_TOTAL = "commands_total";
	private static final String STAT_TAG_CMD_GET = "commands_get";
	private static final String STAT_TAG_CMD_GETS = "commands_gets";
	private static final String STAT_TAG_CMD_SET = "commands_set";
	private static final String STAT_TAG_CMD_CAS = "commands_cas";
	private static final String STAT_TAG_CMD_HIT = "commands_hit";
	private static final String STAT_TAG_CMD_READ = "commands_read";
	private static final String STAT_TAG_CMD_WRITE = "commands_write";
	private static final String STAT_TAG_CMD_DELETE = "commands_delete";
	private static final String STAT_TAG_CMD_TOTAL_TIME = "commands_total_time";
	private static final String STAT_TAG_CMD_TOTAL_TIMEOUT = "total_server_timedout";
	
	private static final String STAT_TAG_CMD_TPS = "commands_tps";
	private static final String STAT_TAG_CLIENT_REQUEST_TPS = "crequest_tps";
	private static final String STAT_TAG_CMD_HIT_RATE = "commands_hit_rate";
	private static final String STAT_TAG_CMD_AVARAGE_TIME = "commands_average_time";
	
	
	/** ================ cacheService sub-biz (cluster) stat ================ */
	
	private static final String CLUSTER_STAT_TAG_TIMESTAMP = "timestamp";
	private static final String CLUSTER_STAT_TAG_CREQUEST_TOTAL = "_crequest";
	private static final String CLUSTER_STAT_TAG_CMD_TOTAL = "_command";
	private static final String CLUSTER_STAT_TAG_CMD_READ = "_read";
	private static final String CLUSTER_STAT_TAG_CMD_HIT = "_hit";
	private static final String CLUSTER_STAT_TAG_CMD_TOTAL_TIME = "_total_time";
	private static final String CLUSTER_STAT_TAG_TIMEOUT = "_timedout";
	
	private static final String CLUSTER_STAT_TAG_CLIENT_REQUEST_TPS = "_ctps";
	private static final String CLUSTER_STAT_TAG_TPS = "_tps";
	private static final String CLUSTER_STAT_TAG_HIR_RATE = "_hit_rate";
	private static final String CLUSTER_STAT_TAG_AVERATE_TIME = "_average_time";
	
	
	/**
	 * 解析对agent发出 stats后的响应结果，包括agent的各种总体统计
	 * @param stat
	 * @param rsp
	 */
	public static void parseAgentStat(CacheAgentStat stat, Map<String, String> rsp) {
		
		if (stat == null || rsp.size() == 0) {
			ApiLogger.warn(String.format("parse agent stat false, for malformed value, stat = %s, rsp = %s", stat, rsp));
			return;
		}
		
		if(rsp.containsKey(STAT_TAG_VERSION)) {
			stat.setVersion(rsp.get(STAT_TAG_VERSION));
		}
		
		if(rsp.containsKey(STAT_TAG_CONFIG_GROUP)) {
			stat.setConfigGroup(rsp.get(STAT_TAG_CONFIG_GROUP));
		}
		
		if(rsp.containsKey(STAT_TAG_CONFIG_KEY)) {
			stat.setConfigKey(rsp.get(STAT_TAG_CONFIG_KEY));
		}
		
		if(rsp.containsKey(STAT_TAG_SIGN)) {
			stat.setSign(rsp.get(STAT_TAG_SIGN));
		}
		
		if(rsp.containsKey(STAT_TAG_UPTIME)) {
			stat.setUptime(Util.convertLong(rsp.get(STAT_TAG_UPTIME)));
		}
		
		if(rsp.containsKey(STAT_TAG_CONNECTIONS)) {
			stat.setConnections(Util.convertInt(rsp.get(STAT_TAG_CONNECTIONS)));
		}
		
		if(rsp.containsKey(STAT_TAG_CREQUEST_TOTAL)) {
			stat.setCrequestTotal(Util.convertLong(rsp.get(STAT_TAG_CREQUEST_TOTAL)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_TOTAL)) {
			stat.setCmdTotal(Util.convertLong(rsp.get(STAT_TAG_CMD_TOTAL)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_GET)) {
			stat.setCmdGet(Util.convertLong(rsp.get(STAT_TAG_CMD_GET)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_GETS)) {
			stat.setCmdGets(Util.convertLong(rsp.get(STAT_TAG_CMD_GETS)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_SET)) {
			stat.setCmdSet(Util.convertLong(rsp.get(STAT_TAG_CMD_SET)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_CAS)) {
			stat.setCmdCas(Util.convertLong(rsp.get(STAT_TAG_CMD_CAS)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_HIT)) {
			stat.setCmdHit(Util.convertLong(rsp.get(STAT_TAG_CMD_HIT)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_READ)) {
			stat.setCmdRead(Util.convertLong(rsp.get(STAT_TAG_CMD_READ)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_WRITE)) {
			stat.setCmdWrite(Util.convertLong(rsp.get(STAT_TAG_CMD_WRITE)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_DELETE)) {
			stat.setCmdDelete(Util.convertLong(rsp.get(STAT_TAG_CMD_DELETE)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_TOTAL_TIME)) {
			stat.setCmdTotalTime(Util.convertLong(rsp.get(STAT_TAG_CMD_TOTAL_TIME)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_TOTAL_TIMEOUT)) {
			stat.setTotalTimedout(Util.convertLong(rsp.get(STAT_TAG_CMD_TOTAL_TIMEOUT)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_TPS)) {
			stat.getSla().setTps(Util.convertInt(rsp.get(STAT_TAG_CMD_TPS)));
		}
		
		if(rsp.containsKey(STAT_TAG_CLIENT_REQUEST_TPS)) {
			stat.getSla().setCtps(Util.convertInt(rsp.get(STAT_TAG_CLIENT_REQUEST_TPS)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_HIT_RATE)) {
			stat.getSla().setHitRate(Util.convertInt(rsp.get(STAT_TAG_CMD_HIT_RATE)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_AVARAGE_TIME)) {
			stat.getSla().setAvgTimeInMills(Util.convertInt(rsp.get(STAT_TAG_CMD_AVARAGE_TIME)));
		}
		
		
			
		
	}
	
	/**
	 * 解析agent中对某个cluster的统计响应
	 * @param stat
	 * @param clusterName
	 * @param rsp
	 */
	public static void parseAgentClusterStat(CacheAgentStat stat, String clusterName, Map<String, String> rsp) {
		if(stat == null || StringUtils.isBlank(clusterName) || rsp == null || rsp.size() == 0) {
			ApiLogger.warn(String.format("parse agent cluster stat false, for malformed value, stat = %s, clusterName = %s, rsp = %s", stat, clusterName, rsp));
			return;
		}
		
		CacheAgentBizStat bizStat = new CacheAgentBizStat(stat);
		stat.getBizStats().put(clusterName, bizStat);
		
		String prefix = "cluster_" + clusterName;
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_TIMESTAMP))) {
			bizStat.setTimestamp(Util.convertLong(rsp.get(CLUSTER_STAT_TAG_TIMESTAMP)));
		}    
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_CREQUEST_TOTAL))) {
			bizStat.setCrequestTotal(Util.convertLong(rsp.get(CLUSTER_STAT_TAG_CREQUEST_TOTAL)));
		}    
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_CMD_TOTAL))) {
			bizStat.setCmdTotal(Util.convertLong(rsp.get(CLUSTER_STAT_TAG_CMD_TOTAL)));
		}    
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_CMD_READ))) {
			bizStat.setCmdRead(Util.convertLong(rsp.get(CLUSTER_STAT_TAG_CMD_READ)));
		}
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_CMD_HIT))) {
			bizStat.setCmdHit(Util.convertLong(rsp.get(CLUSTER_STAT_TAG_CMD_HIT)));
		}
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_CMD_TOTAL_TIME))) {
			bizStat.setCmdTotalTime(Util.convertLong(rsp.get(CLUSTER_STAT_TAG_CMD_TOTAL_TIME)));
		}
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_TIMEOUT))) {
			bizStat.setCmdTimeouts(Util.convertLong(rsp.get(CLUSTER_STAT_TAG_TIMEOUT)));
		}
		
		/** ========== pre-sla ========== */
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_TPS))) {
			bizStat.getSla().setTps(Util.convertInt(rsp.get(CLUSTER_STAT_TAG_TPS)));
		}
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_CLIENT_REQUEST_TPS))) {
			bizStat.getSla().setCtps(Util.convertInt(rsp.get(CLUSTER_STAT_TAG_CLIENT_REQUEST_TPS)));
		}
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_HIR_RATE))) {
			bizStat.getSla().setHitRate(Util.convertInt(rsp.get(CLUSTER_STAT_TAG_HIR_RATE)));
		}
		
		if(rsp.containsKey(getClusterStatKey(prefix, CLUSTER_STAT_TAG_AVERATE_TIME))) {
			bizStat.getSla().setAvgTimeInMills(Util.convertInt(rsp.get(CLUSTER_STAT_TAG_AVERATE_TIME)));
		}
		
		
		
	}
	
	private static String getClusterStatKey(String prefix, String suffix) {
		return prefix + suffix;
	}
	
	
}
