package com.weibo.api.captain.core.protocol;

import java.util.Map;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.Util;

import com.weibo.api.captain.core.model.MemcacheNodeStat;

/**  
 * Package: com.weibo.api.captain.core.common.proto  
 *  
 * File: MemcachedProto.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class MemcachedProtocol {

	public static final String CMD_MEMCACHE_STAT = "printf \"stats\r\n\" | nc {0} {1}";
	
	private static final String STAT_TAG_UPTIME = "uptime";
	private static final String STAT_TAG_CURR_ITEMS = "curr_items";
	private static final String STAT_TAG_BYTES = "bytes";
	private static final String STAT_TAG_CURR_CONNECTIONS = "curr_connections";
	private static final String STAT_TAG_CMD_GET = "cmd_get";
	private static final String STAT_TAG_CMD_SET = "cmd_set";
	private static final String STAT_TAG_GET_HITS = "get_hits";
	private static final String STAT_TAG_EVICTIONS = "evictions";
	private static final String STAT_TAG_BYTES_READ = "bytes_read";
	private static final String STAT_TAG_BYTES_WRITTEN = "bytes_written";
	private static final String STAT_TAG_LIMIT_MAXBYTES = "limit_maxbytes";
	
	
	public static MemcacheNodeStat parseMcStat(MemcacheNodeStat stat, Map<String, String> rsp) {
		if (stat == null || rsp == null || rsp.size() == 0) {
			ApiLogger.warn(String.format("parse memcached stat false, for malformed value, stat = %s, rsp = %s", stat, rsp));
			return null;
		}

		if(rsp.containsKey(STAT_TAG_UPTIME)) {
			stat.setUptime(Util.convertLong(rsp.get(STAT_TAG_UPTIME)));
		}
		
		if(rsp.containsKey(STAT_TAG_CURR_ITEMS)) {
			stat.setCurrItems(Util.convertLong(rsp.get(STAT_TAG_CURR_ITEMS)));
			
		}
		
		if(rsp.containsKey(STAT_TAG_BYTES)) {
			stat.setBytes(Util.convertLong(rsp.get(STAT_TAG_BYTES)));
		}
		
		if(rsp.containsKey(STAT_TAG_CURR_CONNECTIONS)) {
			stat.setCurrConnections(Util.convertInt(rsp.get(STAT_TAG_CURR_CONNECTIONS)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_GET)) {
			stat.setCmdGet(Util.convertLong(rsp.get(STAT_TAG_CMD_GET)));
		}
		
		if(rsp.containsKey(STAT_TAG_CMD_SET)) {
			stat.setCmdSet(Util.convertLong(rsp.get(STAT_TAG_CMD_SET)));
		}
		
		if(rsp.containsKey(STAT_TAG_GET_HITS)) {
			stat.setGetHits(Util.convertLong(rsp.get(STAT_TAG_GET_HITS)));
		}
		
		if(rsp.containsKey(STAT_TAG_EVICTIONS)) {
			stat.setEvictions(Util.convertLong(rsp.get(STAT_TAG_EVICTIONS)));
		}
		
		if(rsp.containsKey(STAT_TAG_BYTES_READ)) {
			stat.setBytesRead(Util.convertLong(rsp.get(STAT_TAG_BYTES_READ)));
		}
		
		if(rsp.containsKey(STAT_TAG_BYTES_WRITTEN)) {
			stat.setBytesWritten(Util.convertLong(rsp.get(STAT_TAG_BYTES_WRITTEN)));
		}
		
		if(rsp.containsKey(STAT_TAG_LIMIT_MAXBYTES)) {
			stat.setLimitMaxBytes(Util.convertLong(rsp.get(STAT_TAG_LIMIT_MAXBYTES)));
		}
		
		return stat;
	} 
}
