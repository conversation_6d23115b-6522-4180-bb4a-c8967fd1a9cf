package com.weibo.api.captain.core.resource;

import java.util.List;

import javax.annotation.Resource;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;

import org.apache.commons.lang.StringUtils;

import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.service.CacheAgentService;
import com.weibo.api.captain.core.service.CacheSlaService;
import com.weibo.api.captain.core.util.CaptainJsonUtil;
import com.weibo.api.captain.core.util.CaptainHttpUtil;
import com.weibo.vintage.model.EndpointAddress;


/**  
 * Package: com.weibo.api.captain.core.resource  
 *  
 * File: CacheAgentResource.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
@Path("agent")
public class CacheAgentResource {
	
	@Resource(name="cacheAgentService")
	private CacheAgentService cacheAgentService;
	
	@Resource(name="cacheSlaService")
	private CacheSlaService cacheSlaService;

	@Path("list")
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	public String getCacheAgentDepyloyList(
			@QueryParam("service") String serviceName,
			@QueryParam("cluster") String clusterName
			) {
		if(StringUtils.isBlank(serviceName)) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		if(!GlobalInstance.serviceIds.contains(serviceName)) {
			return CaptainJsonUtil.JSON_RS_UNSUPPORT_PARAM_CONTENT;
		}
		
		return cacheAgentService.getCacheAgentDeploy(serviceName, clusterName);
		
	}
	
	@Path("check_consistence")
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	/**
	 * <pre>
	 * 检查某个服务的cacheAgent，查看其config一致性，通过sign来判断；
	 * 流程：1 获取服务的集群列表； 
	 * 		2 解析集群列表获得host(ip:port); 
	 * 		3 对比所有host的config sign
	 * 查看某个服务的集群列表：
	 * curl "http://ip:port/naming/service?action=lookup&service=yf-rpc-test&cluster=user-pool1"
	 * </pre> 
	 * @return
	 */
	public String getNotConsistentCluster(
			@QueryParam("service") String serviceName,
			@QueryParam("cluster") String clusterName
			){
		if(StringUtils.isBlank(serviceName)) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		if(!GlobalInstance.serviceIds.contains(serviceName)) {
			return CaptainJsonUtil.JSON_RS_UNSUPPORT_PARAM_CONTENT;
		}
		
		return cacheAgentService.getNotConsistentAgents(serviceName, clusterName);
	}
	
	/**
	 * 返回cacheAgent状态异常的实例，状态异常包括：不可用，SLA指标异常，统计信息异常等
	 * @param serviceName
	 * @param clusterName
	 * @return
	 */
	@Path("check_sla")
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	public String getSlaAbnormalAgents(
			@QueryParam("service") String serviceName,
			@QueryParam("cluster") String clusterName
			){
		if(StringUtils.isBlank(serviceName)) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		if(!GlobalInstance.serviceIds.contains(serviceName)) {
			return CaptainJsonUtil.JSON_RS_UNSUPPORT_PARAM_CONTENT;
		}
		
		return cacheAgentService.getSlaAbormalServer(serviceName, clusterName);
	}
	
	@Path("stats")
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	/**
	 * 获取cache agent的服务状态
	 */
	public String getCacheAgentStats(
			@QueryParam("servers") String servers
			){
		
		List<EndpointAddress> addrs = CaptainHttpUtil.parseServers(servers);
		
		if(addrs == null || addrs.size() == 0) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		return cacheAgentService.getCacheAgentStats(addrs);
	}
	
	@Path("biz_sla")
	@Produces(MediaType.APPLICATION_JSON)
	@GET
	public String getBizSla(
			@QueryParam("service") String serviceName,
			@QueryParam("cluster") String clusterName,
			@QueryParam("sub_biz") String subBizName){
		
		if(StringUtils.isBlank(serviceName)) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		if(!GlobalInstance.serviceIds.contains(serviceName)) {
			return CaptainJsonUtil.JSON_RS_UNSUPPORT_PARAM_CONTENT;
		}
		
		return cacheSlaService.getBizSla(serviceName, clusterName, subBizName);
	}
	
	@Path("node_sla")
	@Produces(MediaType.APPLICATION_JSON)
	@GET
	public String getCacheAgentSla(
			@QueryParam("servers") String servers
			){
		
		List<EndpointAddress> addrs = CaptainHttpUtil.parseServers(servers);
		
		if(addrs == null || addrs.size() == 0) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		return cacheSlaService.getCacheAgentSla(addrs);
	}

	public CacheAgentService getCacheAgentService() {
		return cacheAgentService;
	}

	public void setCacheAgentService(CacheAgentService cacheAgentService) {
		this.cacheAgentService = cacheAgentService;
	}

	public CacheSlaService getCacheSlaService() {
		return cacheSlaService;
	}

	public void setCacheSlaService(CacheSlaService cacheSlaService) {
		this.cacheSlaService = cacheSlaService;
	}
	
	
}
