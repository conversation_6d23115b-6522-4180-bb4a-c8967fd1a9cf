package com.weibo.api.captain.core.resource;

import javax.annotation.Resource;
import javax.ws.rs.FormParam;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;

import org.apache.commons.lang.StringUtils;

import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.service.CacheAgentStaticConfigService;
import com.weibo.api.captain.core.util.CaptainJsonUtil;

/**  
 * Package: com.weibo.api.captain.core.resource  
 *  
 * File: StaticConfigResource.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
@Path("agent_config")
public class CacheAgentStaticConfigResource {

	@Resource(name="cacheAgentStaticConfigService")
	private CacheAgentStaticConfigService cacheAgentStaticConfigService;
	
	@Path("show")
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	public String getConfig(
			@QueryParam("group") String groupId, 
			@QueryParam("key") String key
			){
		if(StringUtils.isBlank(groupId) || StringUtils.isBlank(key)) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		if(!GlobalInstance.groupIds.contains(groupId)) {
			return CaptainJsonUtil.JSON_RS_UNSUPPORT_PARAM_CONTENT;
		}
		
		return cacheAgentStaticConfigService.getConfig(groupId, key);
	}
	
	@Path("register")
	@POST
	@Produces(MediaType.APPLICATION_JSON)
	public String register(
			@FormParam("group") String groupId,
			@FormParam("key") String key, 
			@FormParam("config") String configStr
			){
		if(StringUtils.isBlank(groupId)	
				|| StringUtils.isBlank(key)
				|| StringUtils.isBlank(configStr)) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		if(!GlobalInstance.groupIds.contains(groupId)) {
			return CaptainJsonUtil.JSON_RS_UNSUPPORT_PARAM_CONTENT;
		}
		
		return cacheAgentStaticConfigService.register(groupId, key, configStr);
	}
	
	@Path("unregister")
	@POST
	@Produces(MediaType.APPLICATION_JSON)
	public String unregister(
			@FormParam("group") String groupId, 
			@FormParam("key") String key
			){
		if(StringUtils.isBlank(groupId)
				|| StringUtils.isBlank(key)) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		if(!GlobalInstance.groupIds.contains(groupId)) {
			return CaptainJsonUtil.JSON_RS_UNSUPPORT_PARAM_CONTENT;
		}
		
		return cacheAgentStaticConfigService.unregister(groupId, key);
	}

	public CacheAgentStaticConfigService getCacheAgentStaticConfigService() {
		return cacheAgentStaticConfigService;
	}

	public void setCacheAgentStaticConfigService(
			CacheAgentStaticConfigService cacheAgentStaticConfigService) {
		this.cacheAgentStaticConfigService = cacheAgentStaticConfigService;
	}

	
	
	
}
