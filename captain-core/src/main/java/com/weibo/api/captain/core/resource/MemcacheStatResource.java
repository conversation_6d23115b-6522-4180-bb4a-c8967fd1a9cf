package com.weibo.api.captain.core.resource;

import java.util.List;

import javax.annotation.Resource;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;

import com.weibo.api.captain.core.service.CacheSlaService;
import com.weibo.api.captain.core.service.MemcacheStatService;
import com.weibo.api.captain.core.util.CaptainJsonUtil;
import com.weibo.api.captain.core.util.CaptainHttpUtil;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.resource  
 *  
 * File: McResource.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
@Path("memcache")
public class MemcacheStatResource {

	@Resource(name="memcacheStatService")
	private MemcacheStatService memcacheStatService;
	
	@Resource(name="cacheSlaService")
	private CacheSlaService cacheSlaService;
	
	/**
	 * 返回指定mc server的sla
	 * @param serverPorts
	 * @return
	 */
	@Path("sla")
	@Produces(MediaType.APPLICATION_JSON)
	@GET
	public String getMemcacheSla(
			@QueryParam("servers") String servers
			){
		
		List<EndpointAddress> addrs = CaptainHttpUtil.parseServers(servers);
		if(addrs == null || addrs.size() == 0) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		return cacheSlaService.getMemcacheSla(addrs);
	}
	
	@Path("stats")
	@Produces(MediaType.APPLICATION_JSON)
	@GET
	public String getStats(
			@QueryParam("servers") String servers
			){
		
		List<EndpointAddress> addrs = CaptainHttpUtil.parseServers(servers);
		if(addrs == null || addrs.size() == 0) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		return memcacheStatService.getMemcacheStat(addrs);
	}
	
	@Path("abnormal")
	@Produces(MediaType.APPLICATION_JSON)
	@GET
	public String getSuspectInstance(
			@QueryParam("servers") String servers
			){
		List<EndpointAddress> addrs = CaptainHttpUtil.parseServers(servers);
		if(addrs == null || addrs.size() == 0) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		return memcacheStatService.getAbnormalStats(addrs);
	}
	
	public MemcacheStatService getMemcacheStatService() {
		return memcacheStatService;
	}

	public void setMemcacheStatService(MemcacheStatService memcacheStatService) {
		this.memcacheStatService = memcacheStatService;
	}

	public CacheSlaService getCacheSlaService() {
		return cacheSlaService;
	}

	public void setCacheSlaService(CacheSlaService cacheSlaService) {
		this.cacheSlaService = cacheSlaService;
	}
	
	
}
