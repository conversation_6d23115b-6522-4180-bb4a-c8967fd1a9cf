package com.weibo.api.captain.core.service;

import java.util.List;

import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.service  
 *  
 * File: CacheAgentService.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public interface CacheAgentService {

	/**
	 * 通过serviceName、clusterName确定cacheAgent的部署列表
	 * @param serviceName
	 * @param clusterName
	 * @return
	 */
	String getCacheAgentDeploy(String serviceName, String clusterName);
	
	/**
	 * 检查cacheAgent的配置一致性，通过sign判断
	 * @param serviceName
	 * @param clusterName
	 * @return
	 */
	String getNotConsistentAgents(String serviceName, String clusterName);
	
	/**
	 * 通过sla、config等找出可能存在异常的cache agent server
	 * @param serviceName
	 * @param clusterName
	 * @return
	 */
	String getSlaAbormalServer(String serviceName, String clusterName);
	
	/**
	 * 查看某个cache agent的status
	 * @param address
	 * @return
	 */
	String getCacheAgentStats(List<EndpointAddress> servers);
	
	
}
