package com.weibo.api.captain.core.service;

import java.util.List;

import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.service  
 *  
 * File: CacheSlaService.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public interface CacheSlaService {

	/**
	 * 获得业务的所有agent的sla统计值
	 * @param serviceName
	 * @param clusterName
	 * @return
	 */
	String getBizSla(String serviceName, String clusterName, String subBizName);
	
	/**
	 * 获得某个cache agent节点的sla指标
	 * @param address
	 * @return
	 */
	String getCacheAgentSla(List<EndpointAddress> addresses);
	
	/**
	 * 获取mc的sla统计
	 * @param addrs
	 * @return
	 */
	String getMemcacheSla(EndpointAddress address);
	
	/**
	 * 获取mc列表的sla统计
	 * @param addrs
	 * @return
	 */
	String getMemcacheSla(List<EndpointAddress> addrs);
}
