package com.weibo.api.captain.core.service;

import java.util.List;

import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.service  
 *  
 * File: MemcacheStatService.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public interface MemcacheStatService {

	/**
	 * 根据server address获取统计信息
	 * @param addrs
	 * @return
	 */
	public String getMemcacheStat(EndpointAddress addrs);
	
	/**
	 * 根据server 列表获取统计信息
	 * @param addrs
	 * @return
	 */
	public String getMemcacheStat(List<EndpointAddress> addrs);
	
	/**
	 * 分析给定的instance列表，找出可能存在异常的instaces
	 * @param addrs
	 * @return
	 */
	public String getAbnormalStats(List<EndpointAddress> addrs);
}
