package com.weibo.api.captain.core.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.CaptainConstants;
import com.weibo.api.captain.core.model.CacheAgentSLAThreshold;
import com.weibo.api.captain.core.model.CacheAgentStat;
import com.weibo.api.captain.core.service.CacheAgentService;
import com.weibo.api.captain.core.util.CaptainJsonUtil;
import com.weibo.vintage.model.EndpointAddress;
import com.weibo.vintage.model.NamingServiceCluster;
import com.weibo.vintage.model.NamingServiceNode;

/**  
 * Package: com.weibo.api.captain.core.service.impl  
 *  
 * File: CacheAgentServiceImpl.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentServiceImpl implements CacheAgentService{

	/**
	 * 通过serviceName、clusterName确定cacheAgent的部署列表，如果clusterName为null，返回服务名为serviceName下的所有节点
	 * @param serviceName
	 * @param clusterName
	 * @return
	 */
	@Override
	public String getCacheAgentDeploy(String serviceName, String clusterName) {
		if(StringUtils.isBlank(serviceName)) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		Map<String, NamingServiceCluster> clusterMap = GlobalInstance.cacheAgentServiceClusterLatest.get(serviceName);
		if(clusterMap == null) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		if(StringUtils.isBlank(clusterName)) {
			return CaptainJsonUtil.buildCacheAgentServiceNodes(serviceName, clusterMap.values());
			
		} else {
			NamingServiceCluster cl = clusterMap.get(clusterName);
			if(cl == null|| cl.getWorkingNodes() == null || cl.getWorkingNodes().size() == 0){
				return CaptainJsonUtil.JSON_RS_EMPTY;
			}
			return CaptainJsonUtil.buildCacheAgentClusterNodes(cl);
		}
	}
	
	/**
	 * 检查cacheAgent的配置一致性，通过sign判断，返回sign不一致的server。
	 * @param serviceName
	 * @param clusterName
	 * @return
	 */
	@Override
	public String getNotConsistentAgents(String serviceName, String clusterName) {
		if(StringUtils.isBlank(serviceName)) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		Map<String, NamingServiceCluster> clusters = GlobalInstance.cacheAgentServiceClusterLatest.get(serviceName);
		if(clusters == null || clusters.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<NamingServiceCluster> suspectClusters = new ArrayList<NamingServiceCluster>();
		
		//if clustername is blank, check all the clusters
		if(!StringUtils.isBlank(clusterName)) {
			NamingServiceCluster clu = clusters.get(clusterName);
			if(!checkConsistenceOfCluster(clu)) {
				suspectClusters.add(clu);
			}
			
		} else {
			for(NamingServiceCluster clu : clusters.values()) {
				if(!checkConsistenceOfCluster(clu)) {
					suspectClusters.add(clu);
				}
			}
		}
		
		if(suspectClusters.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		return CaptainJsonUtil.buildCacheAgentConsistenceProps(serviceName, suspectClusters);
	}
	
	/**
	 * 通过sla、config等找出可能存在异常的cache agent server
	 * @param serviceName
	 * @param clusterName
	 * @return
	 */
	@Override
	public String getSlaAbormalServer(String serviceName, String clusterName) {
		if(StringUtils.isBlank(serviceName)){
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		/** Map<clusterName, CacheAgentStat> */
		Map<String, List<CacheAgentStat>> abnormalClusterNameStats = new HashMap<String, List<CacheAgentStat>>();
		Map<String, NamingServiceCluster> clusters = GlobalInstance.cacheAgentServiceClusterLatest.get(serviceName);
		
		List<EndpointAddress> unreachableServers = new ArrayList<EndpointAddress>();
		
		if(clusters == null || clusters.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		if(!StringUtils.isBlank(clusterName)) {
			NamingServiceCluster clu = clusters.get(clusterName);
			if(clu == null) {
				return CaptainJsonUtil.JSON_RS_EMPTY;
			}
			// get stat from working node
			List<CacheAgentStat> stats = getStatAbnormalNodes(clu);
			if(stats.size() > 0){
				abnormalClusterNameStats.put(clusterName, stats);
			}

			// get unreachable servers
			for(NamingServiceNode node : clu.getUnreachableNodes()) {
				unreachableServers.add(node.getAddress());
			}
			
			
		} else {
			for(NamingServiceCluster clu : clusters.values()) {
				List<CacheAgentStat> stats = getStatAbnormalNodes(clu);
				if(stats.size() > 0) {
					abnormalClusterNameStats.put(clu.getClusterId(), stats);
				}

				// get unreachable servers
				for(NamingServiceNode node : clu.getUnreachableNodes()) {
					unreachableServers.add(node.getAddress());
				}
			}
		}
		
		return CacheAgentStat.toJsonForAbnormalService(serviceName, abnormalClusterNameStats, unreachableServers);
	}
	
	/**
	 * 查看某个cache agent的status
	 * @param address
	 * @return
	 */
	@Override
	public String getCacheAgentStats(List<EndpointAddress> servers) {
		if(servers == null || servers.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<CacheAgentStat> allStats = new ArrayList<CacheAgentStat>();
		for(EndpointAddress addr : servers) {
			CacheAgentStat stat = GlobalInstance.cacheAgentStatLatest.get(addr);
			if(stat != null) {
				allStats.add(stat);
			}
		}
		
		return CacheAgentStat.aggregateJson(allStats);
	}
	
	private boolean checkConsistenceOfCluster(NamingServiceCluster cluster) {
		if(cluster == null || cluster.getWorkingNodes() == null || cluster.getWorkingNodes().size() == 0) {
			return true;
		}
		
		CacheAgentStat original = null;
		for(NamingServiceNode node : cluster.getWorkingNodes()) {
			if(original == null) {
				original = GlobalInstance.cacheAgentStatLatest.get(node.getAddress());
				continue;
			}
			CacheAgentStat tmpStat = GlobalInstance.cacheAgentStatLatest.get(node.getAddress());
			if(tmpStat == null) {
				continue;
			}
			if(!StringUtils.equals(original.getSign(), tmpStat.getSign())
					|| !StringUtils.equals(original.getVersion(), tmpStat.getVersion())
					|| !StringUtils.equals(original.getConfigGroup(), tmpStat.getConfigGroup())
					|| !StringUtils.equals(original.getConfigKey(), tmpStat.getConfigKey())){
				
				return false;
			}
			
		}
		return true;
	}
	
	/**
	 * 从cluster检查出sla状态异常的节点
	 * @param cluster
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<CacheAgentStat> getStatAbnormalNodes(NamingServiceCluster cluster) {
		if(cluster == null || cluster.getWorkingNodes() == null || cluster.getWorkingNodes().size() == 0) {
			return Collections.EMPTY_LIST;
		}
		
		CacheAgentSLAThreshold sla = GlobalInstance.getCacheAgentSlaThreshold(cluster.getClusterId());
		
		List<CacheAgentStat> abnormalStats = new ArrayList<CacheAgentStat>();
		boolean exceedSla = false;
		CacheAgentStat stat = null;
		for(NamingServiceNode node : cluster.getWorkingNodes()) {
			stat = GlobalInstance.cacheAgentStatLatest.get(node.getAddress());
			exceedSla = isCacheAgentExceedSla(stat, sla);
			if(exceedSla) {
				abnormalStats.add(stat);
			}
		}
		
		return abnormalStats;
	}
	
	private boolean isCacheAgentExceedSla(CacheAgentStat stat, CacheAgentSLAThreshold sla) {
		if(stat == null || stat.getSla() == null) {
			return false;
		}
		if(sla == null) {
			sla = CaptainConstants.DEFAULT_CACHE_AGENT_SLA_THRESHOLD;
		}

		if(stat.getSla().getTps() > sla.getTps()
				|| stat.getSla().getCtps() > sla.getCtps()
				|| stat.getSla().getTimeoutsPerSecond() > sla.getTimeoutsPerSecond()
				|| stat.getSla().getAvgTimeInMills() > sla.getAvgTimeInMills()
				|| stat.getSla().getHitRate() < sla.getHitRate()) {
			return true;
		}
		
		return false;
	}
	
	
}
