package com.weibo.api.captain.core.service.impl;

import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.GlobalInstanceRevisor;
import com.weibo.api.captain.core.biz.StaticConfigBiz;
import com.weibo.api.captain.core.exception.CaptainExcepFactor;
import com.weibo.api.captain.core.model.CacheAgentConfig;
import com.weibo.api.captain.core.service.CacheAgentStaticConfigService;
import com.weibo.api.captain.core.util.CaptainJsonUtil;

/**  
 * Package: com.weibo.api.captain.core.service.impl  
 *  
 * File: StaticConfigServiceImpl.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentStaticConfigServiceImpl implements CacheAgentStaticConfigService{

	@Resource(name="staticConfigBiz")
	private StaticConfigBiz staticConfigBiz;
	
	/**
	 * 根据groupId、key获取静态配置
	 * @param groupId
	 * @param key
	 * @return
	 */
	@Override
	public String getConfig(String groupId, String key) {
		if(StringUtils.isBlank(groupId)
				|| StringUtils.isBlank(key)) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		if(!GlobalInstance.groupIds.contains(groupId)) {
			return CaptainJsonUtil.JSON_RS_UNSUPPORT_PARAM_CONTENT;
		}
		
		Map<String, CacheAgentConfig> clusterConfigs = GlobalInstance.cacheAgentConfigsLatest.get(groupId);
		
		CacheAgentConfig config = null;
		//如果本地cache没有，直接从config-server拉取
		if(clusterConfigs != null && clusterConfigs.containsKey(key)) {
			config = clusterConfigs.get(key);
		} else {
			config = staticConfigBiz.lookup(groupId, key);
		}
		
		if(config != null) {
			return config.toYamlStr();
		}
		
		return CaptainJsonUtil.JSON_RS_EMPTY;
	}
	
	/**
	 * 更新静态配置
	 * @param groupId
	 * @param key
	 * @param configStr
	 * @return
	 */
	@Override
	public String register(String groupId, String key, String configStr) {
		if(StringUtils.isBlank(groupId) 
				|| StringUtils.isBlank(key) 
				|| StringUtils.isBlank(configStr)) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		if(!CacheAgentConfig.validateStr(configStr)) {
			ApiLogger.warn(String.format("register false ,for configStr is malformed, groupId = %s, key = %s, config = %s", groupId, key, configStr));
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		boolean rs = staticConfigBiz.register(groupId, key, configStr);
		if(rs) {
			return CaptainJsonUtil.JSON_RS_TRUE;
		} else {
			return CaptainJsonUtil.buildErrorJson(CaptainExcepFactor.CAPTAIN_EXCEP_BACKEND_SERVER_ERR);
		}
		
	}
	
	@Override
	public String unregister(String groupId, String key) {
		if(StringUtils.isBlank(groupId) 
				|| StringUtils.isBlank(key) ) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		boolean rs = staticConfigBiz.unregister(groupId, key);
		if(rs) {
			return CaptainJsonUtil.JSON_RS_TRUE;
		} else {
			return CaptainJsonUtil.buildErrorJson(CaptainExcepFactor.CAPTAIN_EXCEP_BACKEND_SERVER_ERR);
		}
	}

	public StaticConfigBiz getStaticConfigBiz() {
		return staticConfigBiz;
	}

	public void setStaticConfigBiz(StaticConfigBiz staticConfigBiz) {
		this.staticConfigBiz = staticConfigBiz;
	}
	
	
}
