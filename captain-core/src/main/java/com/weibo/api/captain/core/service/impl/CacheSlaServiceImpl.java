package com.weibo.api.captain.core.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.biz.CacheAgentBiz;
import com.weibo.api.captain.core.model.CacheAgentBizStat;
import com.weibo.api.captain.core.model.CacheAgentSla;
import com.weibo.api.captain.core.model.CacheAgentStat;
import com.weibo.api.captain.core.model.MemcacheNodeStat;
import com.weibo.api.captain.core.service.CacheSlaService;
import com.weibo.api.captain.core.util.CaptainJsonUtil;
import com.weibo.vintage.model.EndpointAddress;
import com.weibo.vintage.model.NamingServiceCluster;
import com.weibo.vintage.model.NamingServiceNode;

/**  
 * Package: com.weibo.api.captain.core.service.impl  
 *  
 * File: CacheSlaServiceImpl.java   
 *  
 * Author: fishermen    
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheSlaServiceImpl implements CacheSlaService{

	/**
	 * 获得业务的所有agent的sla统计值，clustername、subBizName都可以为blank；
	 * 如果clusterName为blank，返回所有cluster的聚合sla统计；如果subBzName为blank，所有子业务的聚合sla统计；
	 * @param serviceName
	 * @param clusterName
	 * @subBizName
	 * @return
	 */
	@Override
	public String getBizSla(String serviceName, String clusterName, String subBizName) {
		if(StringUtils.isBlank(serviceName)) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		Map<String, NamingServiceCluster> clusterMap = GlobalInstance.cacheAgentServiceClusterLatest.get(serviceName);
		if(clusterMap == null || clusterMap.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<NamingServiceCluster> clusters = new ArrayList<NamingServiceCluster>();
		if(!StringUtils.isBlank(clusterName)) {
			NamingServiceCluster clu = clusterMap.get(clusterName);
			if(clu == null || clu.getWorkingNodes() == null || clu.getWorkingNodes().size() == 0) {
				return CaptainJsonUtil.JSON_RS_EMPTY;
			}
			clusters.add(clu);
		} else {
			clusters.addAll(clusterMap.values());
		}
		
		List<CacheAgentStat> stats = new ArrayList<CacheAgentStat>();
		List<CacheAgentBizStat> bizStats = new ArrayList<CacheAgentBizStat>();
		boolean queryBizSla = !StringUtils.isBlank(subBizName);
		
		for(NamingServiceCluster clu : clusters) {
			for(NamingServiceNode node : clu.getWorkingNodes()) {
				CacheAgentStat stat = GlobalInstance.cacheAgentStatLatest.get(node.getAddress());
				if(stat == null) {
					continue;
				}
				if(queryBizSla) {
					CacheAgentBizStat bizStat = stat.getBizStats().get(subBizName);
					if(bizStat != null) {
						bizStats.add(bizStat);
					}
				} else{
					stats.add(stat);
				}
				
			}
		}
		
		if(queryBizSla) {
			return CacheAgentBizStat.aggregateSlaJson(subBizName, bizStats);
		} else{
			return CacheAgentStat.aggregateSlaJson(stats);
		}
	}
	
	/**
	 * 获得某个cache agent节点的sla指标
	 * @param address
	 * @return
	 */
	@Override
	public String getCacheAgentSla(List<EndpointAddress> addresses) {
		if(addresses == null || addresses.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<CacheAgentStat> stats = new ArrayList<CacheAgentStat>();
		CacheAgentStat stat = null;
		for(EndpointAddress adr : addresses) {
			stat = GlobalInstance.cacheAgentStatLatest.get(adr);
			if(stat != null) {
				stats.add(stat);
			}
		}
		
		if(stats.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		return CacheAgentStat.aggregateSlaJson(stats);
	}
	
	@Override
	public String getMemcacheSla(EndpointAddress address) {
		if(address == null) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		MemcacheNodeStat stat = GlobalInstance.mcStatLatest.get(address);
		if(stat == null) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		return stat.toSlaJson();
	}
	
	/**
	 * 获取mc列表的sla统计
	 * @param addrs
	 * @return
	 */
	@Override
	public String getMemcacheSla(List<EndpointAddress> addresses) {
		if(addresses == null || addresses.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<MemcacheNodeStat> stats = new ArrayList<MemcacheNodeStat>();
		for(EndpointAddress addr : addresses) {
			MemcacheNodeStat stat = GlobalInstance.mcStatLatest.get(addr);
			if(stat != null) {
				stats.add(stat);
			}
		}
		
		return MemcacheNodeStat.aggregateToSlaJson(stats);
	}
}
