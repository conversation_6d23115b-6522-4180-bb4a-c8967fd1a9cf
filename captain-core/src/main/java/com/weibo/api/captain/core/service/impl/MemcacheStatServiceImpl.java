package com.weibo.api.captain.core.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.sina.api.commons.util.JsonBuilder;

import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.model.MemcacheNodeStat;
import com.weibo.api.captain.core.model.MemcacheSla;
import com.weibo.api.captain.core.model.MemcacheSlaThreshold;
import com.weibo.api.captain.core.service.MemcacheStatService;
import com.weibo.api.captain.core.util.CaptainJsonUtil;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.service.impl  
 *  
 * File: MemcacheStatServiceImpl.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class MemcacheStatServiceImpl implements MemcacheStatService{

	/**
	 * 根据server address获取统计信息
	 * @param addrs
	 * @return
	 */
	@Override
	public String getMemcacheStat(EndpointAddress address) {
		if(address == null) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		MemcacheNodeStat stat = GlobalInstance.mcStatLatest.get(address);
		if(stat == null) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		return stat.toJson();
	}
	
	/**
	 * 根据server 列表获取统计信息
	 * @param addrs
	 * @return
	 */
	@Override
	public String getMemcacheStat(List<EndpointAddress> addrs) {
		if(addrs == null || addrs.size() == 0) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		Map<EndpointAddress, MemcacheNodeStat> statsMap = getMcNodeStats(addrs);
		if(statsMap == null || statsMap.size() == 0) {
			return CaptainJsonUtil.JSON_RS_EMPTY;
		}
		
		List<MemcacheNodeStat> statsList = new ArrayList<MemcacheNodeStat>(statsMap.size());
		for(EndpointAddress ea : addrs) {
			MemcacheNodeStat st = statsMap.get(ea);
			if(st != null) {
				statsList.add(st);
			}
		}
		return MemcacheNodeStat.aggregateToJson(statsList);
	}
	
	/**
	 * 分析给定的instance列表，找出可能存在异常的instaces
	 * @param addrs
	 * @return
	 */
	@Override
	public String getAbnormalStats(List<EndpointAddress> addrs) {
		if(addrs == null || addrs.size() == 0) {
			return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
		}
		
		Map<EndpointAddress, MemcacheNodeStat> statsMap = getMcNodeStats(addrs);
		if(statsMap == null || statsMap.size() == 0) {
			return new JsonBuilder()
				.appendJsonArr("no_stat", CaptainJsonUtil.toJsonBuild4EndpointAddress(addrs))
				.flip().toString();
		}
		
		List<EndpointAddress> noStatServers = new ArrayList<EndpointAddress>();
		List<MemcacheNodeStat> exceedSlaMcStats = new ArrayList<MemcacheNodeStat>();
		for(EndpointAddress ea : addrs) {
			MemcacheNodeStat st = statsMap.get(ea);
			if(st == null) {
				noStatServers.add(ea);
			}else if(isMcExceedSla(st)) {
				exceedSlaMcStats.add(st);
			}
		}
		
		JsonBuilder jb = new JsonBuilder();
		List<JsonBuilder> statJsons = new ArrayList<JsonBuilder>(); 
		for(MemcacheNodeStat st : exceedSlaMcStats) {
			statJsons.add(st.toJsonBuilder());
		}
		jb.appendJsonArr("stats", statJsons);
		
		if(noStatServers.size() > 0) {
			jb.appendJsonArr("no_stat", CaptainJsonUtil.toJsonBuild4EndpointAddress(noStatServers));
		}
		
		jb.flip();
		return jb.toString();
	}
	
	private Map<EndpointAddress, MemcacheNodeStat> getMcNodeStats(List<EndpointAddress> addrs) {
		if(addrs == null || addrs.size() == 0) {
			return null;
		}
		
		Map<EndpointAddress, MemcacheNodeStat> stats = new HashMap<EndpointAddress, MemcacheNodeStat>();
		for(EndpointAddress addr : addrs) {
			MemcacheNodeStat st = GlobalInstance.mcStatLatest.get(addr);
			if(st != null) {
				stats.put(addr, st);
			}
		}
		return stats;
	}
	
	private boolean isMcExceedSla(MemcacheNodeStat stat) {
		if(stat == null) {
			return false;
		}
		
		MemcacheSlaThreshold mcSlaThreshold = GlobalInstance.getMcSlaThreshold(stat.getAddress());
		MemcacheSla sla = stat.getSla();
		if(sla.getTps() > mcSlaThreshold.getTps()
				|| sla.getHitRate() < mcSlaThreshold.getHitRate()
				|| sla.getInFlow() > mcSlaThreshold.getInFlow()
				|| sla.getOutFlow() > mcSlaThreshold.getOutFlow()) {
			return true;
		}
			
		return false;
	}
}
