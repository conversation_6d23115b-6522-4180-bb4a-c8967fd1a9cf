package com.weibo.api.captain.core.servlet;

import javax.servlet.Servlet;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;

import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.core.Initialization;

/**  
 * Package: com.weibo.api.captain.core.servlet  
 *  
 * File: StartServlet.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class StartupServlet extends HttpServlet{

	private static final long serialVersionUID = -3406824573061695659L;

	@Override
	public void init() throws ServletException {
		super.init();
		ApiLogger.info("will init in startServlet... ");
		
		Initialization initBean =  (Initialization)WebApplicationContextUtils
				.getRequiredWebApplicationContext(getServletContext()).getBean("initialization");
		initBean.init();
		
		ApiLogger.info("init completed in startServlet!");
	}
}
