package com.weibo.api.captain.core.task;

import java.util.concurrent.Callable;

import cn.sina.api.commons.util.ApiLogger;

/**  
 * Package: com.weibo.api.captain.core.task  
 *  
 * File: AbstractTask.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public abstract class AbstractTask implements Callable<Boolean>{
	
	public abstract String getTaskName();
	
	public abstract void doTask();
	
	@Override
	public Boolean call() throws Exception {
		
		long startTime = System.currentTimeMillis();
		
		ApiLogger.info(String.format("task [%s] start...", getTaskName()));
		
		try {
			doTask();
		} catch (Exception e) {
			ApiLogger.warn(String.format("task [%s] failure!", getTaskName()), e);
			return false;
		}
		
		long endTime = System.currentTimeMillis();
		ApiLogger.info(String.format("task [%s] completed! timeInMills:%s", getTaskName(), (endTime - startTime)));
		
		return true;
	}
	
	@Override
	public String toString() {
		return getTaskName() + super.toString();
	}
}
