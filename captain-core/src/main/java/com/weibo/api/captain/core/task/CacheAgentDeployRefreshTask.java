package com.weibo.api.captain.core.task;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.biz.NamingServiceBiz;
import com.weibo.vintage.model.NamingServiceCluster;


/**  
 * Package: com.weibo.api.captain.core.task  
 *  
 * File: NamingServiceProbeTask.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentDeployRefreshTask extends AbstractTask{
	
	private NamingServiceBiz namingServiceBiz;
	
	private List<String> serviceNames;
	
	public CacheAgentDeployRefreshTask(NamingServiceBiz namingServiceBiz, List<String> serviceNames){
		this.namingServiceBiz = namingServiceBiz;
		this.serviceNames = serviceNames;
	}
	
	@Override
	public String getTaskName() {
		return TaskConstants.TASK_NAME_REFRESH_AGENT_DEPLOY;
	}
	
	/**
	 * 从configServer拿到cacheAgent的最新列表，并设入globalInstatnce
	 */
	@Override
	public void doTask() {
		if(serviceNames == null || serviceNames.size() == 0) {
			return;
		}
		for(String sname : serviceNames) {
			try {
				reloadAgentNode(sname);
			} catch (Exception e) {
				ApiLogger.warn(String.format("reload cacheAgent deploy false, name = %s ", sname), e);
			}
		}
	}
	
	private void reloadAgentNode(String serviceName) {
		List<NamingServiceCluster> serviceClusters = namingServiceBiz.getServerCluster(serviceName);
		
		// FIXME:如果没有从configServer取到数据，但是配置中有serviceId，而且之前已经取到过，则configServer或网络出问题的概率更大，沿用上次信息更靠谱？
		if(serviceClusters == null){
			Map<String, NamingServiceCluster> clusters = GlobalInstance.cacheAgentServiceClusterLatest.get(serviceName);
			if(clusters != null) {
				GlobalInstance.cacheAgentServiceClusterProbing.put(serviceName, clusters);
			}
			
			return;
		}
		
		for(NamingServiceCluster clst : serviceClusters) {
			Map<String, NamingServiceCluster> clusters = GlobalInstance.cacheAgentServiceClusterProbing.get(serviceName);
			if(clusters == null){
				synchronized (serviceName.intern()) {
					clusters = GlobalInstance.cacheAgentServiceClusterProbing.get(serviceName);
					if(clusters == null) {
						clusters = new ConcurrentHashMap<String, NamingServiceCluster>();
						GlobalInstance.cacheAgentServiceClusterProbing.put(serviceName, clusters);
					}
				}
			}
			clusters.put(clst.getClusterId(), clst);
			ApiLogger.info(String.format("reload agent nodes for serviceId = %s, clusterId = %s, nodes = %s", serviceName, clst.getClusterId(), clst.getWorkingNodes()));
		}
	}
	
}
