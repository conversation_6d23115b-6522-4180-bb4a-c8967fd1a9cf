package com.weibo.api.captain.core.task;


import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.biz.CacheAgentBiz;
import com.weibo.api.captain.core.model.CacheAgentStat;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.task  
 *  
 * File: AgentStatProbeTask.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentStatProbeTask extends AbstractTask {
	
	private CacheAgentBiz cacheAgentBiz;
	
	private EndpointAddress address;
	
	private String bizName;
	
	private CacheAgentStat stat;
	
	public CacheAgentStatProbeTask(CacheAgentBiz cacheAgentBiz, EndpointAddress addr) {
		this.cacheAgentBiz = cacheAgentBiz;
		this.address = addr;
	}
	
	public CacheAgentStatProbeTask(CacheAgentBiz cacheAgentBiz, CacheAgentStat stat,
			String bizName) {
		this.cacheAgentBiz = cacheAgentBiz;
		this.address = stat.getAddr();
		this.stat = stat;
		this.bizName = bizName;
	}
	
	@Override
	public String getTaskName() {
		return TaskConstants.TASK_NAME_PROBE_AGENT;
	}
	
	@Override
	public void doTask() {
		if(stat == null) {
			stat = cacheAgentBiz.getCacheAgentStat(address);
			GlobalInstance.cacheAgentStatProbing.put(address, stat);
		} else {
			cacheAgentBiz.fillCacheAgentBizStat(stat, bizName); 
		}
		
	}
}
