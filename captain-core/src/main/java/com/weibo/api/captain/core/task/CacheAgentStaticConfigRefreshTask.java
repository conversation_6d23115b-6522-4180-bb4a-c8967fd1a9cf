package com.weibo.api.captain.core.task;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang.StringUtils;

import cn.sina.api.commons.util.ApiLogger;

import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.biz.StaticConfigBiz;
import com.weibo.api.captain.core.model.CacheAgentConfig;


/**  
 * Package: com.weibo.api.captain.core.task  
 *  
 * File: AgentConfigRefreshTask.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentStaticConfigRefreshTask extends AbstractTask {
	
	private String groupId;
	
	private StaticConfigBiz staticConfigBiz;
	
	public CacheAgentStaticConfigRefreshTask (StaticConfigBiz staticConfigBiz, String groupId) {
		this.staticConfigBiz = staticConfigBiz;
		this.groupId = groupId;
	}
	
	@Override
	public String getTaskName() {
		return TaskConstants.TASK_NAME_REFRESH_AGENT_CONFIG;
	}
	
	public void doTask() {
		if(StringUtils.isBlank(groupId)) {
			ApiLogger.info(String.format("Ignore task %s, for groupId [%s] is blank", getTaskName(), groupId));
			return;
		}
		
		Map<String, CacheAgentConfig> configs = staticConfigBiz.lookup(groupId);
		
		// FIXME：如果没有从configServer拿到group信息，则沿用旧配置；缘由：配置了groupId，而且拿到了配置，后面拿不到，考虑configServer出问题的概率更大？
		if(configs == null || configs.size() == 0) {
			Map<String, CacheAgentConfig> keyConfigs = GlobalInstance.cacheAgentConfigsLatest.get(groupId);
			if(keyConfigs != null) {
				GlobalInstance.cacheAgentConfigsProbing.put(groupId, keyConfigs);
			}
			return;
		}
		
		Map<String, CacheAgentConfig> configs4Key = GlobalInstance.cacheAgentConfigsProbing.get(groupId);
		if(configs4Key == null) {
			synchronized (groupId.intern()) {
				configs4Key = GlobalInstance.cacheAgentConfigsProbing.get(groupId);
				if(configs4Key == null) {
					configs4Key = new ConcurrentHashMap<String, CacheAgentConfig>();
					GlobalInstance.cacheAgentConfigsProbing.put(groupId, configs4Key);
				}
			}
		}		
		configs4Key.putAll(configs);
		
	}
	
	
}
