package com.weibo.api.captain.core.task;

import com.weibo.api.captain.core.GlobalInstance;
import com.weibo.api.captain.core.biz.MemcachedBiz;
import com.weibo.api.captain.core.model.MemcacheNodeStat;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.task  
 *  
 * File: McStatProbeTask.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class McStatProbeTask extends AbstractTask{

	private MemcachedBiz memcachedBiz;
	
	private EndpointAddress address;
	
	public McStatProbeTask(MemcachedBiz memcachedBiz, EndpointAddress addr) {
		this.memcachedBiz = memcachedBiz;
		this.address = addr;
	}
	
	@Override
	public String getTaskName() {
		return TaskConstants.TASK_NAME_PROBE_MC;
	}
	
	@Override
	public void doTask() {
		MemcacheNodeStat mcStat = memcachedBiz.getMcNodeStat(address);
		if(mcStat != null) {
			GlobalInstance.mcStatProbing.put(address, mcStat);
		}
	}
}

