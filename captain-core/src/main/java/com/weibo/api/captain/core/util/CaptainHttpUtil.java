package com.weibo.api.captain.core.util;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.Util;
import com.weibo.api.captain.common.CaptainConstants;
import com.weibo.api.captain.common.util.ApacheHttpClient;
import com.weibo.vintage.model.EndpointAddress;
import com.weibo.vintage.model.HttpResponseMessage;
import com.weibo.vintage.model.ResponsePacket;
import com.weibo.vintage.utils.VintageConstants;
import com.weibo.vintage.utils.VintageUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**  
 * Package: com.weibo.api.captain.core.util  
 *  
 * File: HttpRequestUtil.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainHttpUtil {
	
	private static Logger httpClientLog = Logger.getLogger("httpclient");
	
	private static boolean NEED_URL_CODEC = false;

	public static HttpResponseMessage doHttpGet(ApacheHttpClient httpClient, final String url) {
		HttpResponseMessage message = httpClient.getAsyncMessage(url);
		
		httpClientLog.info(String.format("url = %s, rsp = %s", url, toString(message)));
		
		return message;
	} 
	
	public static String doHttpGetContent(ApacheHttpClient httpClient, final String url, boolean isZipContent) {
		String content = httpClient.getAsync(url, isZipContent);
		httpClientLog.info(String.format("url = %s, isZip = %s, rsp = %s", url, isZipContent, content));
		
		return content;
	} 
	
	public static String doHttpPost(ApacheHttpClient httpClient, String url, Map<String, ?> nameValues) {
		String responseData = httpClient.post(url, nameValues);
		httpClientLog.info(String.format("url = %s, params = %s, rsp = %s", url, nameValues, responseData));
		
		return responseData;
	}

	public static String doHttpPost(String configServer,ApacheHttpClient httpClient, String url, Map<String, ?> nameValues) {
		String responseData = httpClient.post(configServer,url, nameValues);
		httpClientLog.info(String.format("url = %s, params = %s, rsp = %s", url, nameValues, responseData));

		return responseData;
	}
	
	public static ResponsePacket parseResponse(String rspContent) {
		return parseResponse(rspContent, false);
	}
	
	/**
	 * vintage 的parse 没有检查是否为""的
	 * @param rspContent
	 * @return
	 */
	public static ResponsePacket parseResponse(String rspContent, boolean isJsonNode) {
		if(!StringUtils.isBlank(rspContent)) {
			return ResponsePacket.parser(rspContent, isJsonNode);
		}
		return null;
	}
	
	/**
	 * vintage 自身的validate在false时，会抛出异常，此处需要catch住
	 * @param packet
	 * @param action
	 * @return
	 */
	public static boolean validateResponse(ResponsePacket packet, String action) {
		if(packet == null) {
			return false;
		}
		
		try {
			return VintageUtils.validateResponse(packet, action);
		} catch (Exception e) {
			ApiLogger.warn(String.format("validateResponse false, packet=%s, action=%s", packet, action));
			return false;
		}
	}
	
	/**
	 * cacheService对拉取到的配置不进行url decoding，所以此处需要去掉url encoding & decoding
	 * @param parameter
	 * @return
	 */
	public static String encoding(String parameter) {
		if(!NEED_URL_CODEC) {
			return parameter;
		}
		
		if(StringUtils.isBlank(parameter)){
			return null;
		}
		try {
			return URLEncoder.encode(parameter, VintageConstants.DEFAULT_URL_ENCODING);
		} catch (UnsupportedEncodingException ex) {
			ApiLogger.warn("urlEncoder.encode has exception,parameter is "+ parameter, ex);
			return parameter;
		}

	}
	
	public static String decoding(String parameter){
		if(!NEED_URL_CODEC) {
			return parameter;
		}
		
		if(StringUtils.isBlank(parameter)){
			return null;
		}
		
		try {
			return java.net.URLDecoder.decode(parameter,
					VintageConstants.DEFAULT_URL_ENCODING);
		} catch (UnsupportedEncodingException ex) {
			ApiLogger.warn("urlEncoder.decode has exception,parameter is " + parameter, ex);
			return parameter;
		}
	}
	
	/**
	 * 解析servers字符串，servers的格式为: "host1:port1,host2:port2"
	 * @param servers
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static List<EndpointAddress> parseServers(String servers) {
		if(StringUtils.isBlank(servers)) {
			return Collections.EMPTY_LIST;
		}
		
		List<EndpointAddress> addrs = new ArrayList<EndpointAddress>();
		String[] serverArr = servers.split(CaptainConstants.COMMA);
		for(String serv : serverArr) {
			try {
				String[] hostPort = serv.split(CaptainConstants.COLON);
				if(hostPort.length != 2) {
					continue;
				}
				int port = Util.convertInt(hostPort[1]);
				if(port > 0) {
					EndpointAddress addr = new EndpointAddress(hostPort[0], port);
					addrs.add(addr);
				}
				
			} catch (Exception e) {
				ApiLogger.warn(String.format("Found malformed params for servers:%s", servers), e);
			}
		}
		
		return addrs;
	}
	
	private static String toString(HttpResponseMessage message) {
		if(message != null) {
			return new StringBuilder("64")
				.append("[code = ").append(message.getStatusCode())
				.append(", headers = ").append(message.getHeaders())
				.append(", content = ").append(message.getContent())
				.toString();
		} else {
			return null;
		}
	}
}
