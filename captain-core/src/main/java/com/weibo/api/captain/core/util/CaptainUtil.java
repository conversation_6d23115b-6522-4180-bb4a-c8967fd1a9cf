package com.weibo.api.captain.core.util;

import com.weibo.platform.commons.switcher.SwitcherManager;
import com.weibo.platform.commons.switcher.SwitcherManagerFactoryLoader;

/**
 * Package: com.weibo.api.captain.core.util  
 *  
 * File: CaptainUtil.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CaptainUtil {

	public static long roundToPositiveDivisor(long divisor) {
		return divisor > 0 ? divisor : 1;
	}

	private static final String SWITCHER_IGNORE_NEW_FIELD = "feature.clustermanager.biz.ignorefield";
	private static final String SWITCHER_IGNORE_FALSE_FIELD = "feature.clustermanager.biz.ignorefalsefield";
	private static final String SWITCHER_IGNORE_TRUE_FIELD = "feature.clustermanager.biz.ignoretruefield";
	public static final String SWITCHER_IGNORE_LOCATION_FALSE_FIELD = "feature.clustermanager.biz.ignorelocationfalsefield";
	public static final String SWITCHER_IGNORE_FLAG_ZERO_FIELD = "feature.clustermanager.biz.ignoreflagzerofield";

	public static boolean ignoreNewField(){
		return SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager().getSwitcher(SWITCHER_IGNORE_NEW_FIELD).isOpen();
	}

	public static boolean ignoreFalseField(){
		return SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager().getSwitcher(SWITCHER_IGNORE_FALSE_FIELD).isOpen();
	}

	public static boolean ignoreTrueField(){
		return SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager().getSwitcher(SWITCHER_IGNORE_TRUE_FIELD).isOpen();
	}

	public static boolean ignoreLocationFalseField(){
		return SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager().getSwitcher(SWITCHER_IGNORE_LOCATION_FALSE_FIELD).isOpen();
	}

	public static boolean ignoreFlagZeroField(){
		return SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager().getSwitcher(SWITCHER_IGNORE_FLAG_ZERO_FIELD).isOpen();
	}
}
