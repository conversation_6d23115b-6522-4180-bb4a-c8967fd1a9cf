<?xml version="1.0" encoding="UTF-8"?>  
<beans xmlns="http://www.springframework.org/schema/beans"  
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  
       xmlns:context="http://www.springframework.org/schema/context"  
       xsi:schemaLocation="http://www.springframework.org/schema/beans  
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd  
           http://www.springframework.org/schema/context  
           http://www.springframework.org/schema/context/spring-context-3.0.xsd"> 
       

	<!-- api resources -->
	
	<bean id="agentResource" class="com.weibo.api.captain.core.resource.CacheAgentResource">
		<property name="cacheAgentService" ref="cacheAgentService"></property>
		<property name="cacheSlaService" ref="cacheSlaService"></property>
	</bean>
	
	<bean id="mcResource" class="com.weibo.api.captain.core.resource.MemcacheStatResource">
		<property name="memcacheStatService" ref="memcacheStatService"></property>
		<property name="cacheSlaService" ref="cacheSlaService"></property>
	</bean>
	
	<bean id="cacheAgentStaticConfigResource" class="com.weibo.api.captain.core.resource.CacheAgentStaticConfigResource">
		<property name="cacheAgentStaticConfigService" ref="cacheAgentStaticConfigService"></property>
	</bean>
	
	<!-- biz beans -->
	
	<bean id="httpClient" class="com.weibo.api.captain.common.util.ApacheHttpClient">
        <constructor-arg index="0" value="300"/>
        <constructor-arg index="1" value="2000"/>
        <constructor-arg index="2" value="2000"/>
        <constructor-arg index="3" value="1048576"/>
    </bean> 
	
	<bean id="cacheAgentBiz" class="com.weibo.api.captain.core.biz.spi.CacheAgentBizImpl"/>
	
	<bean id="memcachedBiz" class="com.weibo.api.captain.core.biz.spi.MemcachedBizImpl"/>
	
	<bean id="namingServiceBiz" class="com.weibo.api.captain.core.biz.spi.NamingServiceBizImpl">
		<property name="baseUrl" value="http://10.210.130.46:8080"></property>
		<property name="httpClient" ref="httpClient"></property>
	</bean>
	
	<bean id="staticConfigBiz" class="com.weibo.api.captain.core.biz.spi.StaticConfigBizImpl">
		<property name="baseUrl" value="http://10.210.130.46:8080"></property>
		<property name="httpClient" ref="httpClient"></property>
	</bean>
	
	
	<!-- service -->
	
	<bean id="cacheAgentService" class="com.weibo.api.captain.core.service.impl.CacheAgentServiceImpl"></bean>
	
	<bean id="cacheSlaService" class="com.weibo.api.captain.core.service.impl.CacheSlaServiceImpl"></bean>
	
	<bean id="memcacheStatService" class="com.weibo.api.captain.core.service.impl.MemcacheStatServiceImpl"></bean>
	
	<bean id="cacheAgentStaticConfigService" class="com.weibo.api.captain.core.service.impl.CacheAgentStaticConfigServiceImpl">
		<property name="staticConfigBiz" ref="staticConfigBiz"></property>
	</bean>
	
	
	<!-- sla threshold -->
	
	<bean id="commonAgentSla" class="com.weibo.api.captain.core.model.CacheAgentSLAThreshold">
		<property name="tps" value="30000"></property>
		<property name="ctps" value="25000"></property>
		<property name="timeoutsPerSecond" value="20"></property>
		<property name="avgTimeInMills" value="5"></property>
		<property name="hitRate" value="85"></property>
	</bean>
	
	<bean id="commonMcSla" class="com.weibo.api.captain.core.model.MemcacheSlaThreshold">
		<property name="tps" value="100000"></property>
		<property name="hitRate" value="85"></property>
		<property name="inFlow" value="209715200"></property> <!-- 200M bytes -->
		<property name="outFlow" value="1073741824"></property> <!-- 1G bytes -->
		<property name="evictionPerSecond" value="2000"></property>
	</bean>
	
	<!-- initialize -->
	<bean id="cacheAgentClusterSlas" class="java.util.HashMap">
		<constructor-arg>
			<map>
				<entry key="user-pool-liuyu" value-ref="commonAgentSla"/>
				<entry key="tc-user-agent-cluster" value-ref="commonAgentSla" />
			</map>
		</constructor-arg>
	</bean>
	
	<bean id="mcSlas" class="java.util.HashMap">
		<constructor-arg>
			<map>
				<entry key="user-pool-liuyu" value-ref="commonMcSla" />
				<entry key="tc-user-mc-cluster" value-ref="commonMcSla" />
			</map>
		</constructor-arg>
	</bean>
	
	<bean id="serviceIds" class="java.util.ArrayList">
		<constructor-arg>
			<list>
				<value>fishermen-service</value>
			</list>
		</constructor-arg>
	</bean>
	
	<bean id="staticConfigGroupIds" class="java.util.ArrayList">
		<constructor-arg>
			<list>
				<value>fishermen-config</value>
			</list>
		</constructor-arg>
	</bean>
	
	<bean id="initialization" class="com.weibo.api.captain.core.Initialization">
		<property name="globalInstanceRevisor" ref="globalInstanceRevisor"></property>
	</bean>
		
	<bean id="globalInstanceRevisor" class="com.weibo.api.captain.core.GlobalInstanceRevisor">
		<property name="cacheAgentClusterSlas" ref="cacheAgentClusterSlas"></property>
		<property name="mcSlas" ref="mcSlas"></property>
		<property name="serviceIds" ref="serviceIds"></property>
		<property name="staticConfigGroupIds" ref="staticConfigGroupIds"></property>
		<property name="namingServiceBiz" ref="namingServiceBiz"></property>
		<property name="staticConfigBiz" ref="staticConfigBiz"></property>
		<property name="cacheAgentBiz" ref="cacheAgentBiz"></property>
		<property name="memcachedBiz" ref="memcachedBiz"></property>
		<property name="taskScheduledDelaySeconds" value="60"></property>
	</bean>
</beans>
