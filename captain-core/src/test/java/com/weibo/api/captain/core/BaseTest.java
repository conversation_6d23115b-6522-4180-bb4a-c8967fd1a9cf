package com.weibo.api.captain.core;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.net.URL;


import com.weibo.api.captain.common.util.ApacheHttpClient;
import com.weibo.api.captain.core.model.CacheAgentConfig;

/**  
 * Package: com.weibo.api.captain.core  
 *  
 * File: BaseTest.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class BaseTest {

	private static String LINE_SEPERATOR = "\n";
	//private String configFile = "test/resources/nutcracker.yml";
	private String configFile = "test/resources/conf.conf";
	
	protected String defaultServiceName = "/api/cache/fishermen-common-cs-tc";
	protected String defaultClusterName = "fishermen-cs-tc-c1";
	
	protected String defaultStaticConfigGroupId = "/api/group/fishermen-user-tc"; 
	protected String defaultStaticConfigKey = "all";
	
	protected String defaultBaseUrl = "http://10.210.130.46:8080/" ;
	protected ApacheHttpClient defaultHttpClient = new ApacheHttpClient();
	
	protected CacheAgentConfig config;
	
	public void init() throws Exception{
		if(config != null) {
			return;
		}
		
		URL classPathUrl = this.getClass().getClassLoader().getResource("");
		
		//System.out.println("load config file:" + classPathUrl + configFile);
		
		File file = new File(classPathUrl.getFile(), configFile);
		
		BufferedReader fileReader = new BufferedReader(new FileReader(file));
		
		String line = null;
		StringBuilder fileBuilder = new StringBuilder();
		while((line = fileReader.readLine()) != null){
			fileBuilder.append(line).append(LINE_SEPERATOR);
		}
		//System.out.println("get file_content:\n" + fileBuilder);
		
		config = CacheAgentConfig.parseStr(fileBuilder.toString());
		
		
	}

	public CacheAgentConfig getConfig() {
		return config;
	}

	public void setConfig(CacheAgentConfig config) {
		this.config = config;
	}
	
	
}
