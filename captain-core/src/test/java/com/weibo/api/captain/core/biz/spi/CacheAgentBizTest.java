package com.weibo.api.captain.core.biz.spi;

import org.junit.Test;

import com.weibo.api.captain.core.model.CacheAgentBizStat;
import com.weibo.api.captain.core.model.CacheAgentStat;
import com.weibo.api.captain.core.util.CaptainJsonUtil;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.biz.spi  
 *  
 * File: CacheAgentBizTest.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentBizTest {

	private CacheAgentBizImpl cacheAgentBiz = new CacheAgentBizImpl();
	
	@Test
	public void getCacheAgentStat() {
		String host = "***************";
		int port = 6379;
		String bizName = "user";
		
		EndpointAddress addr = new EndpointAddress(host, port);
		CacheAgentStat stat = cacheAgentBiz.getCacheAgentStat(addr);
		System.out.println(stat.toJson());
		
		cacheAgentBiz.fillCacheAgentBizStat(stat, bizName);
		System.out.println("======= bizstat ==========");
		System.out.println(stat.getBizStats().get(bizName));
	}
	
}
