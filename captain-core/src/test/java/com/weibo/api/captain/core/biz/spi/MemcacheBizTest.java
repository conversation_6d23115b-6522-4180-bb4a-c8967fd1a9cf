package com.weibo.api.captain.core.biz.spi;

import org.junit.Test;

import com.weibo.api.captain.core.BaseTest;
import com.weibo.api.captain.core.model.MemcacheNodeStat;
import com.weibo.api.captain.core.util.CaptainJsonUtil;
import com.weibo.vintage.model.EndpointAddress;

/**  
 * Package: com.weibo.api.captain.core.biz.spi  
 *  
 * File: MemcacheBizTest.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class MemcacheBizTest extends BaseTest {

	private MemcachedBizImpl mcBiz = new MemcachedBizImpl();
	
	@Test
	public void getMcNodeStat() {
		String host = "***************";
		int port = 11211;
		
		EndpointAddress address = new EndpointAddress(host, port);
		MemcacheNodeStat stat = mcBiz.getMcNodeStat(address);
		System.out.println(stat.toJson());
	}
	
}
