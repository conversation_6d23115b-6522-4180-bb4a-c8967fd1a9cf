package com.weibo.api.captain.core.biz.spi;

import java.util.List;

import org.junit.Before;
import org.junit.Test;

import com.weibo.api.captain.core.BaseTest;
import com.weibo.api.captain.core.util.CaptainJsonUtil;
import com.weibo.vintage.model.NamingServiceCluster;

/**  
 * Package: com.weibo.api.captain.core.biz.spi  
 *  
 * File: NamingServiceBizTest.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class NamingServiceBizTest extends BaseTest{

	private com.weibo.api.captain.core.biz.spi.NamingServiceBizImpl namingServiceBiz = new NamingServiceBizImpl();
	
	private String bjBaseUrl = "http://10.210.130.46:8080";
	private String serviceName = "liuyu-cacheService-test";
	private String clusterName = "user-pool-liuyu";
	
	@Before
	public void init(){
		namingServiceBiz.setHttpClient(defaultHttpClient);
		namingServiceBiz.setBaseUrl(bjBaseUrl);
	}
	
	@Test
	public void getServerCluster() {
		
		List<NamingServiceCluster> clusters = namingServiceBiz.getServerCluster(serviceName);
		System.out.println("========= service clusters =========");
		System.out.println(clusters);
		
		NamingServiceCluster cluster = namingServiceBiz.getServerCluster(serviceName, clusterName);
		System.out.println("========= service cluster =========");
		System.out.println(cluster);
	}
	
}
