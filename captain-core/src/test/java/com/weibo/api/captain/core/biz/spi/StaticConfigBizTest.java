package com.weibo.api.captain.core.biz.spi;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.QueryParam;

import junit.framework.Assert;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.weibo.api.captain.core.BaseTest;
import com.weibo.api.captain.core.model.CacheAgentConfig;


/**  
 * Package: com.weibo.api.captain.core.biz.spi  
 *  
 * File: StaticConfigBizTest.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class StaticConfigBizTest extends BaseTest{

	
	private com.weibo.api.captain.core.biz.spi.StaticConfigBizImpl configBiz = new StaticConfigBizImpl();
	private String captainBaseUrl = "http://10.210.130.46:8010/1/";
	
	@Before
	public void beforeMethod() throws Exception{
		init();
		configBiz.setBaseUrl(defaultBaseUrl);
		configBiz.setHttpClient(defaultHttpClient);
	}
	
	@After
	public void afterMethod() {
		System.out.println("run after one test in StaticConfigBizTest");
	}
	
	@Test
	public void register() {
		String key = "all-1";
		String configStr = config.toYamlStr();
		boolean registerRs = configBiz.register("fishermen-config", key, configStr);
		
		CacheAgentConfig config = CacheAgentConfig.parseStr(configStr);
		
		Assert.assertEquals(registerRs, true);
		Assert.assertTrue(config != null);
		
		String url = this.captainBaseUrl + "agent_config/register";
		Map<String, String> params = new HashMap<String, String>();
		params.put("group", "liuyu-cacheService-config");
		params.put("key", "KeyA");
		params.put("config", configStr);
		String registerJsonRs = defaultHttpClient.post(url, params);
		System.out.println("register json: " + registerJsonRs);
	}
		
	@Test
	public void unregister() {
		boolean rs = configBiz.unregister(defaultStaticConfigGroupId, defaultStaticConfigKey);
		
		Assert.assertEquals(rs, true);
		
		String group = "fishermen-config";
		String key = "all-3";
		String url = this.captainBaseUrl + "agent_config/unregister";
		Map<String, String> params = new HashMap<String, String>();
		params.put("group", group);
		params.put("key", key);
		
		String jsonRs = defaultHttpClient.post(url, params);
		System.out.println("unregister json:" + jsonRs);
	}
	
	@Test
	public void lookup() {
		register();
		
		String groupId = defaultStaticConfigGroupId;
		String key = defaultStaticConfigKey;
		CacheAgentConfig configByGroupKey = configBiz.lookup(groupId, key);
		
		Map<String, CacheAgentConfig> configs = configBiz.lookup(groupId);
		CacheAgentConfig config = configs.get(key);
		
		System.out.println("map:" + configs);
		System.out.println(configByGroupKey.toYamlStr());
		System.out.println("====================== toYaml ======================");
		System.out.println(config.getGroupConfs().get(0).extractAllMcNodes());
		
		Assert.assertTrue(config != null);
		Assert.assertEquals(configByGroupKey.toYamlStr(), config.toYamlStr());
		
		System.out.println("============== config yaml ==================");
		System.out.println(config.toYamlStr());
		
		
				
	}
}
