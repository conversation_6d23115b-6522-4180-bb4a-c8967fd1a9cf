package com.weibo.api.captain.core.model;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.net.URL;

import junit.framework.Assert;

import org.junit.Test;


/**  
 * Package: com.weibo.api.captain.core.model  
 *  
 * File: CacheAgentConfigTest.java   
 *  
 * Author: fishermen   
 *  
 * Copyright @ 2015 Corpration Name  
 *   
 */
public class CacheAgentConfigTest {

	private static String LINE_SEPERATOR = "\n";
	
	private String configFile = "test/resources/nutcracker.yml";
	

	@Test
	public void testYamlParseAndbuild() throws Exception{
		URL classPathUrl = this.getClass().getClassLoader().getResource("");
		
		System.out.println("classPath:" + classPathUrl);
		
		File file = new File(classPathUrl.getFile(), configFile);
		BufferedReader fileReader = new BufferedReader(new FileReader(file));
		
		String line = null;
		StringBuilder fileBuilder = new StringBuilder();
		while((line = fileReader.readLine()) != null){
			fileBuilder.append(line).append(LINE_SEPERATOR);
		}
		System.out.println("get file_content:\n" + fileBuilder);
		
		CacheAgentConfig config = CacheAgentConfig.parseStr(fileBuilder.toString());
		
		System.out.println("============ config.toString =============");
		System.out.println(config.toYamlStr());
		
		Assert.assertEquals(fileBuilder.toString(), config.toYamlStr());
	}
		
}
