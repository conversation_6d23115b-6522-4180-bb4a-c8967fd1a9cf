# system basic image
# with :
#    java
#    dnsmasq

FROM registry.intra.weibo.com/weibo_rd_content/tomcat_feed:jdk8.0.40_tomcat7.0.81_g1
MAINTAINER weibo_rd_content <<EMAIL>>

# copy source code and configuration
# ADD confs /data1/confs/
ADD node_pool /data1/node_pool/
# ADD authconfs /data1/authconfs/
ADD authkey.properties /data1/
ADD watchman.properties /data1/
ADD 503.sh /data1/weibo/bin/503.sh
ADD 200.sh /data1/weibo/bin/200.sh
ADD catalina.sh /data1/weibo/bin/catalina.sh
RUN chmod +x /data1/weibo/bin/503.sh /data1/weibo/bin/200.sh /data1/weibo/bin/catalina.sh
ADD server.xml /data1/weibo/conf/server.xml
#
ADD ROOT /data1/weibo/webapps/ROOT/
WORKDIR /data1/weibo/bin
