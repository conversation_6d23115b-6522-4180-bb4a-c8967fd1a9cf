/**  
 * Project Name:captain-port  
 * File Name:CaptainAdminFilter.java  
 * Package Name:com.weibo.api.captain.port.filter  
 * Date:2018年7月12日下午2:22:44  
 * Copyright (c) 2018, @weibo All Rights Reserved.  
 *  
*/  
  
package com.weibo.api.captain.port.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;

import cn.sina.api.commons.util.ApiLogger;

/**  
 * <pre>  
 * ClassName:Captain<PERSON><PERSON><PERSON><PERSON>ilter 
 * 
 * 相关filter
 * <pre/>   
 * Date:     2018年7月12日 下午2:22:44 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class CaptainAdminFilter implements Filter{
    private String excludedPages;       
    private String[] excludedPageArray;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        excludedPages = filterConfig.getInitParameter("excludedPages");
        if (StringUtils.isNotEmpty(excludedPages)) {
            excludedPageArray = excludedPages.split(",");
        }
        return;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse resp = (HttpServletResponse) response;
        
        boolean isExcludedPage = false;
        for (String page : excludedPageArray) {// 判断是否在过滤url之外
            ApiLogger.warn("result: page="+page+",uri="+req.getRequestURI());
            if (req.getRequestURI().endsWith(page)) {
                isExcludedPage = true;
                break;
            }
        }
        if (isExcludedPage) {
            chain.doFilter(req, resp);
            return;
        }
        HttpSession session = req.getSession(false);
        if (session == null || session.getAttribute("username") == null) {
            resp.sendRedirect(req.getContextPath() + "/login.html");
        }
        chain.doFilter(req, resp);
    }

    @Override
    public void destroy() {
    }

}
  
	