package com.weibo.api.captain.port.servlet;

import cn.sina.api.commons.util.ApiLogger;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.weibo.api.captain.assigner.model.IdcInfo;
import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.assigner.model.ResourceAccessSetting;
import com.weibo.api.captain.common.util.CaptainUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.List;

public class Base {

    public ResourceAccessSetting buildResourceAccessSetting(String hashStrategy, String distribution, String biz, int saveTag, int autoEjectHosts, long timeout, long serverRetryTimeout, int serverFailureLimit, int resourceType, int partialReply, long exptime,int forceWriteAll,int updateSlaveL1,int localAffinity,int flag) {
        ResourceAccessSetting resourceAccessSetting = new ResourceAccessSetting();
        resourceAccessSetting.setHash(hashStrategy);
        resourceAccessSetting.setDistribution(distribution);
        resourceAccessSetting.setHashTag(biz);
        resourceAccessSetting.setSaveTag(saveTag);
        resourceAccessSetting.setAutoEjectHosts(autoEjectHosts);
        resourceAccessSetting.setTimeout(timeout);
        resourceAccessSetting.setServerRetryTimeout(serverRetryTimeout);
        resourceAccessSetting.setServerFailureLimit(serverFailureLimit);
        resourceAccessSetting.setResourceType(resourceType);
        resourceAccessSetting.setPartialReply(partialReply);
        resourceAccessSetting.setExptime(exptime);
        resourceAccessSetting.setForceWriteAll(forceWriteAll);
        resourceAccessSetting.setUpdateSlaveL1(updateSlaveL1);
        resourceAccessSetting.setlocalAffinity(localAffinity);
        resourceAccessSetting.setFlag(flag);
        return resourceAccessSetting;
    }

    public RecordInfo buildRecordInfo(String type, String biz, float hitPercent, int readTps, int writeTps, String applyUser, int id) {
        RecordInfo recordInfo = new RecordInfo();
        recordInfo.setType(type);
        recordInfo.setBiz(biz);
        recordInfo.setHitPercent(hitPercent);
        recordInfo.setReadTps(readTps);
        recordInfo.setWriteTps(writeTps);
        recordInfo.setApplyUser(applyUser);
        if (id != 0) {
            recordInfo.setProId(id);
        }
        return recordInfo;
    }

    // 将JSON格式的字符串转换为IdcInfo对象的列表
    public List<IdcInfo> buildIdcInfo (String idcInfoString) throws Exception {
        try {
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            List<IdcInfo> result = gson.fromJson(idcInfoString, new TypeToken<List<IdcInfo>>() {
            }.getType());
            return result;
        } catch (Exception e) {
            ApiLogger.warn("buildIdcInfo error,idcInfoString="+idcInfoString);
            throw e;
        }
    }

    public void doReuse(List<IdcInfo> idcInfoList,String idc,String masterL1Ips,int masterL1Blocks) {
        for (IdcInfo tmpIdcInfo : idcInfoList) { //判断复用
            String reuseIdc = tmpIdcInfo.getReuseIdc();
            if (reuseIdc.equals(idc)) {
                tmpIdcInfo.setMasterL1Block(masterL1Blocks);
                tmpIdcInfo.setMasterL1Ips(masterL1Ips);
            }
        }
    }

    public void doReuse(List<IdcInfo> idcInfoList,String idc,String masterIps) {
        for (IdcInfo idcInfo : idcInfoList) { //判断复用
            String reuseIdc = idcInfo.getReuseIdc();
            if (reuseIdc.equals(idc)) {
                idcInfo.setMasterIps(masterIps);
            }
        }
    }



    public ResourceAccessSetting buildResourceAccessSetting(JSONObject resource,String namespace) {
        String hash = resource.getString("hash");
        String distribution = resource.getString("distribution");
        int saveTag = resource.getInt("saveTag");
        int autoEjectHosts = resource.getInt("autoEjectHosts");
        int timeout = resource.getInt("timeout");
        int serverRetryTimeout = resource.getInt("serverRetryTimeout");
        int serverFailureLimit = resource.getInt("serverFailureLimit");
        int resourceType = resource.getInt("resourceType");
        int partialReply = resource.getInt("partialReply");
        long exp = resource.getLong("exptime");
        int forceWriteAll= CaptainUtil.getForceWriteAll(resource);
        int updateSlaveL1 = CaptainUtil.getUpdateSlaveL1(resource);
        int localAffinity = CaptainUtil.getlocalAffinity(resource);
        int flag = CaptainUtil.getFlag(resource);
        ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(hash, distribution, namespace, saveTag, autoEjectHosts, timeout, serverRetryTimeout, serverFailureLimit, resourceType, partialReply, exp,forceWriteAll, updateSlaveL1, localAffinity, flag);
        return resourceAccessSetting;
    }

    public String getIdcInfo(Object idcInfoList,String group) {
        String idcInfo = "";
        JSONArray idcArr = JSONArray.fromObject(idcInfoList);
        for (int j=0;j<idcArr.size();j++) {
            String idc = idcArr.getJSONObject(j).getString("idc");
            if (group.endsWith(".pool."+idc)) {
                idcInfo = "[" + idcArr.getJSONObject(j).toString() + "]";
            }
        }
        return idcInfo;
    }
}
