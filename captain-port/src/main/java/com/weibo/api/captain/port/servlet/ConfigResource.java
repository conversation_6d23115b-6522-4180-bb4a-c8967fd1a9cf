package com.weibo.api.captain.port.servlet;

import cn.sina.api.commons.util.ApiLogger;
import cn.sina.api.commons.util.JsonBuilder;
import com.weibo.api.captain.assigner.model.IdcInfo;
import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.assigner.model.ResourceAccessSetting;
import com.weibo.api.captain.assigner.model.ResourceState;
import com.weibo.api.captain.assigner.service.ResourceService;
import com.weibo.api.captain.common.util.ThreadLocalUtil;
import com.weibo.api.captain.common.exception.CaptainExcepFactor;
import com.weibo.api.captain.common.exception.CaptainException;
import com.weibo.api.captain.common.util.CaptainJsonUtil;
import com.weibo.api.captain.common.util.CaptainUtil;
import com.weibo.platform.commons.switcher.SwitcherManagerFactoryLoader;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

@Path("cacheservice")
public class ConfigResource extends Base {
    private ResourceService resourceService;

    public void setResourceService(ResourceService resourceService) {
        this.resourceService = resourceService;
    }

    private String staticFeedConfigStr;

//    public final static String FEED_CONFIG_STR = "http://feed.config.api.weibo.com";
    //private final static String FEED_CONFIG_STR = "http://10.75.5.84:8080";
    private final static String CONFIG_STR = "http://config.api.weibo.com";
    //private final static String CONFIG_STR = "http://10.75.5.84:8080";
    private final static String FEED_SUFFIX = "feedcontent";
    private final static int PROXY_STATE = 0; //从dba那了解到，目前基本都为0
    private static Set<String> allIdcs = new HashSet<String>();
    private static ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
    private final static int RELOAD_PERIOD = 20;
    public static ConcurrentMap<String,Set<Integer>> resources = new ConcurrentHashMap<String, Set<Integer>>(1000);
    private static final String USER = "weibo_dba";
    private static ReentrantLock delLock = new ReentrantLock();
    //目前支持的资源res
    private static Set<String> resourceList = new HashSet<String>();
    private static Set<String> roleList = new HashSet<String>();

    static {
        SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager()
                .registerSwitcher(CaptainUtil.SWITCHER_IGNORE_NEW_FIELD, false);
        SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager()
                .registerSwitcher(CaptainUtil.SWITCHER_IGNORE_FALSE_FIELD, true);
        SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager()
                .registerSwitcher(CaptainUtil.SWITCHER_IGNORE_TRUE_FIELD, true);
        SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager()
                .registerSwitcher(CaptainUtil.SWITCHER_IGNORE_LOCATION_FALSE_FIELD, true);
        SwitcherManagerFactoryLoader.getSwitcherManagerFactory().getSwitcherManager()
                .registerSwitcher(CaptainUtil.SWITCHER_IGNORE_FLAG_ZERO_FIELD, true);
    }

    public void init() {
        // 初始化所有的idc列表
        allIdcs.add("yf");
        allIdcs.add("tc");
        allIdcs.add("aliyun");
        allIdcs.add("aliyun_tc");
        allIdcs.add("bx");
        allIdcs.add("yz");
        allIdcs.add("xdl");
        allIdcs.add("yhg");
        allIdcs.add("ft");
        allIdcs.add("ja");
        allIdcs.add("xd");
        allIdcs.add("qxg");
        allIdcs.add("nfjd");
        allIdcs.add("sx");
        allIdcs.add("xg");
        allIdcs.add("huawei");
        allIdcs.add("huawei_tc");
        allIdcs.add("jxg");
        allIdcs.add("qxj");
        allIdcs.add("aliyun_wlcb");
        allIdcs.add("aliyun_zb");
        allIdcs.add("wq");
        allIdcs.add("aliyun_wq");
        allIdcs.add("huawei_wq");

        //定期获取所有资源信息,便于跟shanks融合，每次将数据存成一个map k:port,v:id
        loadResource();
        executorService.scheduleWithFixedDelay(new Runnable() {
                @Override
                public void run() {
                    loadResource();
                }
            },RELOAD_PERIOD,RELOAD_PERIOD, TimeUnit.SECONDS);
        resourceList.add("memcacheq");
        resourceList.add("memcached");
        roleList.add("master");
        roleList.add("slave");
        roleList.add("masterL1");
    }

    private void loadResource() {
        try{
            String result = resourceService.getResource(true);
            resolveResource(result);
            ApiLogger.info("resource map size:" + resources.size());
        } catch (Exception e) {
            ApiLogger.info("resource load failed, exception: " + e);
        } catch (Throwable t) {
            ApiLogger.info("resource load failed, t:" + t);
        }
    }

    private void resolveResource(String result) {
        JSONObject jsonObject = JSONObject.fromObject(result);
        JSONArray jsonArray = jsonObject.getJSONArray("result");
        ConcurrentMap<String,Set<Integer>> tmp = new ConcurrentHashMap<String, Set<Integer>>();
        for (int i=0;i<jsonArray.size();i++) {
            int id = jsonArray.getJSONObject(i).getInt("id");
            JSONArray idcInfos = jsonArray.getJSONObject(i).getJSONArray("idcInfos");
            String  masterIps = idcInfos.getJSONObject(0).getString("masterIps");
            String port = masterIps.split(",")[0].split(":")[1];
            Set<Integer> set = new HashSet<Integer>();
            if (tmp.containsKey(port)) {
                set = tmp.get(port);
                //ApiLogger.info("the resource, port:" + port+ ",list:" + Arrays.toString(set.toArray()) + ",size:" + set.size());
            }
            set.add(id);
            tmp.putIfAbsent(port,set);
        }
        resources = tmp;
    }

    @Path("/idcs")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public String getIdcs() {
        JSONObject jb = new JSONObject();
        JSONArray ja = new JSONArray();
        for (String idc : allIdcs) {
            ja.add(idc);
        }
        jb.put("idcs",ja);
        return jb.toString();
    }

    @Path("/config/{port}")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public String addConfig(
            @PathParam("port") String port,
            String configStr ) {
        ApiLogger.info("add config param,port:"+port+",configStr:"+configStr);
        String checkParam = checkParamForCreateConfig(configStr);
        if (!"ok".equals(checkParam)) {
            return checkParam;
        }
        if (isExist(port)) {
            return returnRes(400,"port already exist");
        }
        try {
            JSONObject jsonObject = JSONObject.fromObject(configStr);
            String biz = jsonObject.getString("biz");
            String namespace = jsonObject.getString("namespace");
            if(namespace.contains(" ")){
                return returnRes(400,"namespace contains space");
            }
            JSONArray idcs = jsonObject.getJSONArray("idcs");
            int exp = jsonObject.getInt("timeout");
            int forceWriteAll = CaptainUtil.getForceWriteAll(jsonObject);
            int updateSlaveL1 = CaptainUtil.getUpdateSlaveL1(jsonObject);
            int localAffinity = CaptainUtil.getlocalAffinity(jsonObject);
            int flag = CaptainUtil.getFlag(jsonObject);
            String service = resourceService.getService();
            if (!isExist(idcs)) {
                return returnRes(400,"idcinfos list invalid");
            }
            // getservice返回所有的已经注册的type，因此直接contains会方便点
            if(!service.contains(biz)) {
                String configAddr = CONFIG_STR;
                if (biz.endsWith(FEED_SUFFIX)) {
                    configAddr = staticFeedConfigStr;
                }
                //创建servicepool时设置所有的机房信息
                String result = createServicePool(biz, PROXY_STATE, configAddr,allIdcs.toArray(new String[0]));
                JSONObject jb = JSONObject.fromObject(result);
                if (result.contains("code")) {
                    return returnRes(400,jb.getString("desc"));
                } else if (!"true".equals(jb.getString("result"))){
                    return returnRes(404,"create service pool error");
                }
            }
//            // 通常要做的操作：
//            // 1. 申请提案
//            // 2. 分配资源
            String assignResult = processAssign(biz, namespace, idcs, exp, forceWriteAll, updateSlaveL1,localAffinity,flag, port);
            ApiLogger.warn("processAssign result:" + assignResult);
            if (assignResult.contains("code")) {
                JSONObject jb = JSONObject.fromObject(assignResult);
                return returnRes(400,jb.getString("desc"));
            }
        } catch (Exception e) {
            ApiLogger.warn("config  error,"+ e);
            return returnRes(404,"failed");
        }finally {
            ThreadLocalUtil.clear();
        }
        return returnRes(200,"success");
    }


    // 资源迁移
    @Path("/config/{port}/transfer")
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    public String updateConfig(
            @PathParam("port") String port,
            String configStr
    ) {

        ApiLogger.info("transfer config param,port:"+port+",configStr:"+configStr);
        String checkParam = checkParamForUpdateConfig(configStr);
        if (!"ok".equals(checkParam)) {
            return checkParam;
        }
        try {
            JSONObject jsonObject = JSONObject.fromObject(configStr);
            String paramPort = jsonObject.getString("port");
            if (!paramPort.equals(port)) {
                return returnRes(400,"param port and request port are different");
            }
            if (!isExist(port)) {
                return returnRes(400,"port isn't exist");
            }
            JSONArray oldIps = jsonObject.getJSONArray("oldIps");
            JSONArray newIps = jsonObject.getJSONArray("newIps");
            if (oldIps.size() ==0 || newIps.size() ==0 ){
                return returnRes(400,"the length of oldIps or newIps is 0");
            }
            if (oldIps.size() != newIps.size()) {
                return returnRes(400,"the length of old ips are differenct from new ips");
            }
            Set<Integer> ids = resources.get(port);
            Map<Integer,String> resourceMap = new HashMap<Integer, String>();
            for (Integer id:ids) {
                String resourceInfo = resourceService.getResource(id);
                resourceMap.put(id,resourceInfo);
            }
            boolean isExist = checkInstanceExist(resourceMap,oldIps,port);
            if (!isExist) {
                return returnRes(400,"oldIp isn't exist");
            }
            if (checkInstanceExist(resourceMap,newIps,port)) {
                return returnRes(400,"newIp is exist");
            }
            for (Map.Entry<Integer, String> entry : resourceMap.entrySet()) {
                int id = entry.getKey();
                String resourceInfo = entry.getValue();
                JSONObject resource = JSONObject.fromObject(resourceInfo);
                String namespace = resource.getString("biz");
                String idcInfos = resource.getString("idcInfos");
                for (int i = 0; i< oldIps.size();i++) {
                    String oldIp = oldIps.getString(i);
                    String newIp = newIps.getString(i);
                    idcInfos = idcInfos.replaceAll(oldIp + ":" + port, newIp + ":" + port);
                }
                ApiLogger.info("transfer port:" + port + "idcInfos:" + idcInfos);
                ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(resource, namespace);
                List<IdcInfo> idcInfoList = buildIdcInfo(idcInfos);
                String yaml = resourceService.generatorYaml(id, resourceAccessSetting, idcInfoList);
                ApiLogger.info("transfer yaml:" + yaml);  // 生成yaml
                JSONArray jsonArray = JSONArray.fromObject(yaml);
                String result = batchAssign(jsonArray, idcInfos, id);
                if (result.contains("400") && result.contains("failed")) {
                    return result;
                }
            }
        } catch (Exception e) {
            ApiLogger.warn("update config error:" + e.getMessage());
            return returnRes(400, "failed");
        }finally {
            ThreadLocalUtil.clear();
        }
        return returnRes(200, "success");
    }

    //参数修改
    @Path("/config/modify")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public String processModify(@FormParam("id") int id, @FormParam("expire") long exptime,
                                @FormParam("forceWriteAll") int forceWriteAll, @FormParam("updateSlaveL1") int updateSlaveL1,
                                @FormParam("localAffinity") int localAffinity, @FormParam("flag") int flag) {
        try {
            ApiLogger.info("update resource: id:" + id + ",exptime:" + exptime + ",forceWriteAll=" + forceWriteAll + ",updateSlaveL1=" + updateSlaveL1 + ",localAffinity=" + localAffinity+ ",flag=" + flag);
            String resourceInfo = resourceService.getResource(id);
            if (forceWriteAll > 1 || forceWriteAll < 0 || updateSlaveL1 > 1 || updateSlaveL1 < 0 || localAffinity > 1 || localAffinity < 0 || flag < 0 || exptime < 0) {
                ApiLogger.error("invalid param");
                return returnRes(400, "invalid param");
            }
            if (StringUtils.isEmpty(resourceInfo)) {
                return returnRes(400, "no resource found");
            }
            JSONObject resource = JSONObject.fromObject(resourceInfo);
            String namespace = resource.getString("biz");
            String idcInfos = resource.getString("idcInfos");
            long dbExptime = resource.getLong("exptime");
            int dbForceWriteAll;
            int dbUpdateSlaveL1;
            int dblocalAffinity;
            int dbFlag = 0;
            try {
                dbForceWriteAll = resource.getInt("forceWriteAll");
                dbUpdateSlaveL1 = resource.getInt("updateSlaveL1");
                dblocalAffinity = resource.getInt("localAffinity");
                dbFlag = resource.getInt("flag");
            } catch (Exception e) {
                dbForceWriteAll = 0;
                dbUpdateSlaveL1 = 1;
                dblocalAffinity = 0;
            }
            if (dbExptime == exptime && dbForceWriteAll == forceWriteAll && dbUpdateSlaveL1 == updateSlaveL1 && dblocalAffinity == localAffinity && dbFlag == flag) {
                return returnRes(400, "相同数据不需要修改");
            }
            if(dbExptime!=exptime){
                resource.put("exptime",exptime);
            }
            if(dbForceWriteAll!=forceWriteAll){
                resource.put("forceWriteAll",forceWriteAll);
            }
            if (dbUpdateSlaveL1 != updateSlaveL1) {
                resource.put("updateSlaveL1", updateSlaveL1);
            }
            if (dblocalAffinity != localAffinity) {
                resource.put("localAffinity", localAffinity);
            }
            if (dbFlag != flag) {
                resource.put("flag", flag);
            }
            ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(resource, namespace);
            List<IdcInfo> idcInfoList = buildIdcInfo(idcInfos);

            String yaml = resourceService.generatorYaml(id, resourceAccessSetting, idcInfoList);
            JSONArray jsonArray = JSONArray.fromObject(yaml);
            String result = batchAssign(jsonArray, idcInfos, id);
            if (result.contains("400") && result.contains("failed")) {
                return result;
            }
        } catch (Exception e) {
            ApiLogger.warn("update config error:"+e.getMessage());
            return returnRes(400,"failed");
        }finally {
            ThreadLocalUtil.clear();
        }
        return returnRes(200,"success");
    }

    // 判断一个端口是否在clustermanager中
    @Path("/config/exist/{port}")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public String isCacheService(
            @PathParam("port") String port) {
        boolean result = resources.containsKey(port);
        if (result) {
            return returnRes(200,"exist");
        } else {
            return returnRes(404,"not exist");
        }
    }

    // 下线一个资源
    @Path("/config/{port}")
    @DELETE
    @Produces(MediaType.APPLICATION_JSON)
    public String offlineConfig(
            @PathParam("port") String port,
            String ips
    ) {
        ApiLogger.info("offline param,port:"+port+",ips:"+ips);
        if (StringUtils.isBlank(port)) {
            return returnRes(400,"port is invalid");
        }
        if (!isExist(port)) {
            ApiLogger.info("offline-port is not exist");
            return returnRes(200,"success");
        }
        delLock.lock();
        try {
            if (StringUtils.isBlank(ips) || !ips.contains("ips")) {//直接删除
                Set<Integer> ids = resources.get(port);
                for (Integer id : ids) {
                    String delRs = resourceService.delResource(id);
                    if (delRs.contains("false") || delRs.contains("code")) {
                        return returnRes(400,"failed");
                    }
                }
                resources.remove(port);
            } else {
                try {
                    JSONObject jb = JSONObject.fromObject(ips);
                    JSONArray ja = jb.getJSONArray("ips");
                    Set<Integer> ids = resources.get(port);
                    for (Integer id : ids) {
                        String resourceInfo = resourceService.getResource(id);
                        JSONObject resource = JSONObject.fromObject(resourceInfo);
                        String namespace = resource.getString("biz");
                        String idcInfos = resource.getString("idcInfos");
                        for (int i = 0; i< ja.size();i++) {
                            String ip = ja.getString(i);
                            idcInfos = idcInfos.replaceAll(ip+":"+port,"=").replaceAll("=,","").replaceAll(",=","");
                            idcInfos = idcInfos.replaceAll("=","");//仅仅为了简单，没啥语义
                        }
                        ApiLogger.info("idcInfos:" + idcInfos);
                        String hash = resource.getString("hash");
                        String distribution = resource.getString("distribution");
                        int saveTag = resource.getInt("saveTag");
                        int autoEjectHosts = resource.getInt("autoEjectHosts");
                        int timeout = resource.getInt("timeout");
                        int serverRetryTimeout = resource.getInt("serverRetryTimeout");
                        int serverFailureLimit = resource.getInt("serverFailureLimit");
                        int resourceType = resource.getInt("resourceType");
                        int partialReply = resource.getInt("partialReply");
                        long exp = resource.getLong("exptime");
                        int forceWriteAll = CaptainUtil.getForceWriteAll(resource);
                        int updateSlaveL1 = CaptainUtil.getUpdateSlaveL1(resource);
                        int localAffinity = CaptainUtil.getlocalAffinity(resource);
                        int flag = CaptainUtil.getFlag(resource);
                        ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(hash, distribution, namespace, saveTag, autoEjectHosts, timeout, serverRetryTimeout, serverFailureLimit, resourceType, partialReply, exp, forceWriteAll, updateSlaveL1, localAffinity, flag);
                        List<IdcInfo> idcInfoList = buildIdcInfo(idcInfos);
                        for (IdcInfo idcInfo : idcInfoList) { //修正masterL1Blocks
                            String masterL1Ips = idcInfo.getMasterL1Ips();
                            int masterL1Blocks = 0;
                            if (!masterL1Ips.equals("")) {
                                masterL1Blocks = masterL1Ips.split("#").length;
                            }
                            idcInfo.setMasterL1Block(masterL1Blocks);
                        }
                        String yaml = resourceService.generatorYaml(id,resourceAccessSetting,idcInfoList);
                        JSONArray jsonArray = JSONArray.fromObject(yaml);
                        String result = batchAssign(jsonArray,idcInfoList,id);
                        if (result.contains("400") && result.contains("failed")) {
                            return result;
                        }
                    }
                } catch (Exception e) {
                    ApiLogger.warn("delete error, e:" + e.getMessage());
                    return returnRes(400,"failed");
                }
            }
        } finally {
            delLock.unlock();
            ThreadLocalUtil.clear();
        }
        return returnRes(200,"success");
    }

    //资源扩容，通常包含扩容L1+扩机房
    @Path("/config/{port}/expand")
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    public String expandConfig(
            @PathParam("port") String port,
            String configStr
    ) {
        try {
            ApiLogger.info("expand param,port:" + port + ",configStr:" + configStr);
            if (!isExist(port)) {
                return returnRes(400, "port isn't exist");
            }
            try {
                JSONObject jsonObject = JSONObject.fromObject(configStr);
                JSONArray idcs = jsonObject.getJSONArray("idcs");
                if (!isExist(idcs)) {
                    return returnRes(400, "the idclist is invalid");
                }
                Set<Integer> ids = resources.get(port);
                for (Integer id : ids) {
                    String resourceInfo = resourceService.getResource(id);
                    JSONObject resource = JSONObject.fromObject(resourceInfo);
                    String namespace = resource.getString("biz");
                    String idcInfos = resource.getString("idcInfos");
                    List<IdcInfo> idcInfoList = buildIdcInfo(idcInfos);
                    HashMap<String, String> waitingExpand = getWaitingOperInfo(idcs, port);
                    for (IdcInfo idcInfo : idcInfoList) {
                        String idc = idcInfo.getIdc();
                        if (waitingExpand.containsKey(idc)) { //如果包含该idc，则该idc一定不能有复用属性。
                            if (StringUtils.isNotBlank(idcInfo.getReuseIdc())) {
                                return returnRes(400, "the " + idc + " reuse the " + idcInfo.getReuseIdc());
                            }
                            String masterL1Ips = idcInfo.getMasterL1Ips();
                            String waitingExpandMasterIps = waitingExpand.get(idc);
                            if (masterL1Ips.contains(waitingExpandMasterIps)) {
                                continue;
                            }
                            if (!masterL1Ips.equals("")) {
                                masterL1Ips += "#";
                            }
                            masterL1Ips += waitingExpandMasterIps;
                            int masterL1Blocks = masterL1Ips.split("#").length;
                            idcInfo.setMasterL1Ips(masterL1Ips);
                            idcInfo.setMasterL1Block(masterL1Blocks);
                            doReuse(idcInfoList, idc, masterL1Ips, masterL1Blocks);
                            for (IdcInfo tmpIdcInfo : idcInfoList) { //判断同步机房
                                String slaveL1Idc = tmpIdcInfo.getSlaveL1Idc();
                                if (slaveL1Idc.indexOf(idc) != -1) {
                                    String slaveL1Ips = tmpIdcInfo.getSlaveL1Ips();
                                    if (!slaveL1Ips.equals("")) {
                                        slaveL1Ips += "#";
                                    }
                                    slaveL1Ips += waitingExpand.get(idc);
                                    tmpIdcInfo.setSlaveL1Ips(slaveL1Ips);
                                    for (IdcInfo tmpidc : idcInfoList) { //判断复用
                                        String reuseIdc = tmpidc.getReuseIdc();
                                        if (reuseIdc.equals(tmpIdcInfo.getIdc())) {
                                            tmpidc.setSlaveL1Ips(slaveL1Ips);
                                        }
                                    }

                                }
                            }
                        }
                    }
                    // Todo: 增加一个机房
                    ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(resource, namespace);
                    String yaml = resourceService.generatorYaml(id, resourceAccessSetting, idcInfoList);
                    JSONArray jsonArray = JSONArray.fromObject(yaml);

                    String result = batchAssign(jsonArray, idcInfoList, id);
                    if (result.contains("400") && result.contains("failed")) {
                        return result;
                    }
                }

            } catch (Exception e) {
                ApiLogger.warn("update config error:" + e.getMessage());
                return returnRes(400, "failed");
            }
            return returnRes(200, "success");
        }catch (Exception e) {
            ApiLogger.warn("expandConfig error:" + e.getMessage());
            return returnRes(400, "failed");
        }finally {
           ThreadLocalUtil.clear();
        }
    }

    @Path("/config/{port}/reduce")
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    public String reduceConfig(
            @PathParam("port") String port,
            String configStr
    ) {
        ApiLogger.info("[reduceConfig] 方法入口, port: " + port + ", configStr: " + configStr);
        if (!isExist(port)) {
            ApiLogger.warn("[reduceConfig] port 不存在, port: " + port);
            return returnRes(400, "port isn't exist");
        }
        try {
            JSONObject jsonObject = JSONObject.fromObject(configStr);
            ApiLogger.info("[reduceConfig] 解析configStr成功, jsonObject: " + jsonObject.toString());
            JSONArray idcs = jsonObject.getJSONArray("idcs");
            ApiLogger.info("[reduceConfig] 获取idcs, idcs: " + idcs.toString());
            if (!isExist(idcs)) {
                ApiLogger.warn("[reduceConfig] idclist无效, idcs: " + idcs.toString());
                return returnRes(400, "the idclist is invalid");
            }
            Set<Integer> ids = resources.get(port);
            ApiLogger.info("[reduceConfig] 获取到资源id集合, port: " + port + ", ids: " + ids);
            for (Integer id : ids) {
                String resourceInfo = resourceService.getResource(id);
                ApiLogger.info("[reduceConfig] 获取资源信息, id: " + id + ", resourceInfo: " + resourceInfo);
                JSONObject resource = JSONObject.fromObject(resourceInfo);
                String namespace = resource.getString("biz");
                String idcInfos = resource.getString("idcInfos");
                ApiLogger.info("[reduceConfig] 解析idcInfos, id: " + id + ", idcInfos: " + idcInfos);
                List<IdcInfo> idcInfoList = buildIdcInfo(idcInfos);
                ApiLogger.info("[reduceConfig] 构建idcInfoList, id: " + id + ", idcInfoList.size: " + idcInfoList.size());
                HashMap<String,Set<String>> waitingReduce = getWaitingReduceInfo(idcs);
                ApiLogger.info("[reduceConfig] waitingReduce信息: " + waitingReduce);
                //HashMap<String, String> waitingReduce = getWaitingOperInfo(idcs, port);
                for (IdcInfo idcInfo : idcInfoList) {
                    String idc = idcInfo.getIdc();
                    ApiLogger.info("[reduceConfig] 处理idc: " + idc);
                    if (waitingReduce.containsKey(idc)) { //如果包含该idc，则该idc一定不能有复用属性。
                        if (StringUtils.isNotBlank(idcInfo.getReuseIdc())) {
                            ApiLogger.info("[reduceConfig] idc复用, 跳过, idc: " + idc + ", reuseIdc: " + idcInfo.getReuseIdc() + ", port: " + port);
                            continue;
                        }
                        String masterL1Ips = idcInfo.getMasterL1Ips();
                        ApiLogger.info("[reduceConfig] idc: " + idc + ", 原始masterL1Ips: " + masterL1Ips);
                        if (!masterL1Ips.equals("")) {
                            Set<String> waitings = waitingReduce.get(idc);
                            ApiLogger.info("[reduceConfig] idc: " + idc + ", 需要缩容的masterL1Ips: " + waitings);
                            int count = 0; //count用来计算wating不在masterL1Ips的数量
                            for (String waiting : waitings) {
                                String []masterL1IpList = masterL1Ips.split("#");
                                boolean contain = false;
                                for (int i=0; i< masterL1IpList.length;i++) {
                                    if (masterL1IpList[i].equals(waiting)) {
                                        contain = true;
                                        break;
                                    }
                                }
                                if (contain) {
                                    ApiLogger.info("[reduceConfig] idc: " + idc + ", masterL1Ip: " + waiting + " 命中, 进行替换");
                                    masterL1Ips = masterL1Ips.replaceAll(waiting,"=");//仅仅为了好处理
                                    masterL1Ips = masterL1Ips.replaceAll("#=","").replaceAll("=#","");
                                } else {
                                    ApiLogger.info("[reduceConfig] idc: " + idc + ", masterL1Ip: " + waiting + " 未命中, 不处理");
                                    count ++;
                                }
                            }
                            ApiLogger.info("[reduceConfig] idc: " + idc + ", masterL1Ips缩容后: " + masterL1Ips + ", 未命中数量: " + count);
                            if(count == waitings.size()){
                                ApiLogger.info("[reduceConfig] idc: " + idc + ", 所有待缩容IP均未命中, 跳过");
                                continue;
                            }

                        } else {
                            ApiLogger.info("[reduceConfig] idc: " + idc + ", masterL1Ips为空, 跳过");
                            continue;
                        }
                        int masterL1Blocks = 0;
                        if (!masterL1Ips.equals("=")) {//替换完
                            masterL1Blocks = masterL1Ips.split("#").length;
                        } else {
                           masterL1Ips="";
                        }
                        ApiLogger.info("[reduceConfig] idc: " + idc + ", 设置masterL1Ips: " + masterL1Ips + ", masterL1Blocks: " + masterL1Blocks);
                        idcInfo.setMasterL1Ips(masterL1Ips);
                        idcInfo.setMasterL1Block(masterL1Blocks);
                        doReuse(idcInfoList,idc,masterL1Ips,masterL1Blocks);
                        for (IdcInfo tmpIdcInfo : idcInfoList) { //判断同步机房
                            String slaveL1Idc = tmpIdcInfo.getSlaveL1Idc();
                            if (slaveL1Idc.indexOf(idc) != -1) {
                                String slaveL1Ips = tmpIdcInfo.getSlaveL1Ips();
                                Set<String> waitings = waitingReduce.get(idc);
                                ApiLogger.info("[reduceConfig] 同步机房处理, idc: " + idc + ", slaveL1Idc: " + slaveL1Idc + ", 原始slaveL1Ips: " + slaveL1Ips);
                                for (String waiting : waitings) {
                                    slaveL1Ips = slaveL1Ips.replaceAll(waiting,"=");
                                    slaveL1Ips = slaveL1Ips.replaceAll("#=","").replaceAll("=#","");
                                }
                                if (slaveL1Ips.equals("=")) {
                                    slaveL1Ips="";
                                }
                                ApiLogger.info("[reduceConfig] 同步机房处理后, idc: " + idc + ", slaveL1Ips: " + slaveL1Ips);
                                tmpIdcInfo.setSlaveL1Ips(slaveL1Ips);
                                for (IdcInfo tmpidc : idcInfoList) { //判断复用
                                    String reuseIdc = tmpidc.getReuseIdc();
                                    if (reuseIdc.equals(tmpIdcInfo.getIdc())) {
                                        tmpidc.setSlaveL1Ips(slaveL1Ips);
                                        ApiLogger.info("[reduceConfig] 同步机房复用, idc: " + idc + ", reuseIdc: " + reuseIdc + ", slaveL1Ips: " + slaveL1Ips);
                                    }
                                }
                            }
                        }
                    }
                }

                // Todo: 删除一个机房
                // 1. 删除的机房必须在当前机房列表中 2. 删除的机房不能有其他机房复用 3. 删除的机房不能有被其他机房同步

                ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(resource, namespace);
                ApiLogger.info("[reduceConfig] 构建ResourceAccessSetting, id: " + id + ", namespace: " + namespace);
                String yaml = resourceService.generatorYaml(id, resourceAccessSetting, idcInfoList);
                ApiLogger.info("[reduceConfig] 生成yaml, id: " + id + ", yaml: " + yaml);
                JSONArray jsonyaml = JSONArray.fromObject(yaml);
                String result = batchAssign(jsonyaml,idcInfoList,id);
                ApiLogger.info("[reduceConfig] batchAssign结果, id: " + id + ", result: " + result);
                if (result.contains("400") && result.contains("failed")) {
                    ApiLogger.warn("[reduceConfig] batchAssign失败, id: " + id + ", result: " + result);
                    return result;
                }
            }

        } catch (Exception e) {
            ApiLogger.warn("[reduceConfig] update config error, port: " + port + ", configStr: " + configStr + ", 异常: " + e.getMessage(), e);
            return returnRes(400, "failed");
        }finally {
            ThreadLocalUtil.clear();
        }
        ApiLogger.info("[reduceConfig] 缩容操作成功, port: " + port);
        return returnRes(200, "success");
    }

    @Path("/config/{iid}/roles")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public String reduceConfig(
            @PathParam("iid") String iid
    ) {
        ApiLogger.info("get roles,iid:"+iid);
        try {
            String port = iid.split(":")[1];
            if (!resources.containsKey(port)) {
                return returnRes(400,"get roles error, port:"+port+"isn't exist");
            }
            Set<String> roles = new HashSet<String>(4);
            for (int id : resources.get(port)) {
                String resourceInfo = resourceService.getResource(id);
                JSONObject resource = JSONObject.fromObject(resourceInfo);
                String idcInfos = resource.getString("idcInfos");
                List<IdcInfo> idcInfoList = buildIdcInfo(idcInfos);
                for (IdcInfo idcInfo : idcInfoList) {
                    String masterIps = idcInfo.getMasterIps();
                    if (masterIps.contains(iid)) {
                        roles.add("master");
                    }
                    String slaveIps = idcInfo.getSlaveIps();
                    if (slaveIps.contains(iid)) {
                        roles.add("slave");
                    }
                    String masterL1Ips = idcInfo.getMasterL1Ips();
                    if (masterL1Ips.contains(iid)) {
                        roles.add("master_l1");
                    }
                    String slaveL1Ips = idcInfo.getSlaveL1Ips();
                    if (slaveL1Ips.contains(iid)) {
                        roles.add("slave_l1");
                    }
                    if (roles.size() == 4) {//目前仅支持4个
                        break;
                    }
                }
            }
            String rs = StringUtils.join(roles.toArray(),",");
            JSONObject jsonResult = new JSONObject();
            jsonResult.put("code",200);
            jsonResult.put("roles",rs);
            return jsonResult.toString();
        } catch (Exception e) {
            ApiLogger.warn("get roles error:" + e.getMessage());
            return returnRes(400, "get roles error");
        }
    }

    @Path("/{resource}/{port}/{role}")
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    public String expandConfig(
            @PathParam("port") String port,
            @PathParam("resource") String resource,
            @PathParam("role") String role,
            String configStr) {
        ApiLogger.info("resource:"+resource+" expand config param:"+port+",configStr:"+configStr);
        if (!resourceList.contains(resource)) {
            return returnRes(400,"the resource: "+resource+" isn't exist");
        }
        if (!isExist(port)) {
            return returnRes(400, "the port:"+port+" isn't exist");
        }
        if (!roleList.contains(role)) {
            return returnRes(400, "the role:" + role +" isn't exist");
        }
        if (resource.equals("memcached") && !role.equals("masterL1")) {
            return returnRes(400,"memcached only support masterL1 expand");
        }

        try {
            for (int id : resources.get(port)) {
                String resourceInfo = resourceService.getResource(id);
                JSONObject res = JSONObject.fromObject(resourceInfo);
                String captain_namespace = res.getString("biz");
                String captain_type = res.getString("type");
                JSONObject jsonObject = JSONObject.fromObject(configStr);
                String param_group = jsonObject.getString("group");
                String param_namespace = jsonObject.getString("namespace");
                String param_type = getType(param_group);
                String param_idc = getIdc(param_group);
                if (!allIdcs.contains(param_idc)) {
                    return returnRes(400, "the param idc:" + param_idc +" isn't valid");
                }
                if (captain_namespace.equals(param_namespace) && captain_type.equals(param_type)) {
                    String idcInfos = res.getString("idcInfos");
                    JSONArray ins = jsonObject.getJSONArray("instances");
                    List<IdcInfo> idcInfoList = buildIdcInfo(idcInfos);
                    for (IdcInfo idcInfo : idcInfoList) {
                        String idc = idcInfo.getIdc();
                        if (!idc.equals(param_idc)) {
                            continue;
                        }
                        if (StringUtils.isNotBlank(idcInfo.getReuseIdc())) {
                            ApiLogger.info("the "+ idc+ " reuse the " + idcInfo.getReuseIdc()+",port:"+port);
                            continue;
                        }
                        if (role.equals("master")) {
                            String masterIps = idcInfo.getMasterIps();
                            if (StringUtils.isNotBlank(masterIps)) {
                                masterIps += ",";
                            }
                            String[] insArr = (String[]) JSONArray.toArray(ins, String.class);
                            for (int i=0;i<ins.size();i++) {
                                String iid = insArr[i];
                                masterIps += iid + ",";
                            }
                            if (masterIps.endsWith(",")) {
                                masterIps = masterIps.substring(0,masterIps.length()-1);
                            }
                            idcInfo.setMasterIps(masterIps);
                            doReuse(idcInfoList,idc,masterIps);
                        }
                        if (role.equals("masterL1")) {
                            String[] insArr = (String[]) JSONArray.toArray(ins, String.class);
                            if (insArr.length != 5) {
                                return returnRes(400, "the length of the mc expansion must 5");
                            }
                            String masterL1Ips = idcInfo.getMasterL1Ips();
                            if (StringUtils.isNotBlank(masterL1Ips)) {
                                masterL1Ips += "#";
                            }
                            String waitingIps = "";
                            for (int j=0;j<insArr.length;j++) {
                                waitingIps += insArr[j] + ",";
                            }
                            if (StringUtils.isNotBlank(waitingIps)) {
                                waitingIps = waitingIps.substring(0,waitingIps.length()-1);
                            }
                            if (masterL1Ips.indexOf(waitingIps) == -1) {
                                for(int i=0;i<insArr.length;i++) {
                                    String iid = insArr[i];
                                    masterL1Ips += iid + ",";
                                }
                                if (masterL1Ips.endsWith(",")) {
                                    masterL1Ips = masterL1Ips.substring(0,masterL1Ips.length()-1);
                                }
                                idcInfo.setMasterL1Ips(masterL1Ips);
                                idcInfo.setMasterL1Block(masterL1Ips.split("#").length);
                                doReuse(idcInfoList,idc,masterL1Ips,masterL1Ips.split("#").length);
                            }
                        }
                    }
                    ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(res, param_namespace);
                    String yaml = resourceService.generatorYaml(id, resourceAccessSetting, idcInfoList);
                    JSONArray jsonyaml = JSONArray.fromObject(yaml);
                    String result = batchAssign(jsonyaml,idcInfoList,id);
                    if (result.contains("400") && result.contains("failed")) {
                        return result;
                    }
                }
            }
        } catch (Exception e) {
            ApiLogger.warn("resource:" + resource +",port:"+port+",role:"+role+" expand config error:" + e.getMessage());
            return returnRes(400, "expand config error");
        }finally {
            ThreadLocalUtil.clear();
        }
        return returnRes(200,"success");

    }

    @Path("/{resource}/{port}/{role}")
    @DELETE
    @Produces(MediaType.APPLICATION_JSON)
    public String reduceConfig(
            @PathParam("port") String port,
            @PathParam("resource") String resource,
            @PathParam("role") String role,
            String configStr) {
        ApiLogger.info("resource:"+resource+" reduce config param:"+port+",configStr:"+configStr);
        if (!resourceList.contains(resource)) {
            return returnRes(400,"the resource: "+resource+" isn't exist");
        }
        if (!isExist(port)) {
            return returnRes(400, "the port:"+port+" isn't exist");
        }
        if (!roleList.contains(role)) {
            return returnRes(400, "the role:" + role +" isn't exist");
        }
        if (resource.equals("memcached")&&!role.equals("masterL1")) {
            return returnRes(400,"memcached only support masterL1 reduce");
        }

        try {
            for (int id : resources.get(port)) {
                String resourceInfo = resourceService.getResource(id);
                JSONObject res = JSONObject.fromObject(resourceInfo);
                String captain_namespace = res.getString("biz");
                String captain_type = res.getString("type");
                JSONObject jsonObject = JSONObject.fromObject(configStr);
                String param_group = jsonObject.getString("group");
                String param_namespace = jsonObject.getString("namespace");
                String param_type = getType(param_group);
                String param_idc = getIdc(param_group);
                if (!allIdcs.contains(param_idc)) {
                    return returnRes(400, "the param idc:" + param_idc +" isn't valid");
                }
                if (captain_namespace.equals(param_namespace) && captain_type.equals(param_type)) {
                    String idcInfos = res.getString("idcInfos");
                    JSONArray ins = jsonObject.getJSONArray("instances");
                    List<IdcInfo> idcInfoList = buildIdcInfo(idcInfos);
                    for (IdcInfo idcInfo : idcInfoList) {
                        String idc = idcInfo.getIdc();
                        if (!idc.equals(param_idc)) {
                            continue;
                        }
                        String[] insArr = (String[]) JSONArray.toArray(ins, String.class);
                        if (role.equals("master")) {
                            String masterIps = idcInfo.getMasterIps();
                            String []masterArr = masterIps.split(",");
                            StringBuilder ips = new StringBuilder();
                            for (int i=0;i<masterArr.length;i++) {
                                String ip = masterArr[i];
                                if (!contains(insArr,ip)) {
                                    ips.append(ip);
                                    ips.append(",");
                                }
                            }
                            masterIps = ips.toString();
                            if (masterIps.endsWith(",")) {
                                masterIps = masterIps.substring(0,masterIps.length()-1);
                            }
                            if (masterIps.equals("")) {
                                return returnRes(400, "master ip can not be empty");
                            }
                            idcInfo.setMasterIps(masterIps);
                            doReuse(idcInfoList,idc,masterIps);
                        }
                        if (role.equals("masterL1")) {
                            if (insArr.length != 5) {
                                return returnRes(400, "the length of the mc reduce must 5");
                            }
                            String masterL1Ips = idcInfo.getMasterL1Ips();
                            String []masterL1Arr = masterL1Ips.split("#");
                            String waitingIps = "";
                            for (int j=0;j<insArr.length;j++) {
                                waitingIps += insArr[j] + ",";
                            }
                            if (StringUtils.isNotBlank(waitingIps)) {
                                waitingIps = waitingIps.substring(0,waitingIps.length()-1);
                            }
                            StringBuilder ips = new StringBuilder();
                            for (int i=0;i<masterL1Arr.length;i++) {
                                String ip = masterL1Arr[i];
                                if (!waitingIps.equals(ip)) {
                                    ips.append(ip);
                                    ips.append("#");
                                }
                            }
                            masterL1Ips = ips.toString();
                            if (masterL1Ips.endsWith("#")) {
                                masterL1Ips = masterL1Ips.substring(0,masterL1Ips.length()-1);
                            }
                            idcInfo.setMasterL1Ips(masterL1Ips);
                            if (masterL1Ips.isEmpty()) {
                                idcInfo.setMasterL1Block(0);
                                doReuse(idcInfoList,idc,masterL1Ips,0);
                            } else {
                                idcInfo.setMasterL1Block(masterL1Ips.split("#").length);
                                doReuse(idcInfoList,idc,masterL1Ips,masterL1Ips.split("#").length);
                            }

                        }
                    }
                    ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(res, param_namespace);
                    String yaml = resourceService.generatorYaml(id, resourceAccessSetting, idcInfoList);
                    JSONArray jsonyaml = JSONArray.fromObject(yaml);
                    String result = batchAssign(jsonyaml,idcInfoList,id);
                    if (result.contains("400") && result.contains("failed")) {
                        return result;
                    }
                }
            }
        } catch (Exception e) {
            ApiLogger.warn("resource:" + resource +",port:"+port+",role:"+role+" reduce config error:" + e.getMessage());
            return returnRes(400, "reduce config error");
        }finally {
            ThreadLocalUtil.clear();
        }
        return returnRes(200,"success");

    }

    private String batchAssign(JSONArray jsonyaml,Object idcInfoList,int id) {
        for (int i = 0; i < jsonyaml.size(); i++) {
            JSONObject obj = jsonyaml.getJSONObject(i);
            String group = obj.getString("group");
            String config = obj.getString("content");
            String idcInfo = getIdcInfo(idcInfoList, group);
            String assignResult = null;
            try {
                ApiLogger.info("batchAssign, group: " + group + ", id:" + id + ", currentIndex:" + i);
                assignResult = resourceService.assignResource(id, group, config, USER, idcInfo);
            } catch (Exception e) {
                ApiLogger.warn("batchAssign exception, group:" + group + ", id:" + id +
                        "jsonyamlSize:" + jsonyaml.size()+ ", currentIndex:" + i + ", exception:" + e.getMessage());
                throw new RuntimeException(e);
            }
            ApiLogger.info("assign result,id:" + id +"assignResult:" + assignResult);
            if (assignResult.contains("false") || assignResult.contains("code")) {
                ApiLogger.warn("assign error:" + assignResult);
                return returnRes(400, "failed");
            }
        }
        return returnRes(200, "success");
    }

    private boolean isExist(String port) {
        if (resources.containsKey(port)) {
            return true;
        }
        return false;
    }


    private String returnRes(int code,String message) {
        JSONObject jsonResult = new JSONObject();
        jsonResult.put("code", code);
        jsonResult.put("message", message);
        return jsonResult.toString();
    }

    // 创建服务池，如果已存在，则修改相关机器权限
    private String createServicePool(String biz, int proxyStateValue, String configAddr, String[] idcArray) {
        return resourceService.addService(biz, proxyStateValue, configAddr, idcArray);
    }

    //融合之前的apply，getYaml,assign等操作
    private String processAssign(String biz, String namespace, JSONArray idclist, long exptime, int forceWriteAll, int updateSlaveL1, int localAffinity, int flag, String port) {
        if (resourceService.isExist(biz, namespace)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_SERVICE_ID_EXISTS, "the resource already exists"));
        }
        float hitpercent = 0.0f;
        int readTps = 100;
        int writeTps = 100;
        String hashStrategy = "crc32";
        String distribution = "modula";
        int saveTag = 0;
        int autoEjectHosts = 0;
        long timeout = 300;
        long serverRetryTimeout = 10000;
        int serverFailureLimit = 2;
        int resourceType = 0;
        int partialReply = 1;
        RecordInfo recordInfo = buildRecordInfo(biz, namespace, hitpercent, readTps, writeTps, USER, 0);
        ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(hashStrategy, distribution, namespace, saveTag, autoEjectHosts, timeout, serverRetryTimeout, serverFailureLimit, resourceType, partialReply, exptime, forceWriteAll, updateSlaveL1, localAffinity, flag);

        List<IdcInfo> idcInfos = new ArrayList<IdcInfo>(17);//目前最多17个idc
        for (int i = 0;i < idclist.size(); i++) {
            JSONObject jb = idclist.getJSONObject(i);
            String checkResult = checkIdcs(jb);
            if (checkResult.contains("code")) {
                ApiLogger.warn("idcs err,result:" + checkResult);
                return checkResult;
            }
            String idc = jb.getString("id");
            String reuseIdc = jb.getString("reuseIdc");
            String slaveIdc = jb.getString("slaveIdc");
            JSONObject master = jb.getJSONObject("master");
            JSONObject slave = jb.getJSONObject("slave");
            JSONObject masterL1 = jb.getJSONObject("masterL1");
            JSONArray slaveL1 = jb.getJSONArray("slaveL1");
            if (!reuseIdc.equals("") && !allIdcs.contains(reuseIdc)) {
                return new JsonBuilder().append("code", 400)
                        .append("desc","the idc:"+ idc +" reuseIdc:" + reuseIdc + " value invalid" ).flip().toString();
            }
            if (!slaveIdc.equals("") && !slaveIdc.equals("-1") && !allIdcs.contains(slaveIdc)) {
                return new JsonBuilder().append("code", 400)
                        .append("desc","the idc:"+ idc +" slaveIdc:" + slaveIdc + " value invalid" ).flip().toString();
            }
            String masterIps = buildBaseIps(master,port, "m");
            String slaveIps = buildBaseIps(slave,port,"s");
            int masterMem = buildCap(master,"m");
            if (masterMem == -1) {
                return new JsonBuilder().append("code", 400)
                        .append("desc","the idc:"+ idc +"'s master memory_mb invalid" ).flip().toString();
            }
            String masterL1Ips = buildMasterL1Ips(masterL1,port,idc,"");
            if (masterL1Ips.contains("code")) {
                return masterL1Ips;
            }
            int masterL1Mem = 0;
            if (!StringUtils.isBlank(masterL1Ips)) {
                masterL1Mem = buildCap(masterL1,"m_l1");
                if (masterL1Mem == -1) {
                    return new JsonBuilder().append("code", 400)
                            .append("desc","the idc:"+ idc +"'s masterL1 memory_mb invalid" ).flip().toString();
                }
            }
            int masterL1Block = 0;
            if (!StringUtils.isBlank(masterL1Ips)) {
                masterL1Block = masterL1.getInt("blocks");
            }
            String slaveL1Ips = buildSlaveL1Ips(slaveL1,masterIps,masterL1Ips,port,slaveIdc);
            if (slaveL1Ips.contains("code")) {
                return slaveL1Ips;
            }

            String slaveL1Idc = getSlaveL1Idc(slaveL1);
            IdcInfo idcInfo = buildIdc(idc,biz,masterMem/1024/1024,masterL1Mem/1024/1024,masterL1Block,0,0,slaveIdc,slaveL1Idc,masterIps,masterL1Ips,slaveIps,slaveL1Ips,reuseIdc);
            idcInfos.add(idcInfo);
        }
        recordInfo.setState(Integer.parseInt(ResourceState.APPLY_STATE.toString()));
        String idStr = resourceService.applyResource(recordInfo, resourceAccessSetting, idcInfos);
        JSONObject jsonObject = JSONObject.fromObject(idStr);
        if (idStr.contains("code")) {//失败，只能根据以前的设计这么来啦。。。。
            return idStr;
        }
        String id = jsonObject.getString("result");
        String configString = resourceService.generatorYaml(Integer.parseInt(id), resourceAccessSetting, idcInfos);
        if (configString.contains("code")) {
            return configString;
        }
        // configString为一个json数组，每个里面包含当前对应的group及configstr，因此解析出对应的group及configstr执行分配即可。
        JSONArray jsonArray = JSONArray.fromObject(configString);
        for (int i=0;i<jsonArray.size();i++) {
            JSONObject ob = jsonArray.getJSONObject(i);
            String group = ob.getString("group");
            String config = ob.getString("content");
            String assignResult = resourceService.assignResource(Integer.parseInt(id),group,config,USER,"");
            if (assignResult.contains("code")) {
                return assignResult;
            }
        }
        String deployResult = resourceService.deployResource(USER,Integer.parseInt(id));
        if (deployResult.contains("code")) {
            return deployResult;
        }
        return "ok";
    }

    private String checkIdcs(JSONObject jb) {
        JSONObject jsonResult = new JSONObject();
        if (!jb.containsKey("id")) {
            jsonResult.put("code",400);
            jsonResult.put("desc","the key id for idcs not exist");
            return jsonResult.toString();
        }
        if (!jb.containsKey("reuseIdc")) {
            jsonResult.put("code",400);
            jsonResult.put("desc","the key reuseIdc for idcs not exist");
            return jsonResult.toString();
        }
        if (!jb.containsKey("slaveIdc")) {
            jsonResult.put("code",400);
            jsonResult.put("desc","the key slaveIdc for idcs not exist");
            return jsonResult.toString();
        }
        if (!jb.containsKey("master")) {
            jsonResult.put("code",400);
            jsonResult.put("desc","the key master for idcs not exist");
            return jsonResult.toString();
        }
        if (!jb.containsKey("slave")) {
            jsonResult.put("code",400);
            jsonResult.put("desc","the key slave for idcs not exist");
            return jsonResult.toString();
        }
        if (!jb.containsKey("masterL1")) {
            jsonResult.put("code",400);
            jsonResult.put("desc","the key masterL1 for idcs not exist");
            return jsonResult.toString();
        }
        if (!jb.containsKey("slaveL1")) {
            jsonResult.put("code",400);
            jsonResult.put("desc","the key slaveL1 for idcs not exist");
            return jsonResult.toString();
        }
        return "ok";
    }

    // 构建master/slaveip
    private String buildBaseIps (JSONObject jb, String port, String role) {
        StringBuilder sb = new StringBuilder();
        boolean containsIp = jb.containsKey("ips");
        boolean isnull = false;
        if (containsIp) {
            Object jsonIps = jb.get("ips");
            isnull = "null".equals(jsonIps.toString());
        }
        if (!containsIp || isnull) {
            if ("m".equals(role)) {
                JSONObject jsonResult = new JSONObject();
                jsonResult.put("code",400);
                jsonResult.put("desc","the master value of idcs invalid");
                return jsonResult.toString();
            } else {
                return sb.toString();
            }
        }

        JSONArray ips = jb.getJSONArray("ips");
        String[] ipArr = (String[]) JSONArray.toArray(ips, String.class);
        for (int i = 0; i < ipArr.length; i++) {
            String ip = ipArr[i] + ":" + port;
            sb.append(ip);
            sb.append(",");
        }
        String result = sb.toString();
        if (result.endsWith(",")) {
            result = result.substring(0,result.length()-1);
        }
        return result;
    }

    private int buildCap (JSONObject jb, String role) {
        boolean containsIp = jb.containsKey("ips");
        boolean isnull = false;
        if (containsIp) {
            Object jsonIps = jb.get("ips");
            isnull = "null".equals(jsonIps.toString());
        }
        if (!containsIp || isnull) {
            if (role.equals("m_l1")) {
                return 0;
            }
        }
        JSONArray ips = jb.getJSONArray("ips");
        boolean containsM = jb.containsKey("memory_mb");
        if (!containsM) {
            return -1;
        }
        int memory_mb = jb.getInt("memory_mb");
        return memory_mb * ips.size();
    }

    private String buildMasterL1Ips (JSONObject jb, String port, String idc, String veri) { //先加个特殊字段标明缩容时不用穿mem,后面再改逻辑
        StringBuilder sb = new StringBuilder();
        boolean containsIps = jb.containsKey("ips");
        boolean isnull = false;
        if (containsIps) {
            Object jsonIps = jb.get("ips");
            isnull = "null".equals(jsonIps.toString());
        }
        if (!containsIps || isnull) { //认为没有masterL1
            return sb.toString();
        }
        JSONArray ips = jb.getJSONArray("ips");
        String[] ipArr = (String[]) JSONArray.toArray(ips, String.class);
        if(ipArr.length==0){//认为没有masterL1，兼容ips[]格式的空
            return sb.toString();
        }
        boolean containsBlocks = jb.containsKey("blocks");
        boolean containsM = jb.containsKey("memory_mb");
        if (!containsBlocks) {
            return new JsonBuilder().append("code", 400)
                    .append("desc","the masterL1 blocks is invalid,idc:"+ idc ).flip().toString();
        }
        if(!veri.equals("reduce")) {
            if (!containsM) {
                return new JsonBuilder().append("code", 400)
                        .append("desc","the masterL1 memory_mb is invalid,idc:"+ idc ).flip().toString();
            }
        }


        int blocks = jb.getInt("blocks");
        if (blocks <= 0) {
            return new JsonBuilder().append("code", 400)
                    .append("desc","the masterL1 blocks is invalid,idc:" + idc ).flip().toString();
        }
        int ipLength = ipArr.length;
        if (ipLength % blocks != 0) {
            return new JsonBuilder().append("code", 400)
                    .append("desc","the size of masterL1 and blocks are inconsistent,idc:" + idc ).flip().toString();
        }
        int count = ipLength / blocks;
        int j = 0;
        for (int i = 0; i < ipArr.length; i++) {
            String ip = ipArr[i] + ":" + port;
            sb.append(ip);
            sb.append(",");
            j ++;
            if (j == count) {
                sb = sb.deleteCharAt(sb.length()-1);
                sb.append("#");
                j = 0;
            }
        }
        String result = sb.toString();
        if (result.endsWith("#")) {
            result = result.substring(0,result.length() - 1);
        }
        return result;
    }

    private String buildSlaveL1Ips (JSONArray ja, String masterIps, String masterL1Ips, String port, String slaveIdc) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < ja.size(); i++) {
            JSONObject jb = ja.getJSONObject(i);
            String id = jb.getString("id");
            if (StringUtils.isNotBlank(slaveIdc)&&id.startsWith(slaveIdc) && id.endsWith("master")) { // 备份机房的master:目前id设置规则为：idc-master
                continue;
            }
            if (id.endsWith("master")) {
                String curMasterIps = buildBaseIps(jb,port,"m");
                if (masterIps.equals(curMasterIps)) {
                    continue;
                }
                if (sb.indexOf(curMasterIps) != -1) {
                    continue;
                }
                sb.append(curMasterIps);
                sb.append("#");
            }
            if (id.endsWith("masterL1")){
                String idc = id.split("-")[0];
                String curMasterL1Ips = buildMasterL1Ips(jb,port,idc,"");
                if (curMasterL1Ips.contains("code")) {
                    return curMasterL1Ips;
                }
                if (masterL1Ips.equals(curMasterL1Ips)) {
                    continue;
                }
                if (sb.indexOf(curMasterL1Ips) != -1) {
                    continue;
                }
                sb.append(curMasterL1Ips);
                sb.append("#");
            }
        }
        String result = sb.toString();
        if (result.endsWith("#")) {
            result = result.substring(0,result.length() - 1);
        }
        return result;
    }

    private String getSlaveL1Idc(JSONArray ja) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < ja.size(); i++) {
            String id = ja.getJSONObject(i).getString("id");
            String idc = id.split("-")[0];
            if (sb.indexOf(idc) == -1) {
                sb.append(idc);
                sb.append(",");
            }
        }
        String result = sb.toString();
        if (result.endsWith(",")) {
            result = result.substring(0,result.length() - 1);
        }
        return result;
    }

    private IdcInfo buildIdc (String idc, String biz, int masterCapacity, int masterL1Capacity, int masterL1Block, int masterBandWidth, int masterL1BandWidth, String slave, String slaveL1Idc, String masterIps, String masterL1Ips, String slaveIps, String slaveL1Ips, String reuseIdc) {
        IdcInfo idcInfo = new IdcInfo();
        idcInfo.setIdc(idc);
        idcInfo.setGroup("cache.service." + biz +".pool."+idc);
        idcInfo.setMasterCapacity(masterCapacity);
        idcInfo.setMasterL1Capacity(masterL1Capacity);
        idcInfo.setMasterL1Block(masterL1Block);
        idcInfo.setMasterBandWidth(masterBandWidth);
        idcInfo.setMasterL1BandWidth(masterL1BandWidth);
        idcInfo.setSlave(slave);
        idcInfo.setSlaveL1Idc(slaveL1Idc);
        idcInfo.setMasterIps(masterIps);
        idcInfo.setMasterL1Ips(masterL1Ips);
        idcInfo.setSlaveIps(slaveIps);
        idcInfo.setSlaveL1Ips(slaveL1Ips);
        idcInfo.setReuseIdc(reuseIdc);
        return idcInfo;
    }


    private boolean isExist(JSONArray idcs) {
        for (int i = 0; i < idcs.size(); i++) {
            String idc = idcs.getJSONObject(i).getString("id");
            if (!allIdcs.contains(idc)) {
                return false;
            }
        }
        return true;
    }

    private HashMap<String,String> getWaitingOperInfo(JSONArray idcs, String port) {
        HashMap<String,String> infos = new HashMap<String, String>(17);
        for(int i=0;i<idcs.size();i++) {
            String idc = idcs.getJSONObject(i).getString("id");
            JSONObject masterL1 = idcs.getJSONObject(i).getJSONObject("masterL1");
            String masterL1Ips = buildMasterL1Ips(masterL1,port,idc,"");
            if (masterL1Ips.contains("code")) {
                return null;
            }
            infos.put(idc,masterL1Ips);
        }
        return infos;
    }

    private HashMap<String,Set<String>> getWaitingReduceInfo(JSONArray idcs) {
        HashMap<String,Set<String>> infos = new HashMap<String, Set<String>>();
        for (int i=0; i< idcs.size(); i++) {
            String idc = idcs.getJSONObject(i).getString("id");
            JSONArray groups = idcs.getJSONObject(i).getJSONArray("groups");
            Set<String> info = new HashSet<String>();
            for (int j = 0 ; j < groups.size(); j++) {
                info.add(groups.getString(j));
            }
            infos.put(idc,info);
        }
        return infos;
    }

    private String checkParamForUpdateConfig(String configStr) {
        JSONObject jsonResult = new JSONObject();
        if (StringUtils.isBlank(configStr)) {
            jsonResult.put("code",400);
            jsonResult.put("message","request body is null");
            return jsonResult.toString();
        }
        if (!configStr.contains("port")) {
            jsonResult.put("code",400);
            jsonResult.put("message","param port is null");
            return jsonResult.toString();
        }
        if (!configStr.contains("oldIps")) {
            jsonResult.put("code",400);
            jsonResult.put("message","param oldIps is null");
            return jsonResult.toString();
        }
        if (!configStr.contains("newIps")) {
            jsonResult.put("code",400);
            jsonResult.put("message","param newIps is null");
            return jsonResult.toString();
        }
        return "ok";
    }

    private String checkParamForCreateConfig(String configStr) {
        JSONObject jsonResult = new JSONObject();
        if (StringUtils.isBlank(configStr)) {
            jsonResult.put("code",400);
            jsonResult.put("message","request body is null");
            return jsonResult.toString();
        }
        if (!configStr.contains("biz")) {
            jsonResult.put("code",400);
            jsonResult.put("message","param biz is null");
            return jsonResult.toString();
        }
        if (!configStr.contains("namespace")) {
            jsonResult.put("code",400);
            jsonResult.put("message","param namespace is null");
            return jsonResult.toString();
        }
        if (!configStr.contains("idcs")) { //idc 对应body有点复杂，只先判断空
            jsonResult.put("code",400);
            jsonResult.put("message","param idcs is null");
            return jsonResult.toString();
        }
        if (!configStr.contains("timeout")) {
            jsonResult.put("code",400);
            jsonResult.put("message","param timeout is null");
            return jsonResult.toString();
        }
        return "ok";
    }

    // 校验提案
    private boolean checkInstanceExist(Map<Integer,String> resource,JSONArray ips,String port) {
        for(String resourceInfo:resource.values()) {
            JSONObject jb = JSONObject.fromObject(resourceInfo);
            String idcInfos = jb.getString("idcInfos");
            for (int i = 0; i < ips.size(); i++) {
                String ip = ips.getString(i);
                if (idcInfos.contains(ip+":"+port)){
                    return true;
                }
            }
        }
        return false;
    }

    private String getType (String group) {
        return group.split("cache.service.")[1].split(".pool")[0];
    }
    private String getIdc (String group) {
        return group.split("pool.")[1];
    }

    private boolean contains(String []arr,String element) {
        if (arr == null || arr.length == 0) {
            return false;
        }
        for (int i = 0; i < arr.length; i++) {
            if (arr[i].equals(element)) {
                return true;
            }
        }
        return false;
    }


    public String getStaticFeedConfigStr() {
        return staticFeedConfigStr;
    }

    public void setStaticFeedConfigStr(String staticFeedConfigStr) {
        this.staticFeedConfigStr = staticFeedConfigStr;
    }
}
