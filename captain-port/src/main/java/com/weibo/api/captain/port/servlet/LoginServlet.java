/**
 * Project Name:captain-port File Name:LoginServlet.java Package
 * Name:com.weibo.api.captain.port.servlet Date:2018年7月12日下午2:29:51 Copyright (c) 2018, @weibo All
 * Rights Reserved.
 * 
 */

package com.weibo.api.captain.port.servlet;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.ServletRequestUtils;

import com.weibo.api.captain.common.exception.CaptainExcepFactor;
import com.weibo.api.captain.common.exception.CaptainException;
import com.weibo.api.captain.common.util.CaptainJsonUtil;

/**
 * <pre> ClassName:LoginServlet
 * 
 *  登录校验，目前用户名密码先写死，不涉及权限管理<pre/> Date: 2018年7月12日 下午2:29:51 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Path("login")
public class LoginServlet {
    
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public String loginProcess(@Context HttpServletRequest req, @Context HttpServletResponse resp) throws IOException {
        String username = ServletRequestUtils.getStringParameter(req, "username", null);
        if (StringUtils.isBlank(username)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_MISS_ERROR, "parameter username invalid"));
        }
        String password = ServletRequestUtils.getStringParameter(req, "password", null);
        if (StringUtils.isBlank(password)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_MISS_ERROR, "parameter password invalid"));
        }
        HttpSession session = req.getSession(true);
        if (validateAdmin(username, password)) {
            session.setAttribute("username", username);
            resp.sendRedirect(req.getContextPath() + "/index.html");
        } else {
            resp.sendRedirect(req.getContextPath() + "/error.html");
        }
        return "";
    }

    private boolean validateAdmin(String username, String password) {
        return username.equals("weibo_dba") && password.equals("1d19bac6291501b58486a672034b90");
    }

}

