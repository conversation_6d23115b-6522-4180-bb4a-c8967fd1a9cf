/**
 * Project Name:captain-port File Name:ManageResource.java Package
 * Name:com.weibo.api.captain.port.servlet Date:2016年6月28日下午11:27:06 Copyright (c) 2016, @weibo All
 * Rights Reserved.
 *
 */

package com.weibo.api.captain.port.servlet;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

import cn.sina.api.commons.util.JsonBuilder;
import com.alibaba.fastjson.JSON;
import com.weibo.api.captain.assigner.stats.ConsistencyCheckTask;
import com.weibo.api.captain.common.util.ThreadLocalUtil;
import com.weibo.api.captain.common.service.StaticConfigBiz;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.weibo.api.captain.assigner.model.IdcInfo;
import com.weibo.api.captain.assigner.model.RecordInfo;
import com.weibo.api.captain.assigner.model.ResourceAccessSetting;
import com.weibo.api.captain.assigner.model.ResourceState;
import com.weibo.api.captain.assigner.service.ResourceService;
import com.weibo.api.captain.common.exception.CaptainExcepFactor;
import com.weibo.api.captain.common.exception.CaptainException;
import com.weibo.api.captain.common.model.Idc;
import com.weibo.api.captain.common.util.CaptainJsonUtil;
import com.weibo.vintage.utils.IpUtils;

import cn.sina.api.commons.util.ApiLogger;

/**
 * <pre> ClassName:ManageResource
 *
 * description here! <pre/> Date: 2016年6月28日 下午11:27:06 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Path("cluster")
public class ManageResource extends Base {
    private ResourceService resourceService;
    private StaticConfigBiz staticConfigBiz;
    private ConsistencyCheckTask consistencyCheckTask;
    private static final String BEGIN_STRING = "cache.";
    private static final String END_STRING = ".pool";
    private static final String ACTION_ADDSERVICE = "addService";
    private static final String ACTION_GETSERVICE = "getService";
    private static final String ACTION_APPLY = "apply";
    private static final String ACTION_SAVE = "save";
    private static final String ACTION_GETYAML = "getYaml";
    private static final String ACTION_ASSIGN = "assign";
    private static final String ACTION_MODIFY = "modify";
    private static final String ACTION_DEPLOY = "deploy";
    private static final String ACTION_RETURN = "return";
    private static final String ACTION_REJECT = "reject";
    private static final String ACTION_DELETE = "delete";
    private static Map<String,String> map = new HashMap<String,String>();
    
    static {
        map.put("bkdr", "ketama");
        map.put("crc32", "modula");
        map.put("one_at_a_time", "modula");
    }
    
    public void setResourceService(ResourceService resourceService) {
        this.resourceService = resourceService;
    }


    @GET
    @Path("/checkConsistency")
    @Produces(MediaType.APPLICATION_JSON)
    public String checkConsistency(@QueryParam("type")int type){
        if (Objects.equals(type, 1)) {
            return JSON.toJSONString(consistencyCheckTask.checkIdcInfoConsistency());
        }
        if (Objects.equals(type, 2)) {
            return JSON.toJSONString(consistencyCheckTask.queryAllVintageConfigDiffs());
        }
        return "invalid type";
    }

    @GET
    @Path("/formatCheckConsistency")
    @Produces(MediaType.APPLICATION_JSON)
    public String formatCheckConsistency(@QueryParam("type")int type){
        return consistencyCheckTask.formatResult(type);
    }

    @GET
    @Path("/mailConsistencyCheckResult")
    @Produces(MediaType.APPLICATION_JSON)
    public String mailConsistencyCheckResult(){
        consistencyCheckTask.notifyConsistencyResult();
        return "Success";
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public String handleCluster(
            @FormParam("action") String action,
            @FormParam("type") String type,
            @FormParam("proxyState") @DefaultValue("0") String proxyState,
            @FormParam("configAddr") String configAddr,
            @FormParam("idcList") String idcList) {
        if (StringUtils.isBlank(action)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_MISS_ERROR, "parameter (action) has no value"));
        }
        if (ACTION_ADDSERVICE.equals(action)) {
            return processAddPool(type.trim(),proxyState,configAddr,idcList);
        } else {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (action)'s value invalid,expect value is addService"));
        }
    }

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public String getService(
            @QueryParam("action") String action) {
        if (StringUtils.isBlank(action)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (action) has no value"));
        }
        if (ACTION_GETSERVICE.equals(action)) {
            return resourceService.getService();
        } else {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (action)'s value invalid,expect value is getService"));
        }
    }

    @Path("/resource")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public String handleResource(
            @FormParam("action") String action,
            @FormParam("type") String type,
            @FormParam("biz") String biz,
            @FormParam("idcInfo") String idcInfo,
            @FormParam("hitPercent") @DefaultValue("0.0") float hitPercent,
            @FormParam("readTps") @DefaultValue("0") int readTps,
            @FormParam("writeTps") @DefaultValue("0") int writeTps,
            @FormParam("hash") @DefaultValue("bkdr") String hashStrategy,
            @FormParam("distribution") @DefaultValue("ketama") String distribution,
            @FormParam("saveTag") @DefaultValue("0") int saveTag,
            @FormParam("autoEjectHosts") @DefaultValue("0") int autoEjectHosts,
            @FormParam("timeout") @DefaultValue("300") long timeout,
            @FormParam("serverRetryTimeout") @DefaultValue("10000") long serverRetryTimeout,
            @FormParam("serverFailureLimit") @DefaultValue("2") int serverFailureLimit,
            @FormParam("resourceType") @DefaultValue("0") int resourceType,
            @FormParam("partialReply") @DefaultValue("1") int partialReply,
            @FormParam("exptime") long exptime,
            @FormParam("forceWriteAll") @DefaultValue("0") int forceWriteAll,
            @FormParam("updateSlaveL1") @DefaultValue("1") int updateSlaveL1,
            @FormParam("localAffinity") @DefaultValue("0") int localAffinity,
            @FormParam("flag") @DefaultValue("0") int flag,
            @FormParam("applyUser") String applyUser,
            @FormParam("assignUser") String assignUser,
            @FormParam("deployUser") String deployUser,
            @FormParam("rejectUser") String rejectUser,
            @FormParam("id") @DefaultValue("0") int id,
            @FormParam("group") String group,
            @FormParam("configStr") String configStr) {
        try {
            ApiLogger.info("resource handle,action=" + action + ",type=" + type + ",biz=" + biz + ",idcInfo=" + idcInfo + ",applyUser=" + applyUser + ",assignUser=" + assignUser + ",deployUser=" + deployUser + ",rejectUser=" + rejectUser + ",id=" + id + ",group=" + group);
            if (!idcVerify(idcInfo)) {//idcInfo格式验证,避免有问题仍然会操作成功。
                return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (idcInfo)'s value invalid,expect json"));
            }
            if (ACTION_APPLY.equals(action)) { //资源申请,如果之前进行过保存，则需要对该提案做处理，这时候需要传提案id
                return processApply(type, biz, idcInfo, hitPercent, readTps, writeTps, hashStrategy, distribution, saveTag,
                        autoEjectHosts, timeout, serverRetryTimeout, serverFailureLimit, resourceType,
                        partialReply, exptime, forceWriteAll, updateSlaveL1, localAffinity, flag, applyUser, id);
            } else if (ACTION_GETYAML.equals(action)) {// 只生成yaml文件
                return generatorYaml(type, biz, idcInfo, hashStrategy, distribution, saveTag,
                        autoEjectHosts, timeout, serverRetryTimeout, serverFailureLimit, resourceType,
                        partialReply, exptime, forceWriteAll, updateSlaveL1, localAffinity, flag, id);
            } else if (ACTION_DEPLOY.equals(action)) {//proxy部署
                return processDeploy(deployUser, id);
            } else if (ACTION_SAVE.equals(action)) {// 保存
                return processSave(type, biz, idcInfo, hitPercent, readTps, writeTps, hashStrategy, distribution, saveTag,
                        autoEjectHosts, timeout, serverRetryTimeout, serverFailureLimit, resourceType,
                        partialReply, exptime, forceWriteAll, updateSlaveL1, localAffinity, flag, applyUser);
            } else if (ACTION_ASSIGN.equals(action)) {//更改数据库状态，注册yaml文件
                return processAssign(id, group, configStr, assignUser, idcInfo);
            } else if (ACTION_RETURN.equals(action)) {
                return processReturn(id);
            } else if (ACTION_REJECT.equals(action)) {//驳回功能，目前只涉及修改状态,是否需要加驳回处理人，及驳回原因，待定
                return processReject(id, rejectUser);
            } else if (ACTION_DELETE.equals(action)) {
                return processDelete(id);
            } else {
                return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR,
                        "parameter (action)'s value invalid,expect value range in (apply,getYaml,deploy,save,assign,return)"));
            }
        }
        catch (Exception e) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR,
                    "parameter (action)'s value invalid,expect value range in (apply,getYaml,deploy,save,assign,return)"));
        }
        finally {
                ThreadLocalUtil.clear();
        }
    }

    /**
     * 根据port查询资源列表
     * @param action
     * @param port
     * @param assigned
     * @param mode
     * @return
     */
    @Path("/resource/{port}")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public String lookupResourceByPort(
            @PathParam("port") int port,
            @QueryParam("action") String action,
            @QueryParam("assigned") @DefaultValue("true") boolean assigned,
            @QueryParam("mode") @DefaultValue("1") int mode) {
        if (StringUtils.isBlank(action)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (action) has no value"));
        }
        //如果action不是lookup,返回参数不合理
        if (!"lookup".equals(action)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (action)'s value invalid,expect value range in (lookup)"));
        }

        ApiLogger.info("lookup resource by port,port:" + port + ",assigned:" + assigned + ",mode:" + mode);
        return processLookUpByPort(port, assigned);
    }

    @Path("/resource")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public String lookupResource(
            @QueryParam("action") String action,
            @QueryParam("id") int id,
            @QueryParam("assigned") @DefaultValue("false") boolean assigned,
            @QueryParam("mode") @DefaultValue("0") int mode){
        if (StringUtils.isBlank(action)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (action) has no value"));
        }
        if (id < 0) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (id)'s value invalid,expect value is > 0"));
        }
        if (mode != 0 && mode != 1) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (mode)'s value invalid,expect value range in (0,1)"));
        }
        if ("lookup".equals(action)) {
            return processLookUp(id,assigned,mode);
        } else  {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (action)'s value invalid,expect value is lookup"));
        }
    }

    @Path("/stats")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public String lookUpClusterStats(
            @QueryParam("action") String action,
            @QueryParam("group") String group) {
        if ("lookup".equals(action)) {
            if (!StringUtils.isBlank(group)) {
                return resourceService.getAllBizStats(group);
            }
            return resourceService.getProxyStats();
        } else  {
            return CaptainJsonUtil.JSON_RS_MALFORMED_PARAM;
        }
    }

    private String processLookUpByPort(int port, boolean assigned) {
        JsonBuilder result = new JsonBuilder();
        List<JsonBuilder> list = new ArrayList<JsonBuilder>();
        if (port <= 0) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (port)'s value invalid"));
        }
        Set<Integer> ids = ConfigResource.resources.get(String.valueOf(port));
        if (CollectionUtils.isEmpty(ids)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "no resources found"));
        }

        for(Integer id : ids){
            JsonBuilder resource=resourceService.getResourceJsonBuilder(id);
            list.add(resource);
        }
        return result.appendJsonArr("result", list).flip().toString();
    }

    private String processLookUp(int id, boolean assigned,int mode) {
        if (mode == 0) {
            return resourceService.getResource(assigned);
        } else if (mode == 1) {// 根据id获取数据
            if (id != 0) {
                return resourceService.getResource(id);
            } else {
                return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_CLUSTER_ID_NOT_EXISTS,"parameter(action)'s value invalid,expect > 0"));
            }
        }
        return null;
    }

    private String processDelete(int id){
        if (id <= 0) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (id)'s value invalid"));
        }
        return resourceService.delResource(id);
    }

    private String processApply(String type, String biz,String idcInfoString,float hitPercent, int readTps, int writeTps, String hashStrategy, String distribution, int saveTag,
                                int autoEjectHosts, long timeout, long serverRetryTimeout, int serverFailureLimit, int resourceType,
                                int partialReply, long exptime,int forceWriteAll, int updateSlaveL1, int localAffinity, int flag, String applyUser,int id) {

        //不传id的情况下，说明为不保存直接申请，需要对type及biz,idcInfo等进行验证
        if (id == 0) {
            if (StringUtils.isBlank(type) || StringUtils.isBlank(biz) || StringUtils.isBlank(idcInfoString)) {
                return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_MISS_ERROR, "parameter (type or biz or idcInfo) has no value"));
            }
            type = type.trim();
            biz = biz.trim();
            if (resourceService.isExist(type, biz)) {
                return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_SERVICE_ID_EXISTS, "the resource already exists"));
            }
        }
        if (hitPercent < 0f || readTps < 0 || writeTps < 0) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (hitPercent or readTps or writeTps)'s value invalid, expect hitPercent >= 0.0, readTps >=0 ,writeTps >= 0"));
        }
        RecordInfo recordInfo = buildRecordInfo(type, biz, hitPercent, readTps,writeTps, applyUser,id) ;
        try {
            validateResourceAccessSettingValue(hashStrategy, distribution, timeout, serverRetryTimeout,
                    serverFailureLimit, resourceType, exptime,forceWriteAll, updateSlaveL1, localAffinity, flag);
        } catch (CaptainException e) {
            return CaptainJsonUtil.buildErrorJson(e);
        }
        ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(hashStrategy,distribution, biz, saveTag,autoEjectHosts,timeout,serverRetryTimeout,serverFailureLimit,resourceType,partialReply,exptime,forceWriteAll, updateSlaveL1, localAffinity, flag);
        List<IdcInfo> idcInfos = null;
        try {
            ApiLogger.warn("apply resource validateIdcInfo,idcInfoString="+idcInfoString);
            idcInfos = validateIdcInfo(type, idcInfoString, "");
        } catch (CaptainException e) {
            return CaptainJsonUtil.buildErrorJson(e);
        }
        recordInfo.setState(Integer.parseInt(ResourceState.APPLY_STATE.toString()));
        return resourceService.applyResource(recordInfo,resourceAccessSetting,idcInfos);
    }


    private String processSave(String type, String biz, String idcInfoString, float hitPercent, int readTps, int writeTps,
                               String hashStrategy, String distribution, int saveTag, int autoEjectHosts, long timeout, long serverRetryTimeout, int serverFailureLimit, int resourceType, int partialReply, long exptime,
                               int forceWriteAll, int updateSlaveL1, int localAffinity,int flag, String applyUser) {
        if (StringUtils.isBlank(type) || StringUtils.isBlank(biz)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_MISS_ERROR, "parameter (type or biz) has no value"));
        }
        type = type.trim();
        biz = biz.trim();
        if (resourceService.isExist(type, biz)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_SERVICE_ID_EXISTS, "the resource already exists"));
        }
        if (hitPercent < 0f || readTps < 0 || writeTps < 0) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (hitPercent or readTps or writeTps)'s value invalid, expect hitPercent >= 0.0, readTps >=0 ,writeTps >= 0"));
        }
        RecordInfo recordInfo = buildRecordInfo(type, biz, hitPercent, readTps, writeTps, applyUser, 0);// 提案基本信息
        try {
            validateResourceAccessSettingValue(hashStrategy, distribution, timeout,serverRetryTimeout,
                    serverFailureLimit, resourceType, exptime,forceWriteAll, updateSlaveL1, localAffinity, flag);
        } catch (CaptainException e) {
            return CaptainJsonUtil.buildErrorJson(e);
        }
        ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(hashStrategy, distribution, biz, saveTag, autoEjectHosts,
                timeout, serverRetryTimeout, serverFailureLimit, resourceType, partialReply, exptime,forceWriteAll, updateSlaveL1, localAffinity, flag);
        List<IdcInfo> idcInfos = null;
        try {
            ApiLogger.warn("apply resource validateIdcInfo,idcInfoString="+idcInfoString);
            idcInfos = validateIdcInfo(type, idcInfoString, "");
        } catch (CaptainException e) {
            return CaptainJsonUtil.buildErrorJson(e);
        }

        recordInfo.setState(Integer.parseInt(ResourceState.SAVE_STATE.toString()));
        return resourceService.applyResource(recordInfo, resourceAccessSetting, idcInfos);
    }

    private List<IdcInfo> validateIdcInfo(String type, String idcInfoString, String stage) throws CaptainException{
        List<IdcInfo> idcInfoList = null;
        if (!StringUtils.isBlank(idcInfoString)) {
            try {
                idcInfoList = buildIdcInfo(idcInfoString);
            } catch (Exception e) {
                throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (idcInfo)'s value invalid, expect json style");
            }
            List<String> currentIdcs = new ArrayList<String>();//需要判断复用机房是否在当前机房里面。
            for (IdcInfo idcInfo : idcInfoList) {
                currentIdcs.add(idcInfo.getIdc());
            }
            //复用机房，同步机房逻辑校验 如果a复用了b，任何其余机房的备份，同步，复用都不能用a
            for (IdcInfo idcInfo : idcInfoList) {
                //复用机房逻辑
                String reuseIdc = idcInfo.getReuseIdc();
                if (!StringUtils.isBlank(reuseIdc)) {
                    if (!currentIdcs.contains(reuseIdc)) {
                        throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (idcInfo)'s "+idcInfo.getIdc()+"'s reuseIdc invalid");
                    }
                    for (IdcInfo idcInfo2 : idcInfoList) {
                        if (idcInfo2.getIdc() == idcInfo.getIdc()) {
                            continue;
                        }
                        if (idcInfo.getIdc().equals(idcInfo2.getSlave())) {
                            throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (idcInfo)'s value invalid,"+ idcInfo2.getIdc()+"'s slave should not has "+idcInfo.getIdc()+",because "+idcInfo.getIdc()+" already reuse the " + reuseIdc);
                        }
                        if (idcInfo.getIdc().equals(idcInfo2.getReuseIdc())) {
                            throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (idcInfo)'s value invalid,"+ idcInfo2.getIdc()+"'s reuseIdc should not has "+idcInfo.getIdc()+",because "+idcInfo.getIdc()+" already reuse the " + reuseIdc);
                        }
                        if (!StringUtils.isBlank(idcInfo2.getSlaveL1Idc())) {
                            String slaveL1Idcs[] = idcInfo2.getSlaveL1Idc().split(",");
                            //TODO:masterl1 idc
                            for (int i=0;i<slaveL1Idcs.length;i++){
                                if (slaveL1Idcs[i].equals(idcInfo.getIdc())) {
                                    throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR,"parameter (idcInfo)'s value invalid,"+ idcInfo2.getIdc()+"'s slaveL1Idc should not have "+idcInfo.getIdc()+",because "+idcInfo.getIdc()+" already reuse the "+ reuseIdc);
                                }
                            }
                        }
                    }
                    //如果a复用了b，a传进来的其他属性，比如slave，及slavel1属性都为“”，强制做。
                    idcInfo.setSlave("-1");
                    idcInfo.setSlaveL1Idc("");
                }

                //同步机房逻辑
                String slaveL1Idc = idcInfo.getSlaveL1Idc();
                if (!StringUtils.isBlank(slaveL1Idc)) {
                    String []slaveL1IdcArray = slaveL1Idc.split(",");
                    for (int i=0;i<slaveL1IdcArray.length;i++) {
                        if (!currentIdcs.contains(slaveL1IdcArray[i])) {
                            throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (idcInfo) of slaveL1Idc "+ slaveL1IdcArray[i]+" invalid");
                        }
                    }
                }
            }
            for (IdcInfo idcInfo : idcInfoList) {
                String idc = idcInfo.getIdc();
                String group = idcInfo.getGroup();
                if (!(BEGIN_STRING +"service." + type + END_STRING + "." + idc).equals(group)) {
                    throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (idcInfo)'s group is not desirable,expect cache.service."+type+".pool."+idc);
                }
                if (!resourceService.isExistServicePoolInfo(type,idc)) {
                    throw new CaptainException(CaptainExcepFactor.E_GROUP_ID_NOT_EXISTS, "group:"+group+" is not exist");
                }
                int masterCapacity = idcInfo.getMasterCapacity();
                int masterL1Capacity = idcInfo.getMasterL1Capacity();
                int masterL1Block = idcInfo.getMasterL1Block();
                int masterBandWidth = idcInfo.getMasterBandWidth();
                int masterL1BandWidth = idcInfo.getMasterL1BandWidth();
                String slave = idcInfo.getSlave();
                String masterIps = idcInfo.getMasterIps();
                String slaveIps = idcInfo.getSlaveIps();
                String msterL1Ips = idcInfo.getMasterL1Ips();
                String slaveL1Ips = idcInfo.getSlaveL1Ips();
                if (stage.equals(ACTION_GETYAML) && StringUtils.isBlank(masterIps)) {
                    throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (idcInfo)'s masterIps has no value");
                }

                //判断idcinfo中对应的机房信息是否在所列机房中
                boolean isInIdcList = isInIdcRange(idc);
                if (!isInIdcList) {
                    ApiLogger.error("idc:"+idc+" is not in idc list");
                    throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR,"parameter (idcInfo)'s idc value invalid,expect range in (yf,tc,aliyun,aliyun_tc,bx,yz,xdl,yhg,ft,ja,xd,qxg,nfjd,sx,xg,huawei,huawei_tc,jxg,qxj,aliyun_wlcb,aliyun_zb,wq,aliyun_wq,huawei_wq)");
                }

                //判断容量分组等是否合法
                if (masterCapacity < 0 || masterL1Capacity < 0 || masterL1Block < 0 || masterBandWidth < 0 || masterL1BandWidth < 0) {
                    ApiLogger.error("masterCapacity:"+masterCapacity+" masterL1Capacity:"+masterL1Capacity+" masterL1Block:"+masterL1Block+" masterBandWidth:"+masterBandWidth+" masterL1BandWidth:"+masterL1BandWidth);
                    throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (idcInfo)'s masterCapacity or masterL1Capacity or masterL1Block or masterBandWidth or masterL1BandWidth invalid,expect > 0");
                }

                //判断ip端口是否合法
                if (!StringUtils.isBlank(masterIps)) {
                    try {
                        ApiLogger.info("validate masterIps",masterIps);
                        validateIps(masterIps);
                    } catch (CaptainException e) {
                        ApiLogger.error("validate masterIps: "+masterIps+" error",e);
                        throw e;
                    } catch (Exception e) {
                        ApiLogger.error("validate masterIps: "+masterIps+" error",e);
                        throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR,
                                "parameter (idcInfo)'s masterIps invalid, masterIps should separate by ',',example:*************:1234,*************:1234");
                    }
                }
                if (!StringUtils.isBlank(slaveIps)) {
                    try {
                        validateIps(slaveIps);
                    } catch (CaptainException e) {
                        ApiLogger.error("validate slaveIps: "+slaveIps+" error",e);
                        throw e;
                    } catch (Exception e) {
                        ApiLogger.error("validate slaveIps: "+slaveIps+" error",e);
                        throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR,
                                "parameter (idcInfo)'s slaveIps invalid, slaveIps should separate by ',',example:*************:1234,*************:1234");
                    }
                }

                if (!StringUtils.isBlank(msterL1Ips)) {
                    try {
                        String[] masterL1IpArray = msterL1Ips.split("#");
                        if (masterL1IpArray.length != masterL1Block) {
                            throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR,"parameter (idcInfo)'s masterL1Ips and masterL1Block are not consistent");
                        }
                        for (String masterL1Ip : masterL1IpArray) {
                            validateIps(masterL1Ip);
                        }
                    } catch (CaptainException e) {
                        ApiLogger.error("validate msterL1Ips: "+msterL1Ips+" error",e);
                        throw e;
                    } catch (Exception e) {
                        ApiLogger.error("validate msterL1Ips: "+msterL1Ips+" error",e);
                        throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (idcInfo)'s masterL1Ips invalid,example:*************:1234,*************:1234#*************:1234,*************:1234");
                    }
                }

                if (!StringUtils.isBlank(slaveL1Ips)) {
                    try {
                        String[] slaveL1IpArray = slaveL1Ips.split("#");
                        for (String slaveL1Ip : slaveL1IpArray) {
                            validateIps(slaveL1Ip);
                        }
                    } catch (CaptainException e) {
                        ApiLogger.error("validate slaveL1Ips: "+slaveL1Ips+" error",e);
                        throw e;
                    } catch (Exception e) {
                        ApiLogger.error("validate slaveL1Ips: "+slaveL1Ips+" error",e);
                        throw new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (idcInfo)'s slaveL1Ips invalid,example:*************:1234,*************:1234#*************:1234,*************:1234");
                    }
                }

                //校验slave是否正确,slave可能的值在enum idc中，同时还包含－1。
                isInIdcList = isInIdcRange(slave);
                if (!isInIdcList) {
                    if (slave.equals("-1")) {
                        isInIdcList = true;
                    }
                }
                if (!isInIdcList) {
                    throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR,"parameter (idcInfo)'s slave value invalid,expect range in (yf,tc,aliyun,aliyun_tc,bx,yz,xdl,yhg,ft,ja,xd,qxg,nfjd,sx,xg,-1,huawei,huawei_tc,jxg,qxj,aliyun_wlcb,aliyun_zb,wq,aliyun_wq,huawei_wq)");
                }
            }
        }
        return idcInfoList;
    }

    private boolean isInIdcRange(String idc) {
        boolean  isInIdcList = false;
        for (Idc idcValue: Idc.values()) {
            if (idc.equals(idcValue.name())) {
                isInIdcList = true;
                break;
            }
        }
        return isInIdcList;
    }

    private static void validateIps(String ips) throws CaptainException{
        ApiLogger.info("validateIps: "+ips);
        String hashValue = "";
        String distributionValue = "";
        String[] ipArray = ips.split(",");
        for (String ip : ipArray) {
            String ipAndPort[] = ip.split(":");
            String ipValue = ipAndPort[0];
            String specialValue[] = ipValue.split(" ");
            if (specialValue[0].equals("hash")) {
                if (specialValue.length < 2 || !map.containsKey(specialValue[1])) {
                    throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (idcInfo)'s "+ ipValue +" hash invalid");
                }
                hashValue = specialValue[1];
                continue;
            } else if (specialValue[0].equals("distribution")) {
                if (specialValue.length < 2 || (!specialValue[1].equals("modula") && !specialValue[1].equals("ketama"))) {
                    throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (idcInfo)'s "+ ipValue +" distribution invalid");
                }
                distributionValue = specialValue[1];
                continue;
            } else {
                if (ipAndPort.length < 2 || !IpUtils.isValidIP(ipValue) || !IpUtils.isValidPort(Integer.parseInt(ipAndPort[1]))) {
                    ApiLogger.error("parameter (idcInfo)'s " + ips + " ip or port invalid");
                    throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR,
                            "parameter (idcInfo)'s " + ips + " ip or port invalid");
                }
            }
        }
        if (!hashValue.equals("") && !distributionValue.equals(map.get(hashValue))) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (hash and distribution)'s value inconsistence,expect (hash=crc32,distribution=modula or hash=bkdr,distribution=ketama or hash=one_at_a_time,distribution=modula)");
        }
    }

    private void validateResourceAccessSettingValue(String hashStrategy, String distribution, long timeout, long serverRetryTimeout, int serverFailureLimit, int resourceType, long exptime, long forceWriteAll, long updateSlaveL1, long localAffinity, int flag) throws CaptainException{
        //hash跟distribution是否一致
        Set<String> hashs = map.keySet();
        if (!hashs.contains(hashStrategy)) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (hash)'s value invalid,expect hash value range in (\"bkdr\",\"crc32\",\"one_at_a_time\")");
        }
        if (!distribution.equals(map.get(hashStrategy))) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (hash and distribution)'s value inconsistence,expect (hash=crc32,distribution=modula or hash=bkdr,distribution=ketama or hash=one_at_a_time,distribution=modula)"); 
        }
        if (timeout < 0 || serverRetryTimeout < 0 || serverFailureLimit < 0 || exptime < 0 || forceWriteAll < 0 || updateSlaveL1 < 0 || localAffinity < 0 || flag < 0) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (timeout or serverRetryTimeout or serverFailureLimit or exptime)'s value invalid,expect all this value > 0");
        }
        if (resourceType <0 || resourceType >1) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (resourceType)'s value invalid,expect resourceType value range in (0,1)");
        }
        if (forceWriteAll <0 || forceWriteAll >1) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (forceWriteAll)'s value invalid,expect forceWriteAll value range in (0,1)");
        }
        if (updateSlaveL1 <0 || updateSlaveL1 >1) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (updateSlaveL1)'s value invalid,expect updateSlaveL1 value range in (0,1)");
        }
        if (localAffinity <0 || localAffinity >1) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (localAffinity)'s value invalid,expect localAffinity value range in (0,1)");
        }
        if (flag <0) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (flag)'s value invalid");
        }
        if (exptime <0) {
            throw new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (exptime)'s value invalid,expect exptime value range >= 0");
        }
    }

    private String processDeploy(String deployUser,int id) {
        if (id == 0) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (id) has no value,expect > 0"));
        }
        return resourceService.deployResource(deployUser,id);
    }

    private String processAssign(int id, String group, String configStr, String assignUser,String idcInfo) {
        if (id == 0 || StringUtils.isBlank(group) || StringUtils.isBlank(configStr)) {
            return CaptainJsonUtil.buildErrorJson(
                    new CaptainException(CaptainExcepFactor.E_PARAM_MISS_ERROR, "parameter (id or group or configStr) has no value"));
        }
        Pattern p = Pattern.compile(BEGIN_STRING + "(service\\.|service2\\.0\\.)" + "(\\w+.*)" + END_STRING + (".\\w*"));
        Matcher m=p.matcher(group);
        if (!m.matches()) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (group)'s value style invalid"));
        }
        return resourceService.assignResource(id, group, configStr, assignUser, idcInfo);
    }

    private String generatorYaml(String type, String biz, String idcInfo, String hashStrategy,
                                 String distribution, int saveTag, int autoEjectHosts, long timeout,
                                 long serverRetryTimeout, int serverFailureLimit, int resourceType, int partialReply, long exptime, int forceWriteAll, int updateSlaveL1, int localAffinity, int flag,int id) {
        //生成yaml文件时，对应信息已经申请，需传提案id
        if (id == 0) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (id)'s value invalid, expect > 0"));
        }
        if (StringUtils.isBlank(biz)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (biz)'s has no value"));
        }
        try {
            validateResourceAccessSettingValue(hashStrategy, distribution, timeout,serverRetryTimeout,
                    serverFailureLimit, resourceType, exptime,forceWriteAll,updateSlaveL1,localAffinity, flag);
        } catch (CaptainException e) {
            return CaptainJsonUtil.buildErrorJson(e);
        }
        ResourceAccessSetting resourceAccessSetting = buildResourceAccessSetting(hashStrategy, distribution, biz, saveTag, autoEjectHosts,
                timeout,serverRetryTimeout, serverFailureLimit, resourceType, partialReply, exptime,forceWriteAll,updateSlaveL1, localAffinity, flag);
        List<IdcInfo> idcInfos = null;
        try {
            ApiLogger.info("validateIdcInfo generatorYaml idcInfo:"+idcInfo);
            idcInfos = validateIdcInfo(type, idcInfo, ACTION_GETYAML);
        } catch (CaptainException e) {
            return CaptainJsonUtil.buildErrorJson(e);
        }
        return resourceService.generatorYaml(id, resourceAccessSetting, idcInfos);
    }

    private boolean idcVerify(String idcInfo) {
        try {
            GsonBuilder gb = new GsonBuilder();
            Gson gson = gb.create();
            gson.fromJson(idcInfo, new TypeToken<List<IdcInfo>>() {
            }.getType());
            return true;
        } catch (Exception e) {
            ApiLogger.error("idcInfo verify error,idcInfo:"+idcInfo);
        }
        return false;
    }

    private String processReturn (int id) {
        if (id == 0) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_MISS_ERROR, "parameter (id)'s has no value"));
        }
        return resourceService.returnResource(id);
    }

    //为快速支持接入shanks，暂时先改下访问权限
    public String processAddPool (String type,String proxyState, String configAddr,String idcList) {
        if (StringUtils.isBlank(type) || StringUtils.isBlank(configAddr) || StringUtils.isBlank(idcList)) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_MISS_ERROR, "parameter (type or configAddr or idcList) has no value"));
        }
        int proxyStateValue = 0;
        try {
            proxyStateValue = Integer.parseInt(proxyState) ;
            if (proxyStateValue < 0 || proxyStateValue > 2) {
                return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR,"parameter (proxyState)'s value invalid,expect value in (0,1,2)"));
            }
        } catch (NumberFormatException e) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR,"parameter (proxyState)'s value invalid, expect number"));
        }
        if (!configAddr.startsWith("http://")) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (configAddr)'s value invalid,expect start with http://"));
        }
        // 是否在所给机房范围中。
        String idcArray[] = idcList.split(",");
        for (int i = 0; i < idcArray.length; i++) {
            String idc = idcArray[i];
            boolean isInIdcRange = isInIdcRange(idc);
            if (!isInIdcRange) {
                return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_INVALID_ERROR, "parameter (idcList)'s"+idc +" value style invalid"));
            }
        }
        return resourceService.addService(type,proxyStateValue,configAddr,idcArray);
    }

    private String processReject(int id,String rejectUser) {
        if (id <= 0) {
            return CaptainJsonUtil.buildErrorJson(new CaptainException(CaptainExcepFactor.E_PARAM_ERROR, "parameter (id)'s value invalid"));
        }
        return resourceService.rejectService(id,rejectUser);
    }


    public ConsistencyCheckTask getConsistencyCheckTask() {
        return consistencyCheckTask;
    }

    public void setConsistencyCheckTask(ConsistencyCheckTask consistencyCheckTask) {
        this.consistencyCheckTask = consistencyCheckTask;
    }
}

