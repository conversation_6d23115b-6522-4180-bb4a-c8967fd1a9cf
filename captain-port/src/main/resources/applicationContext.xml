<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans  
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd  
           http://www.springframework.org/schema/context  
           http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<context:property-placeholder location="classpath:captain.properties"/>
    <import resource="classpath:captain-assigner.xml"/>
    <bean id="manageResource" class="com.weibo.api.captain.port.servlet.ManageResource">
		<property name="resourceService" ref="resourceService"></property>
		<property name="consistencyCheckTask" ref="consistencyCheckTask"></property>
	</bean>
	<bean id="loginServlet" class="com.weibo.api.captain.port.servlet.LoginServlet"></bean>
	<bean id="configResource" class="com.weibo.api.captain.port.servlet.ConfigResource" init-method="init">
		<property name="resourceService" ref="resourceService"></property>
		<property name="staticFeedConfigStr" value="${static.feed.config.str}"></property>
	</bean>

</beans>
