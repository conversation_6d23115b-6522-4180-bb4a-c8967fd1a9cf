log4j.rootLogger=info
log4j.logger.com=warn,warnfile
log4j.logger.org=warn,warnfile
log4j.logger.net=warn,warnfile 
log4j.logger.monitor=warn,warnfile
log4j.logger.httpclient=info,httpfile
log4j.logger.cn.vika.memcached=error,errorfile
log4j.logger.monitor_stat=info,monitor_stat
log4j.logger.api=info,apifile
log4j.logger.info=info,infofile
log4j.logger.warn=warn,warnfile
log4j.logger.error=info,errorfile
log4j.logger.debug_stat=debug,debug_stat
log4j.logger.access=debug,access

log4j.logger.trace=debug,tracefile
log4j.logger.profile=info,profile
 
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss SSS} [%p] %m%n

#for user stat
log4j.appender.monitor_stat=org.apache.log4j.DailyRollingFileAppender
log4j.appender.monitor_stat.file=../logs/monitor_stat.log
log4j.appender.monitor_stat.DatePattern=yyyyMMddHH'.log'
log4j.appender.monitor_stat.layout=org.apache.log4j.PatternLayout
log4j.appender.monitor_stat.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} %m%n

log4j.appender.apifile=org.apache.log4j.DailyRollingFileAppender 
log4j.appender.apifile.file=../logs/api.log
log4j.appender.apifile.DatePattern='.'yyyyMMdd-HH
log4j.appender.apifile.layout=org.apache.log4j.PatternLayout
log4j.appender.apifile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%p] %m%n

log4j.appender.infofile=org.apache.log4j.DailyRollingFileAppender 
log4j.appender.infofile.file=../logs/info.log
log4j.appender.infofile.DatePattern='.'yyyyMMdd-HH
log4j.appender.infofile.layout=org.apache.log4j.PatternLayout
log4j.appender.infofile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%p] %m%n

log4j.appender.warnfile=org.apache.log4j.DailyRollingFileAppender 
log4j.appender.warnfile.file=../logs/warn.log
log4j.appender.warnfile.DatePattern='.'yyyyMMdd
log4j.appender.warnfile.layout=org.apache.log4j.PatternLayout
log4j.appender.warnfile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%p] %m%n

log4j.appender.errorfile=org.apache.log4j.DailyRollingFileAppender 
log4j.appender.errorfile.file=../logs/error.log
log4j.appender.errorfile.DatePattern='.'yyyyMMdd
log4j.appender.errorfile.layout=org.apache.log4j.PatternLayout
log4j.appender.errorfile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%p] %m%n

log4j.appender.httpfile=org.apache.log4j.DailyRollingFileAppender 
log4j.appender.httpfile.file=../logs/http_client.log
log4j.appender.httpfile.DatePattern='.'yyyyMMdd
log4j.appender.httpfile.layout=org.apache.log4j.PatternLayout
log4j.appender.httpfile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%p] %m%n

log4j.appender.profile=org.apache.log4j.DailyRollingFileAppender 
log4j.appender.profile.file=../logs/profile.log
log4j.appender.profile.DatePattern='.'yyyyMMdd-HH
log4j.appender.profile.layout=org.apache.log4j.PatternLayout
log4j.appender.profile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} %m%n

log4j.appender.debug_stat=org.apache.log4j.DailyRollingFileAppender 
log4j.appender.debug_stat.file=../logs/debug_stat.log
log4j.appender.debug_stat.DatePattern='.'yyyyMMdd
log4j.appender.debug_stat.layout=org.apache.log4j.PatternLayout
log4j.appender.debug_stat.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%p] %m%n

log4j.appender.access=org.apache.log4j.DailyRollingFileAppender 
log4j.appender.access.file=../logs/access.log
log4j.appender.access.DatePattern='.'yyyyMMdd-HH
log4j.appender.access.layout=org.apache.log4j.PatternLayout
log4j.appender.access.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%p] %m%n

log4j.appender.tracefile=org.apache.log4j.DailyRollingFileAppender 
log4j.appender.tracefile.file=../logs/trace.log
log4j.appender.tracefile.DatePattern='.'yyyyMMdd-HH
log4j.appender.tracefile.layout=org.apache.log4j.PatternLayout
log4j.appender.tracefile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%p] %m%n
