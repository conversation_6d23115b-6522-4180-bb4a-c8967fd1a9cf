<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@ page session="false" %>
<%@page import="com.weibo.platform.commons.conf.*"%>
<%@page import="org.apache.commons.lang.StringUtils"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
   "http://www.w3.org/TR/html4/loose.dtd">
<%
//response.setStatus(503);
%>


<%
        ConfigLoader loader;
        try{
                        loader = ConfigLoaderFactory.getConfigLoader();
                        String serviceId=loader.getProperty("service.module.group");
                        String requetServiceId = request.getParameter("groupid");
                        if(checkGroup(requetServiceId,serviceId)){
                                response.setStatus(200);
                        }
                        else
                        {
                                response.setStatus(503);
                        }
        }
        catch(Exception ex){
                response.setStatus(503);
        }
%>
<%! private  boolean checkGroup(String requetServiceId, String serviceId) {
        if (StringUtils.isNotBlank(requetServiceId)) {
                return requetServiceId.equalsIgnoreCase(serviceId);
        }
        else {//healthy detect from varnish or nginx
                return true;
        }
}
%>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Weibo API</title>
    </head>
    <body>
<script type="text/javascript">
<!--
window.location = "http://open.weibo.com/"
//-->
</script>
    </body>
</html>
