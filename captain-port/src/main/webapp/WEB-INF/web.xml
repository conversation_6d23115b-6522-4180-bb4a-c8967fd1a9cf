<?xml version="1.0" encoding="UTF-8"?>
<web-app version="2.4" xmlns="http://java.sun.com/xml/ns/j2ee"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee 
	http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">
  <display-name>captain</display-name>
  <!-- log4j -->
	<context-param>  
    	<param-name>log4jConfigLocation</param-name>  
    	<param-value>classpath:log4j.properties</param-value>  
	</context-param>  
	
	<listener>  
        <listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>  
    </listener>  
    
	<!--Spring配置文件的路径,可使用通配符,多个路径用逗号分隔 -->
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath:applicationContext.xml;
		</param-value>
	</context-param>
	
	<!--Spring的ApplicationContext 载入 -->
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener </listener-class>
	</listener>
	
	<!--Character Encoding filter(字符集拦截转换) -->
	<filter>
		<filter-name>encodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter </filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>encodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	
	<filter>
		<filter-name>captainSecurity</filter-name>
		<filter-class>com.weibo.api.captain.port.filter.CaptainAdminFilter</filter-class>
		<init-param>
	    	<param-name>excludedPages</param-name>
	    	<param-value>/login.html,/error.html</param-value>
        </init-param>
	</filter>

	<filter-mapping>
		<filter-name>captainSecurity</filter-name>
		<url-pattern>*.html</url-pattern>
	</filter-mapping>

	<!-- 定义Jersey的拦截器 -->
	<servlet>
		<servlet-name>JerseyServlet</servlet-name>
		<servlet-class>
			com.sun.jersey.spi.spring.container.servlet.SpringServlet
		</servlet-class>
		<init-param>
			<param-name>com.sun.jersey.config.property.packages</param-name>
			<!-- 系统启动时扫描的包的路径 -->
			<param-value>com.weibo.api.capain.port.servlet</param-value>
		</init-param>
	</servlet>
	<servlet-mapping>
		<servlet-name>JerseyServlet</servlet-name>
		<url-pattern>/1/*</url-pattern>
	</servlet-mapping>
</web-app>
